"""Add ProcessedDocument table

Revision ID: 72d33cb1c347
Revises: 71e3b3e705c9
Create Date: 2025-07-10 14:43:57.772624

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '72d33cb1c347'
down_revision: Union[str, Sequence[str], None] = '71e3b3e705c9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('processed_documents',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('filename', sa.String(length=255), nullable=False),
    sa.Column('markdown_content', sa.Text(), nullable=False),
    sa.Column('charts_data', sa.J<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_processed_documents_id'), 'processed_documents', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_processed_documents_id'), table_name='processed_documents')
    op.drop_table('processed_documents')
    # ### end Alembic commands ###
