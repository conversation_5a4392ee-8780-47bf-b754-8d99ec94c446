# 嵌套饼图组件 (NestedPieChart)

基于 D3.js 的嵌套饼图 React 组件，用于展示分层数据结构。

## 功能特性

- 🎯 **双层结构**: 外层显示主要分类，内层显示子分类
- 🎨 **自定义颜色**: 支持为每个数据项设置颜色
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🖱️ **交互效果**: 鼠标悬停时的视觉反馈
- 📊 **智能标签**: 自动计算标签位置
- 🔗 **连接线**: 显示内外层数据的关联关系
- 📋 **图例**: 自动生成颜色图例

## 使用方法

### 基本用法

```tsx
import NestedPieChart from './components/ui/nested-pie-chart';

const data = {
  outer: [
    { name: '省内游客', value: 74.9, color: '#6ba3d6' },
    { name: '省外游客', value: 25.1, color: '#ff9f43' }
  ],
  inner: [
    { name: '县内游', value: 46.7, color: '#f8b4cb', parent: '省内游客' },
    { name: '跨县游', value: 23.1, color: '#a8c8ec', parent: '省内游客' },
    { name: '跨市游', value: 30.2, color: '#90ee90', parent: '省内游客' }
  ]
};

<NestedPieChart data={data} />
```

### 自定义尺寸

```tsx
<NestedPieChart 
  data={data}
  width={800}
  height={600}
  className="my-custom-class"
/>
```

## Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `data` | `NestedPieData` | 必需 | 图表数据 |
| `width` | `number` | 700 | SVG 宽度 |
| `height` | `number` | 500 | SVG 高度 |
| `className` | `string` | "" | 自定义 CSS 类名 |

## 数据格式

```typescript
interface NestedPieData {
  outer: Array<{
    name: string;      // 分类名称
    value: number;     // 数值
    color: string;     // 颜色 (hex 格式)
  }>;
  inner: Array<{
    name: string;      // 子分类名称
    value: number;     // 数值
    color: string;     // 颜色 (hex 格式)
    parent: string;    // 对应的外层分类名称
  }>;
}
```

## 样式定制

组件使用 Tailwind CSS 类，可以通过 `className` 属性添加自定义样式：

```tsx
<NestedPieChart 
  data={data}
  className="shadow-2xl border-2 border-gray-300"
/>
```

## 注意事项

1. 确保内层数据的 `parent` 字段与外层数据的 `name` 字段匹配
2. 数值建议使用百分比形式，便于用户理解
3. 颜色建议使用对比度较高的色彩，确保可读性
4. 在移动设备上建议使用较小的尺寸以适应屏幕

## 依赖项

- React 18+
- D3.js 7+
- Tailwind CSS
- TypeScript (可选但推荐)

## 示例场景

- 用户来源分析
- 销售数据分布
- 市场份额展示
- 地理位置统计
- 产品类别分析 