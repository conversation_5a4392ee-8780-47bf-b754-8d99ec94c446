import { ColorScheme, ColorSchemeConfig } from '../types/chartTypes';

// 预定义颜色方案
export const COLOR_SCHEMES: Record<ColorScheme, ColorSchemeConfig> = {
  business: {
    primary: ['#4A90E2', '#7ED321', '#F5A623', '#D0021B', '#9013FE', '#50E3C2', '#B8E986', '#F8E71C'],
    secondary: ['#7FB3D3', '#B7E45C', '#F7C157', '#DA4A4A', '#A855F7', '#7DD3FC', '#D4E157', '#FDD835'],
    accent: '#4A90E2',
    background: '#FFFFFF',
    text: '#2C3E50'
  },
  medical: {
    primary: ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6A994E', '#8E44AD', '#E74C3C', '#F39C12'],
    secondary: ['#5DADE2', '#C39BD3', '#F7DC6F', '#F1948A', '#A9DFBF', '#BB8FCE', '#F1948A', '#F8C471'],
    accent: '#2E86AB',
    background: '#F8F9FA',
    text: '#34495E'
  },
  scientific: {
    primary: ['#003f5c', '#58508d', '#bc5090', '#ff6361', '#ffa600', '#488f31', '#de425b', '#69b3a2'],
    secondary: ['#2c5f7c', '#7a70ad', '#d470b0', '#ff8381', '#ffb820', '#68af51', '#fe627b', '#89d3c2'],
    accent: '#003f5c',
    background: '#FAFAFA',
    text: '#1A1A1A'
  },
  custom: {
    primary: ['#6366F1', '#8B5CF6', '#EC4899', '#EF4444', '#F59E0B', '#10B981', '#06B6D4', '#84CC16'],
    secondary: ['#8B8CF6', '#A78BFA', '#F472B6', '#F87171', '#FBBF24', '#34D399', '#22D3EE', '#A3E635'],
    accent: '#6366F1',
    background: '#FFFFFF',
    text: '#111827'
  }
};

// 获取颜色方案
export function getColorScheme(scheme: ColorScheme): ColorSchemeConfig {
  return COLOR_SCHEMES[scheme];
}

// 获取主色调
export function getPrimaryColors(scheme: ColorScheme, count?: number): string[] {
  const colors = COLOR_SCHEMES[scheme]?.primary || [];
  return count ? colors.slice(0, count) : colors;
}

// 获取单个颜色
export function getColor(scheme: ColorScheme, index: number): string {
  const colors = COLOR_SCHEMES[scheme]?.primary || [];
  return colors[index % colors.length] || '#4A90E2';
}

// 生成渐变色
export function generateGradient(
  startColor: string,
  endColor: string,
  steps: number = 10
): string[] {
  const colors: string[] = [];
  
  // 简单的线性插值生成渐变
  for (let i = 0; i < steps; i++) {
    const ratio = i / (steps - 1);
    const color = interpolateColor(startColor, endColor, ratio);
    colors.push(color);
  }
  
  return colors;
}

// 颜色插值函数
function interpolateColor(color1: string, color2: string, ratio: number): string {
  const hex1 = color1.replace('#', '');
  const hex2 = color2.replace('#', '');
  
  const r1 = parseInt(hex1.substring(0, 2), 16);
  const g1 = parseInt(hex1.substring(2, 4), 16);
  const b1 = parseInt(hex1.substring(4, 6), 16);
  
  const r2 = parseInt(hex2.substring(0, 2), 16);
  const g2 = parseInt(hex2.substring(2, 4), 16);
  const b2 = parseInt(hex2.substring(4, 6), 16);
  
  const r = Math.round(r1 + (r2 - r1) * ratio);
  const g = Math.round(g1 + (g2 - g1) * ratio);
  const b = Math.round(b1 + (b2 - b1) * ratio);
  
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

// 获取对比色
export function getContrastColor(backgroundColor: string): string {
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  // 计算亮度
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  
  return brightness > 128 ? '#000000' : '#FFFFFF';
}

// 颜色透明度调整
export function adjustOpacity(color: string, opacity: number): string {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

// 默认配置
export const DEFAULT_CHART_CONFIG = {
  colorScheme: 'business' as ColorScheme,
  margin: {
    top: 60,
    right: 80,
    bottom: 60,
    left: 80
  },
  responsive: true,
  maintainAspectRatio: false,
  backgroundColor: '#FFFFFF',
  fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
  fontSize: 12,
  textColor: '#374151'
}; 