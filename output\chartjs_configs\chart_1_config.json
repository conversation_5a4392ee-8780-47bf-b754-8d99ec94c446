{"type": "pie", "data": {"labels": ["省外游客", "县内游", "跨县游", "跨市游"], "datasets": [{"data": [0.238901941396332, 0.505835329297856, 0.218542452105061, 0.275622218597083], "backgroundColor": ["#2E86AB", "#A23B72", "#F18F01", "#C73E1D"], "borderColor": "#ffffff", "borderWidth": 2, "hoverOffset": 4}]}, "options": {"responsive": true, "maintainAspectRatio": false, "plugins": {"title": {"display": true, "text": "图表 chart_1", "font": {"size": 16, "weight": "bold"}, "padding": {"top": 10, "bottom": 30}}, "legend": {"display": true, "position": "right", "labels": {"usePointStyle": true, "font": {"size": 12}, "padding": 15}}, "tooltip": {"callbacks": {"label": "function(context) { return context.label + ': ' + context.formattedValue + ' (' + Math.round(context.parsed * 100 / context.dataset.data.reduce((a, b) => a + b, 0)) + '%)'; }"}}}, "layout": {"padding": 20}}}