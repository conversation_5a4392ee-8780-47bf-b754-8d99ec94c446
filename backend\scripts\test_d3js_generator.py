#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试D3JS生成器，使用真实的图表XML数据
"""

import os
import sys
import json
import asyncio
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent))

from services.d3js_generator import D3JSGenerator
from services.chart_service import ChartService

async def test_d3js_generator():
    """测试D3JS生成器"""
    print("🧪 测试D3JS生成器")
    print("=" * 60)
    
    # 查找XML文件
    xml_dir = current_dir.parent.parent / "output" / "images"
    xml_files = []
    
    for subdir in xml_dir.iterdir():
        if subdir.is_dir():
            for xml_file in subdir.glob("*.xml"):
                xml_files.append(xml_file)
    
    if not xml_files:
        print("❌ 未找到XML文件")
        return
    
    print(f"📁 找到 {len(xml_files)} 个XML文件")
    
    # 创建生成器
    generator = D3JSGenerator()
    chart_service = ChartService()
    
    # 测试每个XML文件
    for xml_file in xml_files[:3]:  # 只测试前3个
        print(f"\n📊 测试文件: {xml_file.name}")
        print("-" * 40)
        
        try:
            # 读取XML内容
            with open(xml_file, 'r', encoding='utf-8') as f:
                xml_content = f.read()
            
            print(f"📄 XML长度: {len(xml_content)} 字符")
            
            # 方法1：直接使用生成器
            print("🔧 方法1: 直接使用D3JS生成器")
            config1 = generator.generate_d3_config(xml_content, f"测试图表 - {xml_file.stem}")
            print(f"✅ 类型: {config1.get('type', 'unknown')}")
            print(f"📝 标题: {config1.get('title', 'N/A')}")
            if config1.get('type') == 'error':
                print(f"❌ 错误: {config1.get('error', 'N/A')}")
            else:
                print(f"📊 数据集数量: {len(config1.get('datasets', []))}")
                print(f"🏷️  分类数量: {len(config1.get('categories', []))}")
            
            # 方法2：使用图表服务
            print("\n🔧 方法2: 使用图表服务")
            result2 = await chart_service.process_chart_xml_d3(xml_content, f"服务测试 - {xml_file.stem}")
            config2 = result2.get('final_config', {})
            print(f"✅ 成功: {result2.get('success', False)}")
            print(f"✅ 类型: {config2.get('type', 'unknown')}")
            print(f"📝 标题: {config2.get('title', 'N/A')}")
            if config2.get('type') == 'error':
                print(f"❌ 错误: {config2.get('error', 'N/A')}")
                if result2.get('errors'):
                    print(f"❌ 服务错误: {'; '.join(result2['errors'])}")
            
            # 保存配置文件（用于调试）
            output_dir = current_dir.parent / "temp" / "d3_test_configs"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            config_file = output_dir / f"{xml_file.stem}_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config2, f, indent=2, ensure_ascii=False)
            print(f"💾 配置已保存: {config_file}")
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 测试完成！")

if __name__ == "__main__":
    asyncio.run(test_d3js_generator()) 