import { apiClient } from './client';

export interface ChatMessage {
  message: string;
  context?: any;
}

export interface ChatResponse {
  message: string;
  response?: string;
  context?: any;
  action?: any;
  suggestion?: any;
}

export interface GenerateReportRequest {
  prompt: string;
  data?: any;
}

export interface GeneratePuckBlocksRequest {
  prompt: string;
  data?: any;
}

// AI聊天相关API
export const aiChatApi = {
  // 发送消息
  async sendMessage(message: string, context?: any): Promise<ChatResponse> {
    const response = await apiClient.post<ChatResponse>('/api/ai-chat', {
      message,
      context,
    });
    return response.data;
  },

  // 生成报告内容
  async generateReportContent(prompt: string, data?: any): Promise<any> {
    const response = await apiClient.post('/api/ai-chat/generate-report', {
      prompt,
      data,
    });
    return response.data;
  },

  // 生成Puck块结构
  async generatePuckBlocks(prompt: string, data?: any): Promise<any> {
    const response = await apiClient.post('/api/ai-chat/generate-puck-blocks', {
      prompt,
      data,
    });
    return response.data;
  },
}; 