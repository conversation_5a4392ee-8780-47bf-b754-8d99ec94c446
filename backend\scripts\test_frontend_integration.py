#!/usr/bin/env python3
"""
端到端测试脚本
测试前后端集成和完整功能流程
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.chart_service import ChartService


async def test_complete_integration():
    """完整集成测试"""
    print("🚀 端到端测试开始")
    print("=" * 60)
    
    service = ChartService()
    
    # 测试1: 检查现有图表配置
    print("\n📊 测试1: 检查现有图表配置")
    try:
        config_dir = Path("../output/d3_configs")
        if config_dir.exists():
            config_files = list(config_dir.glob("chart_*_config.json"))
            print(f"✅ 发现 {len(config_files)} 个图表配置文件")
            
            for config_file in config_files:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"   - {config_file.name}: {config.get('type', 'unknown')} 类型")
        else:
            print("⚠️ 配置目录不存在，将在后续测试中创建")
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
    
    # 测试2: 处理测试Word文档
    print("\n📄 测试2: 处理Word文档")
    test_word_file = "../tmp/2025年1-3月浙江省旅游业数据分析报告-改.docx"
    
    if Path(test_word_file).exists():
        try:
            print(f"开始处理: {test_word_file}")
            result = await service.process_word_document_complete_d3(test_word_file)
            
            if result["success"]:
                print(f"✅ 处理成功!")
                print(f"   - 图表数量: {len(result['charts'])}")
                print(f"   - Markdown长度: {len(result['markdown_content'])} 字符")
                
                # 显示生成的图表信息
                for chart in result['charts']:
                    print(f"   - {chart['chartId']}: {chart['title']} ({chart['config']['type']})")
                    
                # 保存结果
                output_dir = Path("../output/d3_configs")
                output_dir.mkdir(parents=True, exist_ok=True)
                
                for chart in result['charts']:
                    config_file = output_dir / f"{chart['chartId']}_config.json"
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(chart['config'], f, indent=2, ensure_ascii=False)
                
                print(f"✅ 配置已保存到: {output_dir}")
            else:
                print(f"❌ 处理失败: {result.get('errors', [])}")
                
        except Exception as e:
            print(f"❌ 文档处理出错: {e}")
    else:
        print(f"⚠️ 测试文档不存在: {test_word_file}")
    
    # 测试3: 验证图表配置文件
    print("\n🔧 测试3: 验证图表配置")
    try:
        config_dir = Path("../output/d3_configs")
        if config_dir.exists() and list(config_dir.glob("chart_*_config.json")):
            # 生成示例Markdown内容
            markdown_example = """
## 浙江省旅游业数据分析报告

### 图1 2025年1-3月浙江省全域游客客源结构

> **图表类型**: simple-pie  
> **图表ID**: `chart_1`

<!-- D3.js图表占位符 -->
```d3js
{
  "chartId": "chart_1",
  "title": "图1 2025年1-3月浙江省全域游客客源结构",
  "description": "省内游客市场份额占比超七成",
  "configFile": "/api/charts/chart_1_config.json"
}
```

### 图2 各市旅游对比分析

> **图表类型**: combo  
> **图表ID**: `chart_2`

<!-- D3.js图表占位符 -->
```d3js
{
  "chartId": "chart_2",
  "title": "图2 各市旅游对比分析",
  "description": "各地市旅游收入和游客数量对比",
  "configFile": "/api/charts/chart_2_config.json"
}
```
"""
            
            # 保存示例文件
            example_file = "../output/frontend_markdown_example.md"
            with open(example_file, 'w', encoding='utf-8') as f:
                f.write(markdown_example.strip())
            
            print(f"✅ 前端Markdown示例已生成: {example_file}")
            print("\n📋 前端将解析以下格式的图表占位符:")
            print("```d3js")
            print("{\n  \"chartId\": \"chart_1\",")
            print("  \"title\": \"图表标题\",")
            print("  \"description\": \"图表描述\",")
            print("  \"configFile\": \"/api/charts/chart_1_config.json\"")
            print("}")
            print("```")
            
    except Exception as e:
        print(f"❌ Markdown示例生成失败: {e}")
    
    # 总结报告
    print("\n" + "=" * 60)
    print("🎉 端到端测试完成!")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(test_complete_integration()) 