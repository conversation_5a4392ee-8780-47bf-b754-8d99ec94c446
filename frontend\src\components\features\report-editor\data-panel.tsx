'use client'

import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { 
  Database, 
  Search, 
  Play, 
  Download, 
  RefreshCw, 
  Table, 
  BarChart3,
  Plus,
  Eye,
  Code,
  Loader2
} from "lucide-react"

interface DataPanelProps {
  onDataSelect?: (data: any) => void
  className?: string
}

interface LocalDataSource {
  id: string
  name: string
  type: 'table' | 'view' | 'query'
  description: string
  columns: Array<{
    name: string
    type: string
    nullable?: boolean
  }>
  rowCount?: number
}

export default function DataPanel({ onDataSelect, className }: DataPanelProps) {
  const [selectedSource, setSelectedSource] = useState<LocalDataSource | null>(null)
  const [query, setQuery] = useState('')
  const [queryResults, setQueryResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 示例数据源
  const sampleDataSources: LocalDataSource[] = [
    {
      id: '1',
      name: '销售数据',
      type: 'table',
      description: '产品销售数据表',
      columns: [
        { name: 'product_name', type: 'string' },
        { name: 'sales_amount', type: 'number' },
        { name: 'sale_date', type: 'date' }
      ],
      rowCount: 1000
    },
    {
      id: '2',
      name: '用户数据',
      type: 'table',
      description: '用户信息表',
      columns: [
        { name: 'user_id', type: 'string' },
        { name: 'user_name', type: 'string' },
        { name: 'email', type: 'string' },
        { name: 'created_at', type: 'date' }
      ],
      rowCount: 500
    }
  ]

  const handleQueryExecute = async () => {
    if (!query.trim()) return

    setIsLoading(true)
    setError(null)

    try {
      // 模拟查询执行
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟查询结果
      const mockResults = [
        { id: 1, name: '产品A', value: 100, date: '2024-01-01' },
        { id: 2, name: '产品B', value: 200, date: '2024-01-02' },
        { id: 3, name: '产品C', value: 150, date: '2024-01-03' }
      ]
      
      setQueryResults(mockResults)
      
      if (onDataSelect) {
        onDataSelect(mockResults)
      }
    } catch (err) {
      setError('查询执行失败')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDataSourceSelect = (source: LocalDataSource) => {
    setSelectedSource(source)
    setQuery(`SELECT * FROM ${source.name} LIMIT 10`)
    }

  return (
    <div className={`h-full flex flex-col ${className || ''}`}>
      <Card className="flex-1 flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            数据面板
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
      <Tabs value="sources" onValueChange={() => {}} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="sources">数据源</TabsTrigger>
          <TabsTrigger value="query">查询</TabsTrigger>
          <TabsTrigger value="results">结果</TabsTrigger>
        </TabsList>

            <TabsContent value="sources" className="flex-1 space-y-4">
              <div className="flex items-center gap-2">
                <Search className="w-4 h-4" />
                <Input placeholder="搜索数据源..." className="flex-1" />
                <Button variant="outline" size="sm">
                  <RefreshCw className="w-4 h-4" />
              </Button>
          </div>

              <div className="space-y-2">
                {sampleDataSources.map((source) => (
              <Card 
                key={source.id} 
                className={`cursor-pointer transition-colors ${
                      selectedSource?.id === source.id ? 'border-blue-500 bg-blue-50' : 'hover:bg-gray-50'
                }`}
                    onClick={() => handleDataSourceSelect(source)}
              >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <Table className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div className="flex-1">
                          <h4 className="font-medium">{source.name}</h4>
                          <p className="text-sm text-gray-600 mt-1">{source.description}</p>
                          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                            <span>{source.columns.length} 列</span>
                            {source.rowCount && <span>{source.rowCount} 行</span>}
                            <span className="capitalize">{source.type}</span>
                          </div>
                          </div>
                      </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

            <TabsContent value="query" className="flex-1 space-y-4">
              <div className="space-y-2">
            <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">SQL 查询</label>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Code className="w-4 h-4 mr-1" />
                      格式化
                    </Button>
                    <Button variant="outline" size="sm">
                      <Eye className="w-4 h-4 mr-1" />
                      预览
                </Button>
                  </div>
                </div>
                <textarea
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="输入 SQL 查询..."
                  className="w-full h-32 p-3 border rounded-md font-mono text-sm"
                />
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={handleQueryExecute}
                  disabled={isLoading || !query.trim()}
                  className="flex-1"
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Play className="w-4 h-4 mr-2" />
                  )}
                  执行查询
                </Button>
                <Button variant="outline" disabled={queryResults.length === 0}>
                  <Download className="w-4 h-4 mr-2" />
                  导出
                </Button>
          </div>

              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{error}</p>
          </div>
              )}
        </TabsContent>

            <TabsContent value="results" className="flex-1 space-y-4">
              {queryResults.length > 0 ? (
                <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      共 {queryResults.length} 行结果
                    </span>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <BarChart3 className="w-4 h-4 mr-1" />
                        创建图表
                    </Button>
                      <Button variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-1" />
                        导出数据
                    </Button>
                </div>
              </div>

                  <div className="border rounded-md overflow-hidden">
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead className="bg-gray-50">
                    <tr>
                            {Object.keys(queryResults[0] || {}).map((key) => (
                              <th key={key} className="px-4 py-2 text-left font-medium">
                                {key}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                          {queryResults.map((row, index) => (
                            <tr key={index} className="border-t">
                              {Object.values(row).map((value, cellIndex) => (
                                <td key={cellIndex} className="px-4 py-2">
                                  {String(value)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
                  </div>
                </div>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              <div className="text-center">
                    <Database className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>暂无查询结果</p>
                    <p className="text-sm mt-1">执行查询后结果将显示在这里</p>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
        </CardContent>
      </Card>

      {/* 迁移提示 */}
      <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
        <p className="text-yellow-800">
          📊 数据面板已简化，支持基本的数据源浏览和查询功能
        </p>
      </div>
    </div>
  )
} 