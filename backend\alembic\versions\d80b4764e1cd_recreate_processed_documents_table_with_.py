"""Recreate processed_documents table with new schema for tables and charts

Revision ID: d80b4764e1cd
Revises: 72d33cb1c347
Create Date: 2025-07-11 10:51:00.274213

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd80b4764e1cd'
down_revision: Union[str, Sequence[str], None] = '72d33cb1c347'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('processed_documents')
    op.create_table('processed_documents',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('filename', sa.String(length=255), nullable=False),
        sa.Column('markdown_content', sa.Text(), nullable=False),
        sa.Column('tables', sa.JSON(), nullable=True),
        sa.Column('charts', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_processed_documents_id'), 'processed_documents', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('processed_documents')
    # Recreate the old table structure for downgrade
    op.create_table('processed_documents',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('filename', sa.String(length=255), nullable=False),
        sa.Column('markdown_content', sa.Text(), nullable=False),
        sa.Column('charts_data', mysql.JSON(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###
