import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json({ error: '请选择要上传的文件' }, { status: 400 })
    }

    // 验证文件类型
    const allowedTypes = ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: '仅支持Word文档格式(.docx, .doc)' }, { status: 400 })
    }

    // 转发到后端API进行完整处理
    const backendFormData = new FormData()
    backendFormData.append('file', file)

    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000'
    const response = await fetch(`${backendUrl}/api/word-parser/process`, {
      method: 'POST',
      body: backendFormData,
    })

    if (!response.ok) {
      throw new Error(`后端请求失败: ${response.status}`)
    }

    const result = await response.json()
    return NextResponse.json(result)

  } catch (error) {
    console.error('Word模板上传失败:', error)
    return NextResponse.json({ 
      error: '文件上传失败，请重试',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
} 