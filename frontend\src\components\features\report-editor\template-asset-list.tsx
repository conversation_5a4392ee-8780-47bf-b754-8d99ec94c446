"use client";

import React from "react";
import { useDraggable } from "@dnd-kit/core";
import { Card } from "@/components/ui/card";
import { GripVertical } from "lucide-react";

interface Asset {
  id: string;
  title: string;
}

interface AssetItemProps {
  asset: Asset;
  assetType: "table" | "chart";
}

const AssetItem = ({ asset, assetType }: AssetItemProps) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: `asset-${assetType}-${asset.id}`,
      data: {
        type: assetType,
        id: asset.id,
        title: asset.title,
      },
    });

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
      }
    : undefined;

  return (
    <div
      ref={setNodeRef}
      style={{ ...style, opacity: isDragging ? 0.5 : 1 }}
      className="mb-2 cursor-grab active:cursor-grabbing"
      {...listeners}
      {...attributes}
    >
      <Card className="p-2 flex items-center">
        <GripVertical className="mr-2 h-5 w-5 text-gray-400" />
        <span className="flex-grow">{asset.title}</span>
      </Card>
    </div>
  );
};

interface TemplateAssetListProps {
  assets: Asset[];
  assetType: "table" | "chart";
}

const TemplateAssetList = ({ assets, assetType }: TemplateAssetListProps) => {
  const emptyMessage =
    assetType === "table" ? "此模板无可用表格" : "此模板无可用图表";

  if (assets.length === 0) {
    return (
      <p className="text-sm text-gray-500 mt-4 text-center">{emptyMessage}</p>
    );
  }

  return (
    <div className="mt-4">
      {assets.map((asset) => (
        <AssetItem key={asset.id} asset={asset} assetType={assetType} />
      ))}
    </div>
  );
};

export default TemplateAssetList;
