"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Bar<PERSON><PERSON>3,
  <PERSON><PERSON>hart,
  Pie<PERSON>hart,
  TrendingUp,
  Plus,
  Settings,
  Eye,
  Download,
  Palette,
} from "lucide-react";

interface ChartConfig {
  id: string;
  type: "bar" | "line" | "pie" | "scatter" | "area";
  title: string;
  dataSource: string;
  xAxis: string;
  yAxis: string;
  groupBy?: string;
  aggregation?: "sum" | "avg" | "count" | "max" | "min";
  colors: string[];
  options: {
    responsive: boolean;
    maintainAspectRatio: boolean;
    showLegend: boolean;
    showDataLabels: boolean;
  };
}

interface ChartTemplate {
  id: string;
  name: string;
  description: string;
  type: ChartConfig["type"];
  icon: React.ReactNode;
  defaultConfig: Partial<ChartConfig>;
}

interface ChartPanelProps {
  template: any;
  onChartCreate: (chart: ChartConfig) => void;
}

export default function ChartPanel({
  template,
  onChartCreate,
}: ChartPanelProps) {
  const [activeTab, setActiveTab] = useState("templates");
  const [currentChart, setCurrentChart] = useState<ChartConfig | null>(null);
  const [previewData, setPreviewData] = useState<any>(null);

  // 图表模板
  const chartTemplates: ChartTemplate[] = [
    {
      id: "bar_basic",
      name: "基础柱状图",
      description: "适合对比不同类别的数据",
      type: "bar",
      icon: <BarChart3 className="w-5 h-5 text-blue-600" />,
      defaultConfig: {
        type: "bar",
        title: "数据对比图",
        options: {
          responsive: true,
          maintainAspectRatio: false,
          showLegend: true,
          showDataLabels: false,
        },
      },
    },
    {
      id: "line_trend",
      name: "趋势折线图",
      description: "显示数据随时间的变化趋势",
      type: "line",
      icon: <LineChart className="w-5 h-5 text-green-600" />,
      defaultConfig: {
        type: "line",
        title: "趋势分析图",
        options: {
          responsive: true,
          maintainAspectRatio: false,
          showLegend: true,
          showDataLabels: false,
        },
      },
    },
    {
      id: "pie_proportion",
      name: "比例饼图",
      description: "显示各部分占整体的比例",
      type: "pie",
      icon: <PieChart className="w-5 h-5 text-purple-600" />,
      defaultConfig: {
        type: "pie",
        title: "比例分析图",
        options: {
          responsive: true,
          maintainAspectRatio: false,
          showLegend: true,
          showDataLabels: true,
        },
      },
    },
  ];

  // 颜色方案
  const colorSchemes = [
    { name: "蓝色系", colors: ["#3B82F6", "#60A5FA", "#93C5FD", "#DBEAFE"] },
    { name: "绿色系", colors: ["#10B981", "#34D399", "#6EE7B7", "#D1FAE5"] },
    { name: "紫色系", colors: ["#8B5CF6", "#A78BFA", "#C4B5FD", "#EDE9FE"] },
    { name: "橙色系", colors: ["#F59E0B", "#FBBF24", "#FCD34D", "#FEF3C7"] },
    {
      name: "多彩",
      colors: ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6"],
    },
  ];

  // 可用数据源（模拟）
  const dataSources = [
    {
      id: "tourism_data",
      name: "旅游数据表",
      columns: ["date", "visitors", "revenue", "region"],
    },
    {
      id: "monthly_stats",
      name: "月度统计",
      columns: ["month", "total", "growth_rate", "category"],
    },
    {
      id: "regional_data",
      name: "地区数据",
      columns: ["region", "population", "gdp", "area"],
    },
  ];

  // 创建新图表
  const createChart = (templateConfig: ChartTemplate) => {
    const newChart: ChartConfig = {
      id: `chart_${Date.now()}`,
      type: templateConfig.type,
      title: templateConfig.defaultConfig.title || "新图表",
      dataSource: "",
      xAxis: "",
      yAxis: "",
      aggregation: "sum",
      colors: colorSchemes[0]?.colors || ["#3B82F6"],
      options: templateConfig.defaultConfig.options || {
        responsive: true,
        maintainAspectRatio: false,
        showLegend: true,
        showDataLabels: false,
      },
    };

    setCurrentChart(newChart);
    setActiveTab("config");
  };

  // 更新图表配置
  const updateChart = (updates: Partial<ChartConfig>) => {
    if (currentChart) {
      setCurrentChart({ ...currentChart, ...updates });
    }
  };

  // 预览图表
  const previewChart = async () => {
    if (!currentChart) return;

    try {
      const response = await fetch("/api/charts/preview", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(currentChart),
      });

      if (response.ok) {
        const data = await response.json();
        setPreviewData(data);
        setActiveTab("preview");
      }
    } catch (error) {
      console.error("图表预览失败:", error);
    }
  };

  // 保存图表
  const saveChart = () => {
    if (currentChart && onChartCreate) {
      onChartCreate(currentChart);
      setCurrentChart(null);
      setActiveTab("templates");
    }
  };

  return (
    <div className="h-full flex flex-col bg-white max-w-md mx-left w-full">
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="flex-1 flex flex-col"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="templates">模板</TabsTrigger>
          <TabsTrigger value="config">配置</TabsTrigger>
          <TabsTrigger value="preview">预览</TabsTrigger>
        </TabsList>

        {/* 图表模板 */}
        <TabsContent value="templates" className="flex-1 overflow-y-auto">
          <div className="p-3 space-y-3">
            <div className="text-sm text-gray-600 mb-3">
              选择图表类型开始创建
            </div>

            {chartTemplates.map((template) => (
              <Card
                key={template.id}
                className="cursor-pointer hover:shadow-md transition-all"
                onClick={() => createChart(template)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      {template.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{template.name}</h4>
                      <p className="text-xs text-gray-600 mt-1">
                        {template.description}
                      </p>
                    </div>
                    <Plus className="w-4 h-4 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* 图表配置 */}
        <TabsContent value="config" className="flex-1 overflow-y-auto">
          {currentChart ? (
            <div className="p-4 space-y-4">
              {/* 基础配置 */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">基础配置</h3>

                <div>
                  <Label className="text-xs">图表标题</Label>
                  <Input
                    value={currentChart.title}
                    onChange={(e) => updateChart({ title: e.target.value })}
                    placeholder="输入图表标题"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label className="text-xs">数据源</Label>
                  <Select
                    value={currentChart.dataSource}
                    onValueChange={(value) =>
                      updateChart({ dataSource: value })
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="选择数据源" />
                    </SelectTrigger>
                    <SelectContent>
                      {dataSources.map((source) => (
                        <SelectItem key={source.id} value={source.id}>
                          {source.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {currentChart.dataSource && (
                  <>
                    <div>
                      <Label className="text-xs">X轴字段</Label>
                      <Select
                        value={currentChart.xAxis}
                        onValueChange={(value) => updateChart({ xAxis: value })}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="选择X轴字段" />
                        </SelectTrigger>
                        <SelectContent>
                          {dataSources
                            .find((s) => s.id === currentChart.dataSource)
                            ?.columns.map((column) => (
                              <SelectItem key={column} value={column}>
                                {column}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-xs">Y轴字段</Label>
                      <Select
                        value={currentChart.yAxis}
                        onValueChange={(value) => updateChart({ yAxis: value })}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="选择Y轴字段" />
                        </SelectTrigger>
                        <SelectContent>
                          {dataSources
                            .find((s) => s.id === currentChart.dataSource)
                            ?.columns.map((column) => (
                              <SelectItem key={column} value={column}>
                                {column}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-xs">聚合方式</Label>
                      <Select
                        value={currentChart.aggregation || "sum"}
                        onValueChange={(value: string) =>
                          updateChart({
                            aggregation: value as
                              | "sum"
                              | "avg"
                              | "count"
                              | "max"
                              | "min",
                          })
                        }
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="选择聚合方式" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sum">求和</SelectItem>
                          <SelectItem value="avg">平均值</SelectItem>
                          <SelectItem value="count">计数</SelectItem>
                          <SelectItem value="max">最大值</SelectItem>
                          <SelectItem value="min">最小值</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
              </div>

              {/* 样式配置 */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium">样式配置</h3>

                <div>
                  <Label className="text-xs">颜色方案</Label>
                  <div className="grid grid-cols-1 gap-2 mt-2">
                    {colorSchemes.map((scheme, index) => (
                      <div
                        key={index}
                        className={`p-2 rounded border cursor-pointer ${
                          JSON.stringify(currentChart.colors) ===
                          JSON.stringify(scheme.colors)
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() => updateChart({ colors: scheme.colors })}
                      >
                        <div className="flex items-center justify-between">
                          <span className="text-xs">{scheme.name}</span>
                          <div className="flex space-x-1">
                            {scheme.colors.slice(0, 4).map((color, i) => (
                              <div
                                key={i}
                                className="w-3 h-3 rounded"
                                style={{ backgroundColor: color }}
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-xs">显示选项</Label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={currentChart.options.showLegend}
                        onChange={(e) =>
                          updateChart({
                            options: {
                              ...currentChart.options,
                              showLegend: e.target.checked,
                            },
                          })
                        }
                        className="w-4 h-4"
                      />
                      <span className="text-xs">显示图例</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={currentChart.options.showDataLabels}
                        onChange={(e) =>
                          updateChart({
                            options: {
                              ...currentChart.options,
                              showDataLabels: e.target.checked,
                            },
                          })
                        }
                        className="w-4 h-4"
                      />
                      <span className="text-xs">显示数据标签</span>
                    </label>
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex space-x-2 pt-4 border-t">
                <Button
                  onClick={previewChart}
                  disabled={
                    !currentChart.dataSource ||
                    !currentChart.xAxis ||
                    !currentChart.yAxis
                  }
                  className="flex-1"
                  size="sm"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  预览
                </Button>
                <Button
                  onClick={saveChart}
                  disabled={
                    !currentChart.dataSource ||
                    !currentChart.xAxis ||
                    !currentChart.yAxis
                  }
                  variant="default"
                  className="flex-1"
                  size="sm"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  创建
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <Settings className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>请先选择图表模板</p>
              </div>
            </div>
          )}
        </TabsContent>

        {/* 图表预览 */}
        <TabsContent value="preview" className="flex-1 overflow-y-auto">
          <div className="p-4">
            {previewData ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">图表预览</h3>
                  <div className="flex space-x-1">
                    <Button size="sm" variant="outline">
                      <Download className="w-4 h-4 mr-1" />
                      下载
                    </Button>
                    <Button size="sm" onClick={saveChart}>
                      <Plus className="w-4 h-4 mr-1" />
                      使用
                    </Button>
                  </div>
                </div>

                <div className="border rounded-lg p-4 bg-white">
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
                    <div className="text-center">
                      <BarChart3 className="w-16 h-16 mx-auto mb-2 text-gray-400" />
                      <p className="text-gray-600">图表预览区域</p>
                      <p className="text-xs text-gray-500 mt-1">
                        {currentChart?.title}
                      </p>
                    </div>
                  </div>
                </div>

                {/* 数据预览 */}
                <div className="border rounded-lg p-4">
                  <h4 className="text-xs font-medium mb-2">数据样本</h4>
                  <div className="bg-gray-50 rounded p-2 text-xs font-mono"></div>
                </div>
              </div>
            ) : (
              <div className="flex-1 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <Eye className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>配置并预览图表后在此处查看</p>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
