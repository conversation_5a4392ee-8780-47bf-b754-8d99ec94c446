import { apiClient } from './client';

export interface DataSource {
  id: string;
  name: string;
  type: string;
  connection_string?: string;
  description?: string;
  columns?: Column[];
  rowCount?: number;
}

export interface Column {
  name: string;
  type: string;
  description?: string;
}

export interface SqlQueryRequest {
  query: string;
  template_context?: any;
}

export interface SqlQueryResponse {
  columns: string[];
  rows: any[][];
  total: number;
}

// 数据相关API
export const dataApi = {
  // 获取数据源列表
  async getDataSources(): Promise<DataSource[]> {
    const response = await apiClient.get<DataSource[]>('/api/data-sources');
    return response.data;
  },

  // 获取单个数据源
  async getDataSource(id: string): Promise<DataSource> {
    const response = await apiClient.get<DataSource>(`/api/data-sources/${id}`);
    return response.data;
  },

  // 创建数据源
  async createDataSource(data: Omit<DataSource, 'id'>): Promise<DataSource> {
    const response = await apiClient.post<DataSource>('/api/data-sources', data);
    return response.data;
  },

  // 更新数据源
  async updateDataSource(id: string, data: Partial<DataSource>): Promise<DataSource> {
    const response = await apiClient.put<DataSource>(`/api/data-sources/${id}`, data);
    return response.data;
  },

  // 删除数据源
  async deleteDataSource(id: string): Promise<{ success: boolean }> {
    await apiClient.delete(`/api/data-sources/${id}`);
    return { success: true };
  },

  // 执行SQL查询
  async executeQuery(request: SqlQueryRequest): Promise<SqlQueryResponse> {
    const response = await apiClient.post<SqlQueryResponse>('/api/sql-query', request);
    return response.data;
  },

  // 获取数据（通用）
  async getData(sourceId: string, query?: any): Promise<any> {
    const response = await apiClient.post(`/api/data/${sourceId}`, query);
    return response.data;
  },
}; 