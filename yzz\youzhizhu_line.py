import json
import re
import sqlparse
import pandas as pd
import mysql.connector
from mysql.connector import Error
from typing import Optional
import os
import openai
from langchain.embeddings.base import Embeddings
from langchain.schema import Document
from langchain_community.vectorstores import FAISS
from typing import Optional, List, Dict, Any
from contextlib import contextmanager
import logging
import uuid
import asyncio  # 添加这行
from datetime import datetime, timedelta
from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse, JSONResponse
from docx import Document as DocxDocument
from fastapi import UploadFile, File
import traceback
import pdfplumber
from fastapi import Body

# v0619
# 禁用特定模块的日志
logging.getLogger('faiss').setLevel(logging.WARNING)
logging.getLogger('faiss.loader').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('mysql.connector').setLevel(logging.WARNING)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# ==================== 配置管理 ====================
class Config:
    """集中管理配置"""
    # 服务器大模型配置
    SERVER_BASE_URL = 'http://***************:18997/v1'
    MODEL_NAME = 'qwen-72b'
    EMBEDDING_MODEL = 'bge-large-zh-v1.5'

    # 数据库配置
    DB_CONFIG = {
        'host': '***************',
        'user': 'root',
        'password': 'Gg_13506800412',
        'database': 'cultural_tourism',
        'port': 18002,
        'pool_size': 5,
        'pool_name': 'qa_pool'
    }


    # 文件路径
    INDICATORS_EXCEL = '指标.xlsx'
    FAISS_INDEX_PATH = 'faiss_index'
    ADMIN_INDICATORS_FILE = 'indicators_admin.txt'
    SCENIC_INDICATORS_FILE = 'scenic_indicators.txt'

    # 模型参数
    DEFAULT_TEMPERATURE = 0.2
    RETRIEVER_K = 1
    MAX_CONCURRENT_REQUESTS = 5

# ==================== 数据库连接池 ====================
class DatabaseManager:
    """数据库连接池管理"""
    _pool = None

    @classmethod
    def get_pool(cls):
        """获取连接池单例"""
        if cls._pool is None:
            try:
                cls._pool = mysql.connector.pooling.MySQLConnectionPool(
                    pool_name=Config.DB_CONFIG['pool_name'],
                    pool_size=Config.DB_CONFIG['pool_size'],
                    **{k: v for k, v in Config.DB_CONFIG.items()
                       if k not in ['pool_name', 'pool_size']}
                )
                logger.info("数据库连接池初始化成功")
            except Error as e:
                logger.error(f"数据库连接池初始化失败: {e}")
                raise
        return cls._pool

    @classmethod
    @contextmanager
    def get_connection(cls):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = cls.get_pool().get_connection()
            yield conn
        except Error as e:
            logger.error(f"数据库连接错误: {e}")
            if conn:
                conn.rollback()
            raise
        finally:
            if conn and conn.is_connected():
                conn.close()


# ==================== OpenAI 客户端包装 ====================
class LLMClient:
    """LLM 客户端封装"""

    def __init__(self):
        self.client = openai.Client(
            base_url=Config.SERVER_BASE_URL,
            api_key="no_key",
        )

    def invoke_llm(
            self,
            messages: Optional[List[Dict]] = None,
            system_prompt: Optional[str] = None,
            user_content: Optional[str] = None,
            stream: bool = False,
            temperature: Optional[float] = None
    ):
        """统一的 LLM 调用接口

        Args:
            messages: 完整的消息列表，格式为 [{"role": "system/user/assistant", "content": "..."}]
            system_prompt: 系统提示词（与messages二选一）
            user_content: 用户输入内容（与messages二选一）
            stream: 是否流式输出
            temperature: 生成温度参数

        Returns:
            OpenAI ChatCompletion 响应对象
        """
        if temperature is None:
            temperature = Config.DEFAULT_TEMPERATURE

        if messages is not None:
            payload = {
                "model": Config.MODEL_NAME,
                "messages": messages,
                "stream": stream,
                "temperature": temperature
            }
        else:
            if system_prompt is None or user_content is None:
                raise ValueError("invoke_llm: 要么传 messages，要么同时传 system_prompt 和 user_content")
            payload = {
                "model": Config.MODEL_NAME,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_content}
                ],
                "stream": stream,
                "temperature": temperature
            }

        return self.client.chat.completions.create(**payload)


# 初始化 LLM 客户端
llm_client = LLMClient()
invoke_llm = llm_client.invoke_llm  # 保持向后兼容


class BGEEmbeddings(Embeddings):
    """BGE 嵌入模型封装（替代原 DashScopeEmbeddings）"""

    def __init__(self, client: openai.Client = None, model_name: str = Config.EMBEDDING_MODEL):
        """
        初始化 BGE 嵌入模型

        Args:
            client: OpenAI 客户端实例，如果不提供则创建新实例
            model_name: 模型名称，默认使用配置中的 EMBEDDING_MODEL
        """
        if client is None:
            self.client = openai.Client(
                base_url=Config.SERVER_BASE_URL,
                api_key="dummy_key"
            )
        else:
            self.client = client
        self.model_name = model_name

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """批量文本向量化

        Args:
            texts: 要向量化的文本列表

        Returns:
            嵌入向量列表，每个向量是1024维的浮点数列表
        """
        if not texts:
            return []

        # 处理大批量文本时的分批处理
        batch_size = 25
        all_embeddings = []

        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            try:
                resp = self.client.embeddings.create(
                    model=self.model_name,
                    input=batch
                )
                all_embeddings.extend([d.embedding for d in resp.data])
            except Exception as e:
                print(f"批量嵌入错误 (batch {i // batch_size + 1}): {e}")
                # 可以选择跳过错误批次或重新抛出异常
                raise

        return all_embeddings

    def embed_query(self, text: str) -> List[float]:
        """单条查询向量化

        Args:
            text: 要向量化的查询文本

        Returns:
            1024维的嵌入向量
        """
        try:
            resp = self.client.embeddings.create(
                model=self.model_name,
                input=text
            )
            return resp.data[0].embedding
        except Exception as e:
            print(f"查询嵌入错误: {e}")
            raise


# 为了向后兼容，创建别名
DashScopeEmbeddings = BGEEmbeddings  # 保持原有的类名兼容性
# 初始化嵌入模型（使用与LLM相同的客户端实例）
embeddings = BGEEmbeddings(client=llm_client.client)


# ==================== 向量数据库管理 ====================
class VectorDBManager:
    """向量数据库管理器"""
    _instance = None
    _vectordb = None
    _retriever = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """初始化向量数据库"""
        embeddings = DashScopeEmbeddings(llm_client.client, Config.EMBEDDING_MODEL)

        if not os.path.exists(Config.FAISS_INDEX_PATH):
            # 载入指标文档
            df = pd.read_excel(Config.INDICATORS_EXCEL)
            docs = [
                Document(
                    page_content=f"指标名称: {r['指标名称']}\n指标含义: {r['指标含义']}\n计算逻辑: {r['计算逻辑']}",
                    metadata={'指标名称': r['指标名称']}
                ) for _, r in df.iterrows()
            ]

            self._vectordb = FAISS.from_documents(docs, embeddings)
            self._vectordb.save_local(Config.FAISS_INDEX_PATH)
            logger.info("向量数据库创建成功")
        else:
            self._vectordb = FAISS.load_local(
                Config.FAISS_INDEX_PATH,
                embeddings,
                allow_dangerous_deserialization=True
            )
            logger.info("向量数据库加载成功")

        self._retriever = self._vectordb.as_retriever(search_kwargs={'k': Config.RETRIEVER_K})

    @property
    def retriever(self):
        return self._retriever


# 初始化向量数据库
vector_db = VectorDBManager()
retriever = vector_db.retriever


# ==================== 指标管理 ====================
class IndicatorManager:
    """指标管理器"""

    @staticmethod
    def load_indicators(txt_path: str) -> str:
        """从文件加载指标"""
        try:
            with open(txt_path, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except FileNotFoundError:
            logger.warning(f"指标文件不存在: {txt_path}")
            return ""

    @staticmethod
    def write_active_indicators(table_name: str, txt_path: str, comma_separated: bool = False) -> List[str]:
        """
        从数据库查询活跃指标并写入到文件

        Args:
            table_name: 数据库表名（必须是合法的表名，防止SQL注入）
            txt_path: 输出文件的路径
            comma_separated: 是否使用逗号分隔格式
                            True: 输出格式为 'name1','name2','name3'
                            False: 每行一个指标名称

        Returns:
            List[str]: 成功写入的指标名称列表，出错时返回空列表
        """
        try:
            # === 1. 安全性检查 ===
            # 验证表名只包含字母、数字、下划线，防止SQL注入攻击
            if not re.fullmatch(r'\w+', table_name):
                raise ValueError(f"非法的表名：{table_name}")

            print(f"开始处理表：{table_name}")  # 调试信息

            # === 2. 数据库查询 ===
            # 使用数据库连接管理器获取连接
            with DatabaseManager.get_connection() as conn:
                cursor = conn.cursor()

                # 查询状态为1（活跃）的指标名称，使用DISTINCT去重
                sql = f"SELECT DISTINCT name FROM {table_name} WHERE status = 1"
                print(f"执行SQL查询：{sql}")  # 调试信息

                cursor.execute(sql)
                rows = cursor.fetchall()  # 获取所有查询结果
                cursor.close()

                print(f"查询到 {len(rows)} 条记录")  # 调试信息

            # === 3. 数据处理 ===
            # 提取指标名称，过滤掉空值，并按字母顺序排序
            names = sorted([r[0] for r in rows if r[0]])
            print(f"有效指标名称数量：{len(names)}")  # 调试信息

            if not names:
                print("警告：没有找到任何活跃指标（status=1）")
                return []

            # === 4. 文件写入准备 ===
            # 确保目标目录存在，如果不存在则创建
            dirpath = os.path.dirname(txt_path) or '.'  # 获取目录路径，默认为当前目录
            print(f"目标目录：{os.path.abspath(dirpath)}")  # 调试信息
            os.makedirs(dirpath, exist_ok=True)

            # 获取文件的绝对路径用于调试
            abs_path = os.path.abspath(txt_path)
            print(f"准备写入文件：{abs_path}")

            # === 5. 写入文件 ===
            with open(txt_path, 'w', encoding='utf-8') as f:
                if comma_separated:
                    # 逗号分隔格式：'name1','name2','name3'
                    content = ",".join(f"'{n}'" for n in names)
                    print(f"写入内容（逗号分隔）：{content}")  # 调试信息
                    f.write(content)
                else:
                    # 每行一个指标名称的格式
                    print("写入内容（每行一个）：")  # 调试信息
                    for n in names:
                        print(f"  {n}")  # 打印每个指标名称
                        f.write(n + "\n")

            # === 6. 验证写入结果 ===
            # 检查文件是否成功创建并读取内容进行验证
            if os.path.exists(txt_path):
                file_size = os.path.getsize(txt_path)
                print(f"文件写入成功！文件大小：{file_size} 字节")

                # 读取并显示文件内容（用于调试）
                with open(txt_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()
                    print(f"文件实际内容：{repr(file_content)}")  # 使用repr显示转义字符
            else:
                print("警告：文件未成功创建")

            # 记录成功日志
            logger.info(f"成功写入 {len(names)} 条 status=1 的指标到：{txt_path}")
            return names

        except Exception as e:
            # === 7. 异常处理 ===
            error_msg = f"写入指标文件时出错：{e}"
            logger.error(error_msg)
            print(error_msg)  # 同时输出到控制台，确保能看到错误信息

            # 打印详细的错误堆栈信息，便于调试
            import traceback
            print("详细错误信息：")
            traceback.print_exc()

            # 返回空列表表示失败（注意：这里没有重新抛出异常）
            return []


# 导出函数（保持向后兼容）
def load_indicators_admin(txt_path: str = "indicators_admin.txt") -> str:
    return IndicatorManager.load_indicators(txt_path)


def load_indicators_scenic(txt_path: str = "scenic_indicators.txt") -> str:
    return IndicatorManager.load_indicators(txt_path)


def write_active_indicators(table_name: str, txt_path: str, comma_separated: bool = False):
    return IndicatorManager.write_active_indicators(table_name, txt_path, comma_separated)


def export_indicators_to_file():
    """
    导出区域指标到文件
    从 tb_area_target_info 表中查询 status=1 的指标并写入到 indicators_admin.txt 文件
    """
    write_active_indicators(
        table_name="tb_area_target_info",    # 区域指标表名
        txt_path="indicators_admin.txt",     # 输出文件路径
        comma_separated=True                 # 使用逗号分隔格式
    )


def export_scenic_indicators_to_file():
    """导出tb_scenic_target_info表中status=1的指标到文件"""
    write_active_indicators(
        table_name="tb_area_target_info",
        txt_path="scenic_indicators.txt",
        comma_separated=True
    )

def build_prompt(block_text):
    return f"""你是一个文档结构化助手。请将下列文本分级结构化为JSON数组，要求如下：
    1. 每项包含 title、level、content、children 字段。
    2. 标题（title）字段必须完整保留原文所有编号、序号、括号等前缀内容（例如“1.”、“二、”、“（一）”、“1.1”），不能省略或修改。
    3. 不输出任何解释、代码块和范例。
    4. 严格忽略所有带“表”或“图”字的标题及内容，不要出现在结果里。
    5. 仅输出合法JSON数组。
    6. 除文件第一行外，只有以编号或序号（如“1.”、“2.”、“一、”、“（一）”、“1.1”、“2.2.1”、“（三）”等）开头的行才能作为标题（title 字段），否则只能作为正文内容（content 字段），绝不能把无编号/无序号的行当标题。
    7. 文件第一行（最上面那一行）无论是否带编号，均作为第一级标题。若该行包含时间（如括号内的日期、年份、时间段，或行首、行尾的日期/年份/月份等），则将所有时间类内容（如“2025年”“1-5月”“（2025年1-5月）”“2025年7月10日”）全部去除，仅保留标题本身作为第一级标题的 title 字段内容。
    正文如下：
    {block_text}
"""


def call_llm(prompt):
    resp = invoke_llm(messages=[{"role": "user", "content": prompt}])
    result = resp.choices[0].message.content
    return result

def extract_text_from_file(file_path):
    ext = os.path.splitext(file_path)[-1].lower()
    print(f"[INFO] 解析文件: {file_path}, 后缀: {ext}")
    if ext == ".docx":
        doc = DocxDocument(file_path)
        text = "\n".join([para.text for para in doc.paragraphs])
        print(f"[INFO] docx转文本, 长度: {len(text)}")
        print(text[:100])
        return text
    elif ext == ".pdf":
        text = ""
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                text += page.extract_text() or ""
        print(f"[INFO] pdf转文本, 长度: {len(text)}")
        print(text[:100])
        return text
    else:
        # 纯txt
        with open(file_path, 'r', encoding='utf-8') as f:
            text = f.read()
        print(f"[INFO] txt读取, 长度: {len(text)}")
        print(text[:100])
        return text

#def llm_structured_split(text: str):
    #example = '''
#[
  #{
    #"title": "一级标题示例",
    #"level": 1,
    #"content": "",
    #"children": [
      #{
        #"title": "二级标题示例",
        #"level": 2,
        #"content": "详细描述内容……",
        #"children": []
      #}
    #]
  #}
#]
    #'''
    #prompt = f"""你是一个文档结构化助手。请将下列文本拆成多级嵌套结构，每级有 title、level、content、children 字段，正文放 content，严格输出 JSON 数组。范例如下：
#{example}
#文本如下：
#{text}
#"""
    #print("[INFO] llm_structured_split 调用大模型，文本长度:", len(text))
    #try:
        #resp = invoke_llm(messages=[{"role": "user", "content": prompt}])
        
        #result = resp.choices[0].message.content
        #print("[INFO] LLM 返回前500字:", result[:500])
        #try:
            #segments = json.loads(result)
        #except Exception as e:
            #print("[ERROR] LLM输出不是合法JSON:", e)
            #print("[DEBUG] 原始大模型输出:", result)
            #segments = []
        #return segments
    #except Exception as e:
        #print("[ERROR] LLM接口异常:", e)
        #return []
#分块分割
def split_to_blocks(text):
    pattern = re.compile(r'^[一二三四五六七八九十][、.].*', re.MULTILINE)
    matches = list(pattern.finditer(text))
    blocks = []
    for i, match in enumerate(matches):
        start = match.start()
        end = matches[i+1].start() if i+1 < len(matches) else len(text)
        block = text[start:end].strip()
        blocks.append(block)
    return blocks

#自动修正加一级
def fix_levels(node, level=1):
    node['level'] = level
    if node.get('children'):
        for child in node['children']:
            fix_levels(child, level + 1)
    return node     

#分块分割
def split_document_to_segments(file_path):
    text = extract_text_from_file(file_path)
    #segments = llm_structured_split(text)
    lines = text.splitlines()
    if not lines:
        return []
    first_line = lines[0]
    rest_text = "\n".join(lines[1:])

    blocks = [first_line] + split_to_blocks(rest_text) 
    segments = []
    for block in blocks:
        prompt = build_prompt(block)
        llm_raw = call_llm(prompt)  
        # 去掉代码块
        llm_raw = re.sub(r"^```(?:json)?|```$", "", llm_raw.strip(), flags=re.MULTILINE).strip()
        try:
            part = json.loads(llm_raw)
            segments.extend(part)
        except Exception as e:
            print("分块解析失败", e, llm_raw[:200])
            raise
    if len(segments) >= 2:
        first = segments[0]
        first['children'] = segments[1:]
        fix_levels(first, level=1)  # 递归修正所有层级
        return [first]
    else:
        return segments


def get_available_tables() -> List[str]:
    """获取数据库中所有可用的cultural_wide_YYYYMM格式的表"""
    try:
        with DatabaseManager.get_connection() as conn:
            cursor = conn.cursor()
            # 查询所有表名
            cursor.execute("""
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = 'cultural_tourism' 
                AND TABLE_NAME LIKE 'cultural_wide_%'
                ORDER BY TABLE_NAME
            """)
            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()
            return tables
    except Exception as e:
        logger.error(f"获取表名列表失败: {e}")
        return []
def get_available_tables_scenic() -> List[str]:
    """获取数据库中所有可用的cultural_wide_YYYYMM格式的表"""
    try:
        with DatabaseManager.get_connection() as conn:
            cursor = conn.cursor()
            # 查询所有表名
            cursor.execute("""
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = 'cultural_tourism' 
                AND TABLE_NAME LIKE 'scenic_wide_%'
                ORDER BY TABLE_NAME
            """)
            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()
            return tables
    except Exception as e:
        logger.error(f"获取表名列表失败: {e}")
        return []


def get_available_months() -> str:
    """获取可用的月份范围，用于提示大模型"""
    tables = get_available_tables()
    if not tables:
        return ""

    # 提取年月信息，使用set去重
    months_set = set()
    for table in tables:
        if match := re.match(r'cultural_wide_(\d{6})', table):
            year_month = match.group(1)
            year = year_month[:4]
            month = year_month[4:]
            months_set.add(f"{year}年{month}月")

    if months_set:
        # 转换为列表并排序
        months_list = sorted(list(months_set))
        return f"数据库中目前只有以下月份的数据：{', '.join(months_list)}"
    return ""
def get_available_months_scenic() -> str:
    """获取可用的月份范围，用于提示大模型"""
    tables = get_available_tables_scenic()
    if not tables:
        return ""

    # 提取年月信息，使用set去重
    months_set = set()
    for table in tables:
        if match := re.match(r'scenic_wide_(\d{6})', table):
            year_month = match.group(1)
            year = year_month[:4]
            month = year_month[4:]
            months_set.add(f"{year}年{month}月")

    if months_set:
        # 转换为列表并排序
        months_list = sorted(list(months_set))
        return f"数据库中目前只有以下月份的数据：{', '.join(months_list)}"
    return ""


def build_admin_prompt(month_data: List[str]):
    indicators_admin = load_indicators_admin()
    print(f"当前指标{indicators_admin}")
    available_tables = get_available_tables()
    table_info = "\n".join([f"- {table}" for table in available_tables])
    print(f"数据库中的表{table_info}")
    print(f"\n月份表{month_data}\n")

    # 获取月份范围
    month_range = get_available_months()
    print(f"{month_range}")


    return f"""
        你是 SQL 生成助手。用户所有涉及的节假日，系统已经自动解析成了实际可查的表名，并顺序放在了 month_data 列表。
        【记忆优先级】
            当上一轮记忆中存在两个问答对且有冲突时，优先参照“最新记忆：”。
            当本轮用户问题出现“省”“市”“区”“县”等行政区关键词，视为“行政区”相关指标问题。优化为“时间+行政区名+指标名+问题形式”结构。
        【表名选择规则】
        1. 节假日查询  
        - 如果用户问题中提及节假日（如春节、国庆、中秋、端午、五一、十一、清明、元旦、劳动节、儿童节、重阳节、元宵节、圣诞节、新年等），sql查询的表为{month_data}
        
        2. 非节假日查询  
        - 若用户问题未提及节假日，则按以下【时间后缀规则】从问题中提取时间范围，根据时间后缀规则拼接出表名（如 2025、202501、20250101、2025Q1、202504A 等），再用于 SQL 查询。

        【时间后缀规则】
        a. 年：如“2025年……” → 返回：2025
        b. 月：如“2025年1月……” → 返回：202501
        c. 日：如“2025年1月1日……” → 返回：20250101
        d. 周：如“2025年1月第1周……” → 返回：202501w1
        e. 旬：如“2025年1月第1旬……” → 返回：202501T1
        f. 跨月累计查询：如“2025年1-4月”，且未出现“每个月”“分别”等分月关键词 → 返回：202504A
        g. 分别列出每月：如有“分别”“每个月”等分月关键词 → 返回多个后缀，如 202501, 202502, 202503, 202504
        h. 若出现“累计”“合计”“总数”等 → 强制使用累计表（结尾月+A）
        i. 季度：如“2025年第1季度……” → 返回：2025Q1

        若问题未明确提及时间，则默认时间为 2024 年；但若有任何时间表达（如年份、月份、节日等），必须保留原始时间信息。

        【SQL生成总则】
        - SQL查询严格使用当前步骤处理后得到的表名。
        - 其他所有规则（如指标、地区、增量/增速/占比、分组、排序等）全部保留原有逻辑，不允许删除或更改。
        - 只输出 SQL 语句本身，不输出注释、前缀、Markdown 等其他内容。
        - UNION/UNION ALL 必须保证所有 SELECT 字段数、字段类型一致！不一致就补 NULL。

        【数据库字段信息】
        - 数据库：cultural_tourism
        - 每张表结构如下：  
            - area_name (varchar)：区域名称，如'浙江省'
            - data_value (decimal)：指标值
            - date_time (varchar)：如'202401',注意:date_time与表名后缀是相同的！
            - data_unit (varchar)：单位
            - target_name (varchar)：指标名，如'全域旅游人数'
            - area_level (int)：1=省，2=市，3=区县

        【地区选取标准】
            area_level 必须与 area_name 层级严格一致：
            若 area_name 是省级（如“浙江省”），area_level = 1；
            若 area_name 是市级（如“杭州市”），area_level = 2；
            若 area_name 是区县级（如“西湖区”“上城区”），area_level = 3；
            禁止 area_level 与 area_name 层级不一致。
            若 area_name 不唯一（如“杭州市各区”），需查所有下辖区县，area_level=3，area_name 用所有下属区县名。
            判断 area_name 层级时，如含“区”“县”“市辖区”“旗”等，默认区县级；如含“市”且无“区”，默认市级；如含“省”，默认省级。

        【指标与地区选择】
        - 指标(target_name)严格匹配用户问题与指标列表{indicators_admin}。
        - area_name、area_level按粒度最细原则选择。

        【增量/增速/占比等计算】
        - “增量/提高”用今年减去年：今年-去年。
        - “增速/增长/增幅”用百分比：(今年-去年)/去年*100。
        - “占比/比重”按本地值/同级汇总值*100，按行政区级别汇总。
        - 具体计算按下方示例生成。
        【计算逻辑】
            原始指标完全匹配：直接查原始表
            排名列别名 visit_rank，计算列名 computed_value
            “在全省排名多少”务必按 area_level 限定范围
            市内排名：用 t_area_info.pid 限定同城
            占比/比重：分子本地值，分母同级汇总
            增量/提高：今年-去年
            增速/增长/增幅：百分比 (今年-去年)/去年*100
            相关计算与排序全部按“示例”部分
        【输出要求】
        - 只输出SQL语句本身，禁止输出任何注释、前缀、Markdown等。

        【错误处理】
        - 如果month_data为空，直接输出空字符串即可，禁止生成SQL。

        【示例】
        1.单个月查询
        用户问题：“2025年5月杭州市全域旅游人数”
        month_data=['cultural_wide_202505']
        输出：
        SELECT data_value, data_unit FROM cultural_wide_202505 WHERE area_name = '杭州市' AND target_name = '全域旅游人数';
        
        2.分别查询
        包括分别，多个，各个等情况
        用户问题：“2024年1月到3月杭州市全域旅游人数分别是多少？”
        month_data=['cultural_wide_202401', 'cultural_wide_202402', 'cultural_wide_202403']
        输出：
        SELECT data_value, data_unit FROM cultural_wide_202401 WHERE area_name = '杭州市' AND target_name = '全域旅游人数'
        UNION ALL
        SELECT data_value, data_unit FROM cultural_wide_202402 WHERE area_name = '杭州市' AND target_name = '全域旅游人数'
        UNION ALL
        SELECT data_value, data_unit FROM cultural_wide_202403 WHERE area_name = '杭州市' AND target_name = '全域旅游人数';
        
        3.累计查询
        除了分别，多个，各个等情况其他全部都是累计查询
        用户问题：“2025年1-5月杭州市全域旅游人数分别是多少”
        month_data=['cultural_wide_202505A']
        输出：
        SELECT data_value, data_unit FROM cultural_wide_202505A WHERE area_name = '杭州市' AND target_name = '全域旅游人数'
        UNION ALL
        SELECT data_value, data_unit FROM cultural_wide_202505A WHERE area_name = '杭州市' AND target_name = '全域旅游人数';
        
        4.占比
        用户问题：“2025年5月杭州市全域旅游人数占比是多少”
        month_data=['cultural_wide_202505']
        输出：
        SELECT (t1.data_value / t2.total_value) * 100 AS ratio
        FROM
        (SELECT data_value FROM cultural_wide_202505 WHERE area_name = '杭州市' AND target_name = '全域旅游人数') t1,
        (SELECT SUM(data_value) AS total_value FROM cultural_wide_202505 WHERE target_name = '全域旅游人数' AND area_level = 2) t2;

        5.同比增速
        用户问题：“2025年5月杭州市全域旅游人数同比增速是多少”
        month_data=['cultural_wide_202505','cultural_wide_202405']
        输出：
        SELECT ((t1.data_value - t2.data_value) / t2.data_value * 100) AS percent_diff,
            t1.data_unit
        FROM cultural_wide_202505 t1,
            cultural_wide_202405 t2
        WHERE t1.target_name = '全域旅游人数'
        AND t1.area_name = '杭州市'
        AND t2.target_name = '全域旅游人数'
        AND t2.area_name = '杭州市';

        6.环比增速
        用户问题：“2025年5月杭州市全域旅游人数同比增速是多少”
        month_data=['cultural_wide_202505','cultural_wide_202405']
        输出：
        SELECT ((t1.data_value - t2.data_value) / t2.data_value * 100) AS percent_diff,
            t1.data_unit
        FROM cultural_wide_202505 t1,
            cultural_wide_202504 t2
        WHERE t1.target_name = '全域旅游人数'
        AND t1.area_name = '杭州市'
        AND t2.target_name = '全域旅游人数'
        AND t2.area_name = '杭州市';

        7.不同区县市级对比：
            查询“2024年西湖区全域旅游人数”：
                    SELECT area_name, data_value
                    FROM cultural_wide_2024
                    WHERE target_name = '全域旅游人数'
                    AND area_name = '西湖区'
                    AND area_level = 3;
            查询“2024年杭州市全域旅游人数”：
                    SELECT area_name, data_value
                    FROM cultural_wide_2024
                    WHERE target_name = '全域旅游人数'
                    AND area_name = '杭州市'
                    AND area_level = 2;
        8.节假日查询：
            查询：“2025年劳动节杭州市全域旅游人数”：
                SELECT data_value,
                    data_unit
                FROM cultural_wide_2025H4
                WHERE area_name = '杭州市'
                AND target_name = '全域旅游人数';
        9.同市县处理：
            查询：请问2024年一月 衢州市 全域旅游人数是多少 以及其下县市区
                SELECT area_name, data_value, data_unit
                FROM cultural_wide_202401
                WHERE target_name = '全域旅游人数'
                AND (
                        area_name = '衢州市'
                        OR area_name IN (
                            SELECT name
                            FROM t_area_info
                            WHERE pid = (SELECT id FROM t_area_info WHERE name = '衢州市')
                        )
                    );
        10.文化及相关产业增加值占GDP比重等含有GDP比重的指标名
            查询：请问2023年浙江省文化及相关产业增加值占GDP比重比上年提高多少？
            SELECT t1.data_value - t2.data_value AS percent_diff
            FROM cultural_wide_2023 t1,
                cultural_wide_2022 t2
            WHERE t1.target_name = '文化及相关产业增加值占GDP比重'
            AND t1.area_name = '浙江省'
            AND t2.target_name = '文化及相关产业增加值占GDP比重'
            AND t2.area_name = '浙江省';
        【反例】
        month_data=['cultural_wide_2025H2']，用户问“2025年5月杭州市全域旅游人数”
        错误示例（禁止输出）：
        SELECT ... FROM cultural_wide_202505 ...（不允许，因为表名不在month_data）

        month_data=[]，用户问“2025年5月杭州市全域旅游人数”
        正确输出：
        （空字符串）

        【特别强调】
        - SQL中表名**绝不能**出现在month_data列表之外。
        - 不允许根据时间、节假日等拼接表名，month_data只有什么用什么，否则输出空。

        请严格按以上规则，生成唯一一段SQL代码。


"""



def build_admin_rank_prompt(month_data: List[str]):
    indicators_admin = load_indicators_admin()
    return f"""
        你是 SQL 生成助手。用户所有涉及的节假日，系统已经自动解析成了实际可查的表名，并顺序放在了 month_data 列表。
        【表名选择规则】
        1. 节假日查询  
        - 如果用户问题中提及节假日（如春节、国庆、中秋、端午、五一、十一、清明、元旦、劳动节、儿童节、重阳节、元宵节、圣诞节、新年等），sql查询的表为{month_data}
        
        2. 非节假日查询  
        - 若用户问题未提及节假日，则按以下【时间后缀规则】从问题中提取时间范围，根据时间后缀规则拼接出表名（如 2025、202501、20250101、2025Q1、202504A 等），再用于 SQL 查询。

        【时间后缀规则】
        a. 年：如“2025年……” → 返回：2025
        b. 月：如“2025年1月……” → 返回：202501
        c. 日：如“2025年1月1日……” → 返回：20250101
        d. 周：如“2025年1月第1周……” → 返回：202501w1
        e. 旬：如“2025年1月第1旬……” → 返回：202501T1
        f. 跨月累计查询：如“2025年1-4月”，且未出现“每个月”“分别”等分月关键词 → 返回：202504A
        g. 分别列出每月：如有“分别”“每个月”等分月关键词 → 返回多个后缀，如 202501, 202502, 202503, 202504
        h. 若出现“累计”“合计”“总数”等 → 强制使用累计表（结尾月+A）
        i. 季度：如“2025年第1季度……” → 返回：2025Q1

        若问题未明确提及时间，则默认时间为 2024 年；但若有任何时间表达（如年份、月份、节日等），必须保留原始时间信息。
        【SQL 生成总则】
            - SQL查询严格使用当前步骤处理后得到的表名。
            - 其他所有规则（如指标、地区、增量/增速/占比、分组、排序等）全部保留原有逻辑，不允许删除或更改。
            - 只输出 SQL 语句本身，禁止输出注释、前缀、Markdown、额外说明等内容。
            - UNION/UNION ALL 必须保证所有 SELECT 字段数、字段类型一致！不一致就补 NULL。
        【数据库字段信息】
            数据库名：cultural_tourism
            每张表结构如下：
            area_name (varchar)：区域名称（如'浙江省'）
            data_value (decimal)：指标值
            date_time (varchar)：如'202401',注意:date_time与表名后缀是相同的！
            data_unit (varchar)：单位（如'万人次'）
            target_name (varchar)：指标名（如'全域旅游人数'）
            area_level (int)：1=省，2=市，3=区县
        【指标和地区选择标准】
            target_name 匹配顺序：
                完全匹配用户问题中的指标词和指标列表 {indicators_admin}
                无完全匹配则按语义相似度匹配
                或关键词模糊匹配
            匹配逻辑示例：
            用户问题："2024年杭州接待游客量是多少？"
            提取："接待游客量"
            在 {indicators_admin} 找到最匹配项如"全域接待游客量"
            最终 target_name = "全域接待游客量"
            target_name 必须严格来源于 indicators_admin列表中，不可创造或修改。
            area_name、area_level 选择：
            用户未指定，结合记忆补全
            市级 area_level=2，区县 area_level=3
            地名优先最细粒度，如“杭州市上城区”→ area_name=上城区
            “杭州市各区”需查所有下辖区县
            计算占比/比重时，分母限定在同级行政区
            禁止跨层级比较
            排名默认降序（最高、最多、第一名），升序（最少、最低、倒数第一）
            排名和聚合所有示例全部按下方“示例”区列出
        【地区选取标准】
            area_level 必须与 area_name 层级严格一致：
            若 area_name 是省级（如“浙江省”），area_level = 1；
            若 area_name 是市级（如“杭州市”），area_level = 2；
            若 area_name 是区县级（如“西湖区”“上城区”），area_level = 3；
            禁止 area_level 与 area_name 层级不一致。
            若 area_name 不唯一（如“杭州市各区”），需查所有下辖区县，area_level=3，area_name 用所有下属区县名。
            判断 area_name 层级时，如含“区”“县”“市辖区”“旗”等，默认区县级；如含“市”且无“区”，默认市级；如含“省”，默认省级。
        【计算与排名逻辑】
            原始指标完全匹配：直接查原始表
            排名列别名 visit_rank，计算列名 computed_value
            “在全省排名多少”务必按 area_level 限定范围
            市内排名：用 t_area_info.pid 限定同城
            占比/比重：分子本地值，分母同级汇总
            增量/提高：今年-去年
            增速/增长/增幅：百分比 (今年-去年)/去年*100
            相关计算与排序全部按“示例”部分
        【示例】
            1.问题优化结构示例
                本轮用户问题：2024年4月杭州市全域过夜游人数在全省排名？
                → 时间：2024年4月（保留）
                → 行政区：杭州市（已提及）
                → 指标词：“全域过夜游人数”与列表存在完全一致的指标名：“全域过夜游”
                → 问题：排名
                → 输出：2024年4月，杭州市全域过夜游人数是在全省排名多少？
            2.累计 vs 分别 查询示例
                “请问2024年1月到4月湖州市的全域旅游人数是多少？”
                累计查询：使用 cultural_wide_202404A 表
                “请分别列出2024年1月到4月湖州市的全域旅游人数”
                分别查询：使用 cultural_wide_202401、cultural_wide_202402、cultural_wide_202403、cultural_wide_202404，UNION ALL 拼接
            3.不可累加类指标/其他特例
                平均房价、入住率等无“分别”关键词，累计时也用累计表，结果为区间平均/比例
            4.指标与地区选择、占比、排名 SQL 示例
                （1）排名第一/最高：
                    WITH base AS (
                        SELECT area_name, data_value
                        FROM cultural_wide_2024
                        WHERE target_name = 'target_name' AND area_level = 2
                    ),
                    ranked AS (
                        SELECT area_name, data_value, RANK() OVER (ORDER BY data_value DESC) AS visit_rank
                        FROM base
                    )
                    SELECT area_name FROM ranked WHERE visit_rank = 1;
                （2）排名倒数第一/最低：
                    WITH base AS (
                        SELECT area_name, data_value
                        FROM cultural_wide_2024
                        WHERE target_name = 'target_name' AND area_level = 2
                    ),
                    ranked AS (
                        SELECT area_name, data_value, RANK() OVER (ORDER BY data_value ASC) AS visit_rank
                        FROM base
                    )
                    SELECT area_name FROM ranked WHERE visit_rank = 1;
                （3）排名第N名/倒数第N名：
                    -- 降序
                    SELECT area_name FROM ranked WHERE visit_rank = N;
                    -- 升序
                    SELECT area_name FROM ranked WHERE visit_rank = N;
                （4）“市内排名”逻辑：
                    WITH target_pid AS (
                        SELECT pid FROM t_area_info WHERE name = 'xx区' LIMIT 1
                    ),
                    same_city_areas AS (
                        SELECT name FROM t_area_info WHERE pid = (SELECT pid FROM target_pid)
                    ),
                    base AS (
                        SELECT area_name, data_value FROM cultural_wide_2024
                        WHERE target_name = '全域旅游人数'
                        AND area_name IN (SELECT name FROM same_city_areas)
                    ),
                    ranked AS (
                        SELECT area_name, data_value, RANK() OVER (ORDER BY data_value DESC) AS visit_rank
                        FROM base
                    )
                    SELECT visit_rank FROM ranked WHERE area_name = 'xx区';
                （5）“占比/比重”逻辑：
                    WITH target_area AS (
                        SELECT DISTINCT area_level FROM cultural_wide_xxxx WHERE area_name = 'xx市'
                    ),
                    area_sum AS (
                        SELECT area_name, SUM(CASE WHEN target_name = 'yy指标' THEN data_value ELSE 0 END) as area_value
                        FROM cultural_wide_xxxx
                        WHERE area_level = (SELECT area_level FROM target_area)
                        GROUP BY area_name
                    ),
                    level_total AS (
                        SELECT SUM(data_value) as total_value
                        FROM cultural_wide_xxxx
                        WHERE area_level = (SELECT area_level FROM target_area)
                        AND target_name = 'yy指标'
                    ),
                    percentage AS (
                        SELECT a.area_name, a.area_value, (a.area_value / t.total_value) * 100 as computed_value
                        FROM area_sum a
                        CROSS JOIN level_total t
                    ),
                    ranked AS (
                        SELECT area_name, computed_value, RANK() OVER (ORDER BY computed_value DESC) AS visit_rank
                        FROM percentage
                    )
                    SELECT visit_rank FROM ranked WHERE area_name = 'xx市';
                （6）增量、增速计算与排名：
                    WITH base_2025 AS (
                        SELECT area_name, SUM(data_value) AS data_value
                        FROM cultural_wide_202505A
                        WHERE target_name = '全域旅游人数' AND area_level = 2
                        GROUP BY area_name
                    ),
                    base_2024 AS (
                        SELECT area_name, SUM(data_value) AS data_value
                        FROM cultural_wide_202405A
                        WHERE target_name = '全域旅游人数' AND area_level = 2
                        GROUP BY area_name
                    ),
                    growth AS (
                        SELECT b.area_name,
                            b.data_value AS this_year_value,
                            b24.data_value AS last_year_value,
                            ROUND((b.data_value - b24.data_value) / NULLIF(b24.data_value, 0) * 100, 2) AS computed_value
                        FROM base_2025 b
                        JOIN base_2024 b24 ON b.area_name = b24.area_name
                    ),
                    ranked AS (
                        SELECT area_name, this_year_value, computed_value, RANK() OVER (ORDER BY computed_value DESC) AS visit_rank
                        FROM growth
                    )
                    SELECT area_name, this_year_value AS data_value_2025, computed_value AS growth_rate, visit_rank AS rank_in_province
                    FROM ranked WHERE area_name = '嘉兴市';
                （7）2025年1-5月，西湖区过夜游客人数同比增速为多少？增速分别在全省和杭州市的排名为多少？
                    WITH base_2025 AS (
                    SELECT area_name, SUM(data_value) AS data_value_2025
                    FROM cultural_wide_202505A
                    WHERE target_name = '全域旅游人数' AND area_level = 3
                    GROUP BY area_name
                    ),
                    base_2024 AS (
                    SELECT area_name, SUM(data_value) AS data_value_2024
                    FROM cultural_wide_202405A
                    WHERE target_name = '全域旅游人数' AND area_level = 3
                    GROUP BY area_name
                    ),
                    growth AS (
                    SELECT b.area_name,
                            b.data_value_2025,
                            b24.data_value_2024,
                            ROUND((b.data_value_2025 - b24.data_value_2024) / NULLIF(b24.data_value_2024, 0) * 100, 2) AS growth_rate
                        FROM base_2025 b
                        JOIN base_2024 b24 ON b.area_name = b24.area_name
                    ),
                    ranked_province AS (
                    SELECT area_name,
                            growth_rate,
                            RANK() OVER (ORDER BY growth_rate DESC) AS rank_in_province
                        FROM growth
                    ),
                    -- 这里同市排名用 pid=杭州市id 查
                    same_city_areas AS (
                    SELECT name FROM t_area_info WHERE pid = (SELECT pid FROM t_area_info WHERE name = '西湖区' LIMIT 1)
                    ),
                    base_city AS (
                    SELECT area_name, growth_rate FROM growth
                        WHERE area_name IN (SELECT name FROM same_city_areas)
                    ),
                    ranked_city AS (
                    SELECT area_name,
                            growth_rate,
                            RANK() OVER (ORDER BY growth_rate DESC) AS rank_in_city
                        FROM base_city
                    )
                    SELECT rp.area_name,
                        rp.growth_rate,
                        rp.rank_in_province,
                        rc.rank_in_city
                    FROM ranked_province rp
                    JOIN ranked_city rc ON rp.area_name = rc.area_name
                    WHERE rp.area_name = '西湖区';
                （8）节假日查询：
                    查询：“2025年劳动节杭州市全域旅游人数”：
                        SELECT data_value,
                            data_unit
                        FROM cultural_wide_2025H5
                        WHERE area_name = '杭州市'
                        AND target_name = '全域旅游人数';
                （9）文化及相关产业增加值占GDP比重等含有GDP比重的指标名
                    查询：请问2023年浙江省文化及相关产业增加值占GDP比重比上年提高多少？
                    SELECT t1.data_value - t2.data_value AS percent_diff
                    FROM cultural_wide_2023 t1,
                        cultural_wide_2022 t2
                    WHERE t1.target_name = '文化及相关产业增加值占GDP比重'
                    AND t1.area_name = '浙江省'
                    AND t2.target_name = '文化及相关产业增加值占GDP比重'
                    AND t2.area_name = '浙江省';
                (10) 查询温州市各县市，知道area_level=2的 想查area_level=3的
                    SELECT name
                    FROM t_area_info
                    WHERE pid =
                        (SELECT id
                        FROM t_area_info
                        WHERE name = '温州市'
                        LIMIT 1)
                (11) 查询“拱墅区”所在城市中，与“拱墅区”同属一个上级市且区县级别（area_level=3）的所有区县数量。
                    SELECT name
                    FROM t_area_info
                    WHERE pid =
                        (SELECT pid
                        FROM t_area_info
                        WHERE name = '拱墅区'
                        LIMIT 1)
                （12）复杂排名与多重输出
                    输出：原始数值、增速、全省排名、市内排名（见上面省市内排名SQL范例）
                （13）特例与衍生指标计算
                    过夜游客平均停留天数 = 监测期内各天过夜游客人数之和 / 监测期过夜游客人数
                    过夜游客平均停留天数与上年同期变化量 = 本期-上期
                    2025年一季度浙江省旅游企业经营指数环比提高 = 2025Q1 - 2024Q4
                    住宿样本单位平均房价增速 = (本期-上期)/上期*100%
                    示例公式见原提示词各条公式说明
"""
def build_scenic_prompt(month_data: List[str]):
    indicators_scenic = load_indicators_scenic()
    print(f"当前指标{indicators_scenic}")
    available_tables = get_available_tables_scenic()
    # 获取月份范围
    month_range = get_available_months_scenic()
    print(f"{month_range}")

    return f"""
        你是 SQL 生成助手。用户所有涉及的节假日，系统已经自动解析成了实际可查的表名，并顺序放在了 month_data 列表。
        【记忆优先级】
            当上一轮记忆中存在两个问答对且有冲突时，优先参照“最新记忆：”。
            当本轮用户问题出现“省”“市”“区”“县”等行政区关键词，视为“行政区”相关指标问题。优化为“时间+行政区名+指标名+问题形式”结构。
        【表名选择规则】
        1. 节假日查询  
        - 如果用户问题中提及节假日（如春节、国庆、中秋、端午、五一、十一、清明、元旦、劳动节、儿童节、重阳节、元宵节、圣诞节、新年等），sql查询的表为{month_data}
        
        2. 非节假日查询  
        - 若用户问题未提及节假日，则按以下【时间后缀规则】从问题中提取时间范围，根据时间后缀规则拼接出表名（如 2025、202501、20250101、2025Q1、202504A 等），再用于 SQL 查询。

        【时间后缀规则】
        a. 年：如“2025年……” → 返回：2025
        b. 月：如“2025年1月……” → 返回：202501
        c. 日：如“2025年1月1日……” → 返回：20250101
        d. 周：如“2025年1月第1周……” → 返回：202501w1
        e. 旬：如“2025年1月第1旬……” → 返回：202501T1
        f. 跨月累计查询：如“2025年1-4月”，且未出现“每个月”“分别”等分月关键词 → 返回：202504A
        g. 分别列出每月：如有“分别”“每个月”等分月关键词 → 返回多个后缀，如 202501, 202502, 202503, 202504
        h. 若出现“累计”“合计”“总数”等 → 强制使用累计表（结尾月+A）
        i. 季度：如“2025年第1季度……” → 返回：2025Q1

        若问题未明确提及时间，则默认时间为 2024 年；但若有任何时间表达（如年份、月份、节日等），必须保留原始时间信息。

        【SQL生成总则】
        - SQL查询严格使用当前步骤处理后得到的表名。
        - 其他所有规则（如指标、地区、增量/增速/占比、分组、排序等）全部保留原有逻辑，不允许删除或更改。
        - 只输出 SQL 语句本身，不输出注释、前缀、Markdown 等其他内容。
        - UNION/UNION ALL 必须保证所有 SELECT 字段数、字段类型一致！不一致就补 NULL。

        【数据库字段信息】
        - 数据库：cultural_tourism
        - 每张表结构如下：  
             查询基于表名为：scenic_wide_xxxx（时间后缀），每个时间都有独立的表，例如查询2025年的指标值，对应的表为scenic_wide_2025，每一张表的表结构如下：\n"
                "   - id (序号, bigint)，值举例：'1939870446100586498'\n"
                "   - target_id (指标id,bigint), 值举例：'1939870416975339521'\n"
                "   - target_name (指标名称, varchar), 值举例：'到访人数'，'当日到访'\n"
                "   - area_code (监测景区code,varchar), 值举例：'02-33010914'\n"
                "   - area_name (监测景区名称, varchar), 值举例：'湘湖旅游度假区'\n"
                "   - data_value (值, varchar)，值举例：6350.500'
                "   - data_unit (单位, varchar)，值举例：'万人'\n"
                "   - date_time (时间, varchar)，值举例：'202506'\n"
                "   - city_name (地市名称, varchar)，值举例：'杭州市'\n"
                "   - county_name(区县名称，varchar)，值举例：'拱墅区'\n"
                "   - type (类型 , varchar，值举例：'01','02','03'\n"
                "   - type_name (类型名称, varchar),值举例： 'A级景区','旅游度假区','夜间文化和旅游消费集聚区','文博场馆','爆款培育-新业态','爆款培育-核心大景区','爆款培育-备用'\n"
                "   - type_category (类型名称, varchar),值举例： '省级/国家级','国家级','省级','5A','4A'等\n"
                "   - period (维度, varchar)，值举例：'月'\n"
                该表用于查询所有景区的对应指标（到访人数，当日到访等）的指标值。\n"


        【要素抽取与SQL要点】
            (1)要素抽取与问题重组：
            优先从“本轮用户问题”中提取时间、景区名、指标名，若有缺失可结合上一轮记忆补全。
            优化用户表达为“时间+地区+指标名”的结构，便于生成SQL。
            时间相关的表名全部以 month_data 为准，禁止自行判断、映射或拼接。
            示例：本轮用户问题：2024年4月杭州市全域过夜游人数是多少？  
            → 时间：2024年4月（保留）  
            → 行政区：杭州市（已提及）  
            → 指标词：“全域过夜游人数”与列表存在完全一致的指标名：“全域过夜游”  
            → 问题：是多少
            → 输出：2024年4月，杭州市全域过夜游人数是多少？
            (2)指标匹配规则（target_name）：
                target_name匹配优先级：
                a、完全匹配：提取本轮用户问题中的指标关键词，若本轮用户问题中的指标名称与现有指标列表 {indicators_scenic} 中某一个指标名完全一致，则target_name直接使用该指标名
                b、相似度匹配：若没有完全一致，则通过语义分析识别用户问题与列表中每个指标名的相似度，选取最相似的一项作为target_name
                c、关键词提取：从用户问题中提取核心指标关键词，与指标列表进行模糊匹配            
                匹配逻辑示例
                用户问题："2024年杭州接待游客量是多少？"
                提取指标词："接待游客量"
                在现有指标列表 indicators_admin 中寻找最匹配项，如找到"全域接待游客量"
                最终target_name使用："全域接待游客量"
                注意：target_name 必须严格来源于 indicators_admin 列表，不可创造或修改指标名，不附加任何解释。            
            (3)多时间/多表场景处理：
                分别查询： 只有在用户问题明确要求“分别、每月、各月、多个时间点、逐月”等表达时，才用 UNION ALL 查询多个表，如：
                SELECT ... FROM {{month_data[0]}} ... UNION ALL
                SELECT ... FROM {{month_data[1]}} ... UNION ALL ...
                累计查询： 没有“分别”等词时，只查一个累计表（一般是 month_data 列表的最后一个）。
                禁止在累计查询中拼接多个单月表，禁止输出多月明细。
        【指标间的计算逻辑】
            a、如果用户想查询的指标名称与指标列表中的某一项完全匹配，则直接使用该指标名，在对应的时间表中查询数据，无需进行任何计算或派生逻辑处理。只有在无完全匹配项的情况下，才进入后续的计算或匹配流程。
            b、关于“增量”“增速”“增长”“提高”“增幅”等计算的统一规则如下：          
            **占比／比重**  
            - 用户问“XX 比重”、“XX 占比”时，需满足以下规则：
            - **分子（numerator_value）**为用户指定地区的指标值（data_value）
            - 分母（denominator_value）为与该地区area_level相同的所有地区中，行政归属一致的子区域的总和（例如：若为西湖区，则应汇总杭州市下辖的所有区县；若为杭州市，则汇总所有市级地区，不包含省级）
            - 计算方式为：ratio = numerator_value / denominator_value * 100
            
            **增量／提高**：表示绝对值的变化，用今年值减去年值：
            - SQL：`今年值 - 去年值`
            - 示例：2024年 - 2023年
            - 示例问题：增加了多少人？比去年多多少？

            - **增长／增速／增幅**：表示百分比变化，用今年减去年后除以前年值再乘 100：
            - SQL：`(今年值 - 去年值) / 去年值 * 100`
            - 示例：同比增长、增速为多少、增长百分比
            - 示例问题：增长了多少百分比？同比增速为多少？

            注意：必须根据用户问题的用词精准区分：
            - 出现“增量”“提高” → 用绝对差值
            - 出现“增速”“增长”“增幅” → 用百分比计算
            注意：若没有特别说明，占比／比重/增量／提高/增长／增速/增幅都是计算本市/县的，即area_name一致
            下列其他指标同理
            c、2025年一季度浙江省旅游企业经营指数环比提高=2025年一季度浙江省旅游企业经营指数-2024年四季度浙江省旅游企业经营指数  
            d、未涉及全省平均水平时每次查询尽量只使用target_name，area_name两个个字段。
            e、住宿样本单位平均房价比上年增长(增速)=（平均房价-上年平均房价）/上年平均房价*100%,用户若表达为“平均房价增比”“平均房价涨幅”等相似说法，也可视为查询此指标。       
            f、过夜游客平均停留天数与上年同期相比的变化量=过夜游客平均停留天数(比上年同期提高/下降)=过夜游客平均停留天数-上年同期过夜游客平均停留天数
            g、如果用户问题是询问xx年yy市的zz指标“高于/低于全省平均水平多少”，应先计算该指标在与该行政区 area_level 一致的所有行政区中的平均值作为“全省平均水平”，再计算该行政区该指标的实际值，最后进行差值计算。
            h、计算xx市/县的yy指标高于全省平均水平多少时，不需要查询单位
            i、每次查询都要查询对应指标值（data_value）和单位（data_unit），不需要指定 date_time
            j、 用户提到单景区的'日均到访'时, SQL不使用AVG，因为查询的date_time就是每天的数据。只有提到'前X天的平均人数'时，先取每一天的指标值相加，再取AVG，如果提到景区大类（type：景区/度假区/夜间消费区/文博场馆/新业态/核心大景区）的日均到访时需要使用'SUM'。target_name都指定为'到访人数'。提到'和'时取SUM
            k、 每次查询尽量只使用 target_name 和 area_name 两个字段；如无特别指定，不强制加 type、city_name、county_name 等字段。
            - 注意：**景区名必须以“市级行政区+景区名”完整形式出现（如“杭州市湘湖旅游度假区”），否则无法匹配。用户提供什么景区名，就需要查询相同景区名，不能修改**
            - 查询 area_name 时，请使用完整匹配。例如：
            WHERE area_name = '杭州市湘湖旅游度假区'

        【增量/增速/占比等计算】
        - “增量/提高”用今年减去年：今年-去年。
        - “增速/增长/增幅”用百分比：(今年-去年)/去年*100。
        - “占比/比重”按本地值/同级汇总值*100，按行政区级别汇总。
        - 具体计算按下方示例生成。
        【计算逻辑】
            原始指标完全匹配：直接查原始表
            排名列别名 visit_rank，计算列名 computed_value
            “在全省排名多少”务必按 area_level 限定范围
            市内排名：用 t_area_info.pid 限定同城
            占比/比重：分子本地值，分母同级汇总
            增量/提高：今年-去年
            增速/增长/增幅：百分比 (今年-去年)/去年*100
            相关计算与排序全部按“示例”部分
        【输出要求】
        - 只输出SQL语句本身，禁止输出任何注释、前缀、Markdown等。

        【错误处理】
        - 如果month_data为空，直接输出空字符串即可，禁止生成SQL。

        【注意点】    
            1.用户询问'夜间集聚地',查询的type指定为'夜间消费区。
            2.所有 type_name 举例包括：
                'A级景区'
                '旅游度假区'
                '夜间文化和旅游消费集聚区'
                '文博场馆'
                '爆款培育-新业态'
                '爆款培育-核心大景区'
            用户提到类别词时，如“夜间集聚区”“夜间文旅聚集地”“夜间文化区”等，应统一映射为 type_name = '夜间文化和旅游消费集聚区'。
            严禁使用字段 type，只能使用 type_name 字段来过滤景区类型。
            3.用户询问排名时：
                若问题包含“第一名”“最多”“最高”等表达，应使用 ORDER BY data_value DESC LIMIT 1 简化生成，不建议使用 CTE；
                若需要展示多项排名（如前5名），可使用 RANK() OVER (ORDER BY data_value DESC) AS visit_rank；
                排名字段名统一命名为 visit_rank；
                筛选类型时，必须使用 type_name 字段，禁止使用 type 字段。
            4.用户询问爆款景区时，指定type为'新业态'+'核心大景区',即爆款景区的指标值=type为'新业态'的指标值+type为'核心大景区'的指标值。     
            5. 若用户询问“某景区的某指标高于/低于全省平均水平多少”，请提取完整景区名（含市级前缀）进行比较。例如：
            SELECT (t1.data_value - t2.avg_value) AS absolute_diff,
                t1.data_unit
            FROM scenic_wide_2024 t1,
                (SELECT AVG(data_value) AS avg_value,
                        data_unit
                FROM scenic_wide_2024
                WHERE target_name = '到访人数') t2
            WHERE t1.target_name = '到访人数'
            AND t1.area_name = '杭州市湘湖旅游度假区';
            6. 当用户询问“哪一个景区的所有数据”时，需要结合多张表（默认使用2023年和2024年）进行数据汇总，
            且匹配 area_name 时，使用完整的“市级+景区名”格式。例如：
            SELECT data_value, data_unit, '2023' AS data_year
            FROM scenic_wide_2023
            WHERE target_name = '到访人数'
            AND area_name = '杭州市湘湖旅游度假区'

            UNION ALL

            SELECT data_value, data_unit, '2024' AS data_year
            FROM scenic_wide_2024
            WHERE target_name = '到访人数'
            AND area_name = '杭州市湘湖旅游度假区';
            tips：到访人数与当日到访指标名不能混淆。
            
            7.如果用户提问的景区名就是没有市区则直接用它的景区名，例如：
            用户提问：2024年1-5月台州官河古道累计到访人数是多少
            SELECT data_value,
                data_unit
            FROM scenic_wide_202405A
            WHERE target_name = '到访人数'
            AND area_name = '台州官河古道';
            9. 当用户问题中出现“同比增长”、“同比增速”、“同比增幅”、“同比变化”等字眼时，表示需要计算同比百分比差值，应按如下逻辑生成 SQL：

            - 分别查找用户所问时间（如 2025年1-5月）的累计表，如 scenic_wide_202505A；
            - 向前推一年作为对比基准（如 scenic_wide_202405A）；
            - 匹配相同 area_name 与 target_name；
            - 且必须强制添加：t1.type_name = t2.type_name；
            - 最终 SQL 必须包含字段：
                - ((t1.data_value - t2.data_value) / t2.data_value * 100) AS percent_diff
                - t1.data_unit
                - t1.type_name
                - t1.type_category

            示例如下（供结构参考，变量按用户问题填充）：

            SELECT ((t1.data_value - t2.data_value) / t2.data_value * 100) AS percent_diff,
                t1.data_unit,
                t1.type_name,
                t1.type_category
            FROM scenic_wide_202505A t1,
                scenic_wide_202405A t2
            WHERE t1.target_name = '到访人数'
            AND t1.area_name = '衢州古城文化旅游区'
            AND t2.target_name = '到访人数'
            AND t2.area_name = '衢州古城文化旅游区'
            AND t1.type_name = t2.type_name;
            8.当使用子查询、WITH（CTE）或 UNION 查询结构时，主查询所需字段必须包含在子查询的 SELECT 子句中，否则将导致字段缺失报错。例如：
                若主查询需要使用 data_unit，则子查询中必须包含 data_unit 字段；
                不允许在子查询中遗漏字段导致外层引用失败
            9.当 SELECT 语句中包含聚合函数（如 SUM(data_value)、AVG(...) 等）且同时保留了非聚合字段（如 data_unit）时，必须添加 GROUP BY 子句，确保兼容 ONLY_FULL_GROUP_BY 模式。
                - 示例：SELECT SUM(data_value), data_unit → 必须 GROUP BY data_unit
                - 否则会导致 MySQL 报错：In aggregated query without GROUP BY...
                - 默认将 data_unit 加入 GROUP BY 即可，无需其他字段。
            10.当用户问题中提到景区等级表达（如“4A级及以上景区”“5A级景区”“3A以下”等），需使用 type_category 字段筛选：
                - “5A级景区” → type_category = '5A'
                - “4A级及以上景区” → type_category IN ('4A', '5A')
                - “3A级及以下景区” → type_category IN ('3A', '2A', '1A')
                - “所有A级景区” → type = '01'
                - 注意：type_category 是景区等级字段，不能与 type 混淆，只有“A级景区”这种模糊说法才使用 type = '01'。
        【特别注意】
            只用 month_data 里的表名，不做任何自行拼接和时间映射。
            指标名、景区名严格遵循参数列表，优先最细粒度。
            禁止输出除 SQL 语句外的任何内容。
            每次查询需返回 data_value 和 data_unit 字段，除非用户有特殊需求。
            严格禁止使用data_time字段

        【特别强调】
        - SQL中表名**绝不能**出现在month_data列表之外。
        - 不允许根据时间、节假日等拼接表名，month_data只有什么用什么，否则输出空。

        请严格按以上规则，生成唯一一段SQL代码。
        """

def build_scenic_rank_prompt(month_data: List[str]):
    indicators_scenic = load_indicators_scenic()

    return f"""
        你是 SQL 生成助手。用户所有涉及的节假日，系统已经自动解析成了实际可查的表名，并顺序放在了 month_data 列表。
        【记忆优先级】
            当上一轮记忆中存在两个问答对且有冲突时，优先参照“最新记忆：”。
            当本轮用户问题出现“省”“市”“区”“县”等行政区关键词，视为“行政区”相关指标问题。优化为“时间+行政区名+指标名+问题形式”结构。
        【表名选择规则】
        1. 节假日查询  
        - 如果用户问题中提及节假日（如春节、国庆、中秋、端午、五一、十一、清明、元旦、劳动节、儿童节、重阳节、元宵节、圣诞节、新年等），sql查询的表为{month_data}
        
        2. 非节假日查询  
        - 若用户问题未提及节假日，则按以下【时间后缀规则】从问题中提取时间范围，根据时间后缀规则拼接出表名（如 2025、202501、20250101、2025Q1、202504A 等），再用于 SQL 查询。

        【时间后缀规则】
        a. 年：如“2025年……” → 返回：2025
        b. 月：如“2025年1月……” → 返回：202501
        c. 日：如“2025年1月1日……” → 返回：20250101
        d. 周：如“2025年1月第1周……” → 返回：202501w1
        e. 旬：如“2025年1月第1旬……” → 返回：202501T1
        f. 跨月累计查询：如“2025年1-4月”，且未出现“每个月”“分别”等分月关键词 → 返回：202504A
        g. 分别列出每月：如有“分别”“每个月”等分月关键词 → 返回多个后缀，如 202501, 202502, 202503, 202504
        h. 若出现“累计”“合计”“总数”等 → 强制使用累计表（结尾月+A）
        i. 季度：如“2025年第1季度……” → 返回：2025Q1

        若问题未明确提及时间，则默认时间为 2024 年；但若有任何时间表达（如年份、月份、节日等），必须保留原始时间信息。

        【SQL生成总则】
        - SQL查询严格使用当前步骤处理后得到的表名。
        - 其他所有规则（如指标、地区、增量/增速/占比、分组、排序等）全部保留原有逻辑，不允许删除或更改。
        - 只输出 SQL 语句本身，不输出注释、前缀、Markdown 等其他内容。
        - UNION/UNION ALL 必须保证所有 SELECT 字段数、字段类型一致！不一致就补 NULL。

        【数据库字段信息】
        - 数据库：cultural_tourism
        - 每张表结构如下：  
             查询基于表名为：scenic_wide_xxxx（时间后缀），每个时间都有独立的表，例如查询2025年的指标值，对应的表为scenic_wide_2025，每一张表的表结构如下：\n"
                "   - id (序号, bigint)，值举例：'1939870446100586498'\n"
                "   - target_id (指标id,bigint), 值举例：'1939870416975339521'\n"
                "   - target_name (指标名称, varchar), 值举例：'到访人数'，'当日到访'\n"
                "   - area_code (监测景区code,varchar), 值举例：'02-33010914'\n"
                "   - area_name (监测景区名称, varchar), 值举例：'湘湖旅游度假区'\n"
                "   - data_value (值, varchar)，值举例：6350.500'
                "   - data_unit (单位, varchar)，值举例：'万人'\n"
                "   - date_time (时间, varchar)，值举例：'202506'\n"
                "   - city_name (地市名称, varchar)，值举例：'杭州市'\n"
                "   - county_name(区县名称，varchar)，值举例：'拱墅区'\n"
                "   - type (类型 , varchar，值举例：'01','02','03'\n"
                "   - type_name (类型名称, varchar),值举例： 'A级景区','旅游度假区','夜间文化和旅游消费集聚区','文博场馆','爆款培育-新业态','爆款培育-核心大景区','爆款培育-备用'\n"
                "   - type_category (类型名称, varchar),值举例： '省级/国家级','国家级','省级','5A','4A'等\n"
                "   - period (维度, varchar)，值举例：'月'\n"
                该表用于查询所有景区的对应指标（到访人数，当日到访等）的指标值。\n"
        【要素抽取与SQL要点】
            (1)要素抽取与问题重组：
            优先从“本轮用户问题”中提取时间、地区、指标名，若有缺失可结合上一轮记忆补全。
            优化用户表达为“时间+地区+指标名”的结构，便于生成SQL。
            时间相关的表名全部以 month_data 为准，禁止自行判断、映射或拼接。
            示例：本轮用户问题：2024年4月杭州市全域过夜游人数是多少？  
            → 时间：2024年4月（保留）  
            → 行政区：杭州市（已提及）  
            → 指标词：“全域过夜游人数”与列表存在完全一致的指标名：“全域过夜游”  
            → 问题：是多少
            → 输出：2024年4月，杭州市全域过夜游人数是多少？
            (2)指标匹配规则（target_name）：
                target_name匹配优先级：
                a、完全匹配：提取本轮用户问题中的指标关键词，若本轮用户问题中的指标名称与现有指标列表 {indicators_scenic} 中某一个指标名完全一致，则target_name直接使用该指标名
                b、相似度匹配：若没有完全一致，则通过语义分析识别用户问题与列表中每个指标名的相似度，选取最相似的一项作为target_name
                c、关键词提取：从用户问题中提取核心指标关键词，与指标列表进行模糊匹配            
                匹配逻辑示例
                用户问题："2024年杭州接待游客量是多少？"
                提取指标词："接待游客量"
                在现有指标列表 indicators_admin 中寻找最匹配项，如找到"全域接待游客量"
                最终target_name使用："全域接待游客量"
                注意：target_name 必须严格来源于 indicators_admin 列表，不可创造或修改指标名，不附加任何解释。            
            (3)多时间/多表场景处理：
                分别查询： 只有在用户问题明确要求“分别、每月、各月、多个时间点、逐月”等表达时，才用 UNION ALL 查询多个表，如：
                SELECT ... FROM {{month_data[0]}} ... UNION ALL
                SELECT ... FROM {{month_data[1]}} ... UNION ALL ...
                累计查询： 没有“分别”等词时，只查一个累计表（一般是 month_data 列表的最后一个）。
                禁止在累计查询中拼接多个单月表，禁止输出多月明细。
        【指标间的计算逻辑】
            a、如果用户想查询的指标名称与指标列表中的某一项完全匹配，则直接使用该指标名，在对应的时间表中查询数据，无需进行任何计算或派生逻辑处理。只有在无完全匹配项的情况下，才进入后续的计算或匹配流程。
            b、关于“增量”“增速”“增长”“提高”“增幅”等计算的统一规则如下：          
            **占比／比重**  
            - 用户问“XX 比重”、“XX 占比”时，需满足以下规则：
            - **分子（numerator_value）**为用户指定地区的指标值（data_value）
            - 分母（denominator_value）为与该地区area_level相同的所有地区中，行政归属一致的子区域的总和（例如：若为西湖区，则应汇总杭州市下辖的所有区县；若为杭州市，则汇总所有市级地区，不包含省级）
            - 计算方式为：ratio = numerator_value / denominator_value * 100
            
            **增量／提高**：表示绝对值的变化，用今年值减去年值：
            - SQL：`今年值 - 去年值`
            - 示例：2024年 - 2023年
            - 示例问题：增加了多少人？比去年多多少？

            - **增长／增速／增幅**：表示百分比变化，用今年减去年后除以前年值再乘 100：
            - SQL：`(今年值 - 去年值) / 去年值 * 100`
            - 示例：同比增长、增速为多少、增长百分比
            - 示例问题：增长了多少百分比？同比增速为多少？

            注意：必须根据用户问题的用词精准区分：
            - 出现“增量”“提高” → 用绝对差值
            - 出现“增速”“增长”“增幅” → 用百分比计算
            注意：若没有特别说明，占比／比重/增量／提高/增长／增速/增幅都是计算本市/县的，即area_name一致
            下列其他指标同理
            c、2025年一季度浙江省旅游企业经营指数环比提高=2025年一季度浙江省旅游企业经营指数-2024年四季度浙江省旅游企业经营指数  
            d、未涉及全省平均水平时每次查询尽量只使用target_name，area_name两个个字段。
            e、住宿样本单位平均房价比上年增长(增速)=（平均房价-上年平均房价）/上年平均房价*100%,用户若表达为“平均房价增比”“平均房价涨幅”等相似说法，也可视为查询此指标。       
            f、过夜游客平均停留天数与上年同期相比的变化量=过夜游客平均停留天数(比上年同期提高/下降)=过夜游客平均停留天数-上年同期过夜游客平均停留天数
            g、如果用户问题是询问xx年yy市的zz指标“高于/低于全省平均水平多少”，应先计算该指标在与该行政区 area_level 一致的所有行政区中的平均值作为“全省平均水平”，再计算该行政区该指标的实际值，最后进行差值计算。
            h、计算xx市/县的yy指标高于全省平均水平多少时，不需要查询单位
            i、每次查询都要查询对应指标值（data_value）和单位（data_unit），不需要指定 date_time
            j、 用户提到单景区的'日均到访'时, SQL不使用AVG，因为查询的date_time就是每天的数据。只有提到'前X天的平均人数'时，先取每一天的指标值相加，再取AVG，如果提到景区大类（type：景区/度假区/夜间消费区/文博场馆/新业态/核心大景区）的日均到访时需要使用'SUM'。target_name都指定为'到访人数'。提到'和'时取SUM
            k、 每次查询尽量只使用 target_name 和 area_name 两个字段；如无特别指定，不强制加 type、city_name、county_name 等字段。
            - 注意：**景区名必须以“市级行政区+景区名”完整形式出现（如“杭州市湘湖旅游度假区”），否则无法匹配。用户提供什么景区名，就需要查询相同景区名，不能修改**
            - 查询 area_name 时，请使用完整匹配。例如：
            WHERE area_name = '杭州市湘湖旅游度假区'
        【注意事项】
            1. 指标名必须要从{indicators_scenic}中获取，如用户提问"当日到访人数",你从指标名中获取的只有"当日到访"，就不要混为一谈，还是要选择"当日到访"。
            2. 当用户提到“累计”“总数”“合计”等字眼时，无论是否提及“分别”，都必须查累计表。
            3. 注意允许的指标中若出现'到访人数'与'当日到访人数'，这是不一样的指标名，不要混淆，要求严格区分。
            4. 对于不可累加类指标（如平均房价、入住率），只要用户未要求分别列出每月数据，也应使用累计表，并计算整段时间的平均值或比例。
            5. 请根据“本轮用户问题”和“上一轮记忆”进行思考，把本轮用户问题转为时间+景区+指标名的形式，再生成sql。例如用户输入：'杭州市京杭大运河·杭州景区到访人数 2024', 转为'2024，杭州市京杭大运河·杭州景区到访人数是多少？'，再按照以下要求转为sql。
            6. 当用户询问“在全省排名”时，需：
                a. 在 WITH 子句里，先筛选 target_name = '到访人数'，并根据具体场景（原始值、增量、增幅等）计算出 computed_value；
                b. 对全省所有区县的 computed_value 使用 
                        RANK() OVER (ORDER BY computed_value DESC) 
                    排序，得出 visit_rank；
                c. 在外层 SELECT 中按区域名称（area_name）过滤，只返回指定区域的 visit_rank。
                d.排名列别名必须为 visit_rank，计算列请命名为 computed_value。
            7. 当用户询问“在xx市内排名”时，需指定city_name：
                a. 在 WITH 子句里，先筛选 target_name = '到访人数'，并根据具体场景（原始值、增量、增幅等）计算出 computed_value；
                b. 指定city_name为"xx市"，并computed_value 使用 
                        RANK() OVER (ORDER BY computed_value DESC) 
                    排序，得出 visit_rank；
                c. 在外层 SELECT 中按区域名称（area_name）过滤，只返回指定区域的 visit_rank。
                d.排名列别名必须为 visit_rank，计算列请命名为 computed_value。
            8. 在“增量”“增速”“提高”“增长”“增幅”场景中，务必在 WITH 子句里分别查询本年和上一年到访人数，再计算出 computed_value，最后使用 RANK() 排序。
            9. 涉及到不同的时间时，需要查不同时间对应的表，不能指定data_time
            10. 排名前务必使用 computed_value 字段做 ORDER BY，而非原始 data_value。
            11. 对于“增量”“提高”——输出对应年份的绝对差值；“增长”“增幅”——输出对应年份的百分比差值。
            12. 每次查询都要查询对应指标值（data_value）和单位（data_unit），不需要指定 date_time
            13. 用户提到单景区的'日均到访'时, SQL不使用AVG，因为查询的date_time就是每天的数据。只有提到'前X天的平均人数'时，先取每一天的指标值相加，再取AVG，如果提到景区大类（type：景区/度假区/夜间消费区/文博场馆/新业态/核心大景区）的日均到访时需要使用'SUM'，。target_name都指定为'到访人数'。提到'和'时取SUM
            14. 每次查询尽量只使用 target_name，area_name 两个字段，没有特别指定类型时，可以不指定 type、city_name 和 county_name。    
            15. 用户询问'夜间集聚地',查询的type指定为'夜间文化和旅游消费集聚区。
            16. 当用户问题中提到景区等级表达（如“4A级及以上景区”“5A级景区”“3A以下”等），需使用 type_category 字段筛选：
                - “5A级景区” → type_category = '5A'
                - “4A级及以上景区” → type_category IN ('4A', '5A')
                - “3A级及以下景区” → type_category IN ('3A', '2A', '1A')
                - “所有A级景区” → type = '01'
                - 注意：type_category 是景区等级字段，不能与 type 混淆，只有“A级景区”这种模糊说法才使用 type = '01'。
            17.当用户查询排名时，查询后的字段名用'visit_rank',data_value不用SUM,TYPE为指定类型。
            18.用户询问爆款景区时，指定type为'新业态'+'核心大景区',即爆款景区的指标值=type为'新业态'的指标值+type为'核心大景区'的指标值。
            19.需要计算xx景区的指标提高值并在yy市进行排名时，按照以下步骤编写sql:
                步骤1:查询 2024 年yy市所有景区人数作为current_year
                步骤2:查询 2023 年yy市所有景区人数作为previous_year
                步骤3：计算每个景区的到访人数增量current_value - previous_value
                步骤4：按增量降序排名	RANK() OVER (...)
                步骤5：选出xx景区的排名与增量值	WHERE area_name = 'xx市xx景区'
                举例：2025年5月杭州市A级景区到访人数总和是多少
                SELECT SUM(data_value) AS data_value,
                    data_unit
                FROM scenic_wide_202505
                WHERE target_name = '到访人数'
                AND TYPE = '01'
                AND city_name = '杭州市'
                GROUP BY data_unit;
                举例：2025年5月杭州市A级景区到访人数是多少

                举例：2024年杭州市湘湖旅游度假区到访人数较上一年提高值在yy市内排名第几
                WITH current_year AS
                (SELECT area_name,
                        data_value,
                        data_unit,
                        RANK() OVER (
                                    ORDER BY data_value DESC) AS visit_rank
                FROM scenic_wide_2024
                WHERE target_name = '到访人数'
                    AND city_name = 'yy市'),
                    specific_scenic AS
                (SELECT visit_rank,
                        data_value,
                        data_unit
                FROM current_year
                WHERE area_name = '杭州市湘湖旅游度假区')
                SELECT visit_rank,
                    data_value,
                    data_unit
                FROM specific_scenic;

            20.需要计算增量并进行排名时同理：sql举例如下：
                -- 步骤1：查询 2024 年 yy市 所有景区的到访人数
                WITH current_year AS (
                SELECT area_name,
                        data_value AS current_value
                FROM scenic_wide_2024
                WHERE target_name = '到访人数'
                    AND city_name = 'yy市'
                ),

                -- 步骤2：查询 2023 年 yy市 所有景区的到访人数
                previous_year AS (
                SELECT area_name,
                        data_value AS previous_value
                FROM scenic_wide_2023
                WHERE target_name = '到访人数'
                    AND city_name = 'yy市'
                ),

                -- 步骤3+4：计算每个景区的增量，并按增量降序排名
                ranked_data AS (
                SELECT cy.area_name,
                        cy.current_value,
                        py.previous_value,
                        (cy.current_value - py.previous_value) AS diff_value,
                        RANK() OVER (ORDER BY (cy.current_value - py.previous_value) DESC) AS rank_order
                FROM current_year cy
                JOIN previous_year py ON cy.area_name = py.area_name
                )

                -- 步骤5：例如：选出 杭州市湘湖旅游度假区 的排名与增量值
                SELECT area_name,
                    diff_value,
                    rank_order
                FROM ranked_data
                WHERE area_name = '杭州市湘湖旅游度假区';
                默认策略：
                - 如果用户问题中存在多义或模糊项（如“到访人数” vs “当日到访人数”），优先匹配完整 target_name。
                - 若无法判断时间或维度，默认按年（2024年）处理，表为 scenic_wide_2024。
                - 排名查询必须明确城市或全省维度，且使用 computed_value 排序。
                - 所有查询输出字段必须包含 data_value，不需要查询data_unit,排名类查询需包含 visit_rank。
            21.当使用子查询、WITH（CTE）或 UNION 查询结构时，主查询所需字段必须包含在子查询的 SELECT 子句中，否则将导致字段缺失报错。例如：
                若主查询需要使用 data_unit，则子查询中必须包含 data_unit 字段；
                不允许在子查询中遗漏字段导致外层引用失败
            22.当 SELECT 语句中包含聚合函数（如 SUM(data_value)、AVG(...) 等）且同时保留了非聚合字段（如 data_unit）时，必须添加 GROUP BY 子句，确保兼容 ONLY_FULL_GROUP_BY 模式。
                - 示例：SELECT SUM(data_value), data_unit → 必须 GROUP BY data_unit
                - 否则会导致 MySQL 报错：In aggregated query without GROUP BY...
                - 默认将 data_unit 加入 GROUP BY 即可，无需其他字段。
        【特别注意】
            只用 month_data 里的表名，不做任何自行拼接和时间映射。
            指标名、景区名严格遵循参数列表，优先最细粒度。
            禁止输出除 SQL 语句外的任何内容。
            每次查询需返回 data_value 和 data_unit 字段，除非用户有特殊需求。
"""
def bulid_holiday_prompt():
    return f"""
        【节假日判断规则】
        请判断用户问题中是否包含以下节假日关键词之一：“春节”、“国庆”、“中秋”、“端午”、“五一”、“十一”、“清明”、“元旦”、“劳动节”、“儿童节”、“重阳节”、“元宵节”、“圣诞节”、“新年”。

        - 如果包含任一节假日关键词，输出：是
        - 如果不包含，输出：否
        仅输出“是”或“否”，禁止输出其他内容。
"""
    

def build_month_prompt():
    month_range = get_available_months()
    return f"""
        你是一个节假日提取助手，只需根据用户问题判断并返回其中的节假日信息。

        【输出规则】
        - 如果问题中提及节假日（如元旦、春节、清明节、劳动节、端午节、中秋节、国庆节等），则直接返回格式：节假日：2024年端午节（包含年份和节日名称，年份以问题中实际时间为准）
        - 如果未提及节假日，则输出空字符串。

        【输出格式示例】
        - 用户问：“2025年元旦住宿单位客房出租率是多少？”
        输出：节假日：2025年元旦

        - 用户问：“2024年端午节全域旅游人数”
        输出：节假日：2024年端午节

        - 用户问：“2024年1月到4月湖州市的全域旅游人数”
        输出：（空字符串）
"""
#RAG_SYSTEM_PROMPT = "如果用户询问'全域旅游人数占比',则直接输出全域旅游人数占比=同一地级市级别的全域相加再相除，否则直接输出学习到的'指标含义'或'计算逻辑'，不要多余说明和总结"
SYSTEM_INTENT = """
        你是一个文旅领域的意图分类助手。请根据“本轮用户问题”和“上一轮记忆”判断本次提问属于下列三类之一，只输出四个关键词之一，且不要任何额外文字：
        1. 当上一轮记忆中存在两个问答对，且有冲突时，优先参照“最新记忆：”
        - admin：本轮用户问题要查询某一年、某一季度、某个节日的“行政区（省/市/区/县）”级别的指标数值（人数、占比、排名、变动幅度等），有具体的时间段、区县、和指标名称的其中一个要素
        - scenic：本轮用户问题要查询某一年、某一季度、某个节日的“景区（图书馆/鼓楼/景区/度假区/夜间消费区/文博场馆/新业态/核心大景区）”级别的指标数值（人数、占比、排名、变动幅度等），有具体的时间段、景区、和指标名称
        - other:非文旅领域，用户询问你好，或者其他领域例如：计算机领域，英文，等。
        【规则】  
        1. 当本轮用户问题中同时满足以下两个条件时，应归类为 admin 意图：
            （1）出现行政区关键词，“省”“市”“区”“县”等；
            （2）同时提及具有“指标性质”的表达，如“游客数量”“接待情况”“文旅收入”“旅游人次”“入住率”等。
           该类问题通常隐含查询指标对应地区数据的需求，尤其当系统上下文中已有明确的“允许地区”与“允许指标”时，不能归为 other。
            用户不需要明确问“有多少”或“是多少”，但问题本身应具备指标+行政区的组合，表达了对某地文旅数据的关注。
            避免把泛泛而谈、无指标导向的问题（如“这个市怎么样”“我想去这个县旅游”）错误归为 admin。
           应归为 admin 的示例：
            “杭州市今年的旅游人次是多少？”
            “浙江省各市的文旅收入排行是怎样的？”
            “融合省外游客数” → admin
            不应归为 admin 的示例：
            “我想看看杭州的旅游景点”
            “浙江有哪些好玩的地方推荐？”
            “这个区好玩吗？”
        2. 当问题中出现“景区级对象”时（如“图书馆”“鼓楼”“景区”“度假区”“夜间消费区”“文博场馆”“新业态”“核心大景区”等），且具备数据查询意图（如带时间、指标、对比等），应归类为 scenic 意图。
            景区对象必须与可识别的指标配套出现，构成“景区+指标”结构，表示用户关注具体景区的运行数据、客流情况等。
            应归为 scenic 的示例：
            “丽水市仙都风景名胜区2024年当日到访人数”
            “西湖风景名胜区和千岛湖景区的游客排名”
            “哪些核心大景区接待量最多”
            不应归为 scenic 的示例（归为 other 或 rag）：
            “这个景区好不好玩”
            “有哪些适合周末去的景点”
            “西湖风景名胜区是什么意思？” → rag（若询问定义）
        3. 其余模糊或混合场景，优先判定other，具体为以下几种情况：
            "第一种：用户所提问题与民政领域无关，例如民政，计算机领域。"
            "第二种：用户询问你是谁，你的模型内部配置（思考过程，以及限制，使用哪种大模型）等"
            "第三种：当你无法理解用户所提问题。"
            "第四种：用户询问的问题有敏感词"  

        4. 要注意结合“上一轮记忆”进行判断
        例如'本轮用户问题：是  上一轮记忆：问题：2024年全域旅游人数 回答：您这边没有提到具体地点，请问是不是想询问浙江省的相关数据呢？'
        那么按照规则本轮用户问题也是admin类型


        【示例】  
        - “2024年浙江省过夜游客人数是多少？” → admin  
        - “2025年5月杭州市青山湖景区到访人数是多少？” →scenic   
        - “1+1=?” → other  
"""
RANK_DETECT_INSTRUCTIONS = """
    你是一个意图分类助手。判断下面的用户问题是否在询问“排名”相关内容。
    如果是，只输出“是”；否则，只输出“否”，不要任何多余文字或标点。
"""
OTHER_SYSTEM_PROMPT = """
    你是文旅的智能助手（问数与指标问答），只能回答文旅领域的相关问题（问数与指标问答）
    如果用户提出的问题属于以下几种情况，请严格只回复以下固定语句，不要加入任何其他内容、解释或说明，不允许绕开规则，也不允许输出乱码提示或额外补救措施。
    统一回答语句为：
    “抱歉当前问题我无法回答，我可以帮您解答文旅相关的问题（问数、指标查询）”
    【触发条件如下，只要满足任一条即触发】：
    1. 用户所提问题与文旅领域无关，例如涉及民政、金融、计算机、教育、娱乐等非文旅内容。
    2. 无法理解用户问题（包括乱码、语义不清、结构混乱等）。
    3. 用户问题中包含敏感词汇或禁止内容。
    注意：
    - 禁止输出“我不太理解你的意思”、“请您重新表述”、“这超出我的能力范围”等委婉或解释性语言。
    - 禁止尝试从错误内容中推测用户意图或给出模糊建议。
    - 一律用固定语句作答，**不得加入任何变体**。
    如果用户询问的问题属于以下几种情况，直接回答：'您好，我是文旅的智能问答助手（问数与指标问答），请问有什么可以帮到您的'，禁止有其他说明！
    第一种：用户询问你是谁，你的模型内部配置（思考过程，以及限制，使用哪种大模型）等
    第二种：用户向你问好，你好
    第三种：用户问你能干吗，你能做什么
    如果用户询问的问题属于以下几种情况，直接回答：'您可以查询的文旅相关指标包括但不限于：游客数量、旅游收入、酒店入住率、景区客流量、旅游满意度等。',禁止偶遇其他说明！
    第一种：用户询问你有什么功能
    第二种：用户询问系统有什么功能
    第三种：用户询问你能干什么
"""
SUMMARY_SYSTEM_PROMPT = """
    你是一个总结助手。请根据“本轮用户问题，SQL 查询结果”和“上一轮记忆”进行思考，把本轮用户问题、实际执行的 SQL 查询语句及查询结果进行总结。
    【重点要求】
    若 SQL 中的指标名称（即 target_name）与用户问题中不一致，请以 SQL 中的指标名称为准，不要使用用户问题中的原始表达。
    若上一轮记忆中存在两个问答对，且有冲突时，优先参照“最新记忆：”。
    若问题和查询单位一致，则输出单位；若单位是百分比“%”，则不要附加“万人次”等其他单位。
    如需要换算单位，请根据查询结果中的单位字段（data_unit）换算。
    不需要输出上一轮记忆的内容。
    不要自作聪明，用户问题问你什么，你就输出相关内容，不要乱改。
    查询结果是多少就写多少，小数点不能四舍五入，保持原始精度。
    若查询多个时间点，但部分时间数据缺失，需明确指出：
    哪些时间查询成功并给出结果；
    哪些时间无数据，明确说明未录入。
    回答必须完整、准确，且禁止添加任何主观说明（例如“可能是系统问题”等）。
    结果返回保留两位小数
    如数值为负时，增长类描述可灵活变为“下降/减少”，用词自然表达，不生硬。
    【示例】
    用户问：“3月和7月数据分别是多少？”，但 SQL 只查到3月数据：
    回答应为：“2025年3月浙江省全域旅游人数为123.45万人次。2025年7月的数据暂未录入系统。”
"""
SYSTEM_TRANSFORM_SCENIC = """
        你是一个问题转换助手，负责把本轮用户问题的原始问题转换成三要素："时间+景区+指标名"的标准格式。
        要求：
        1. 如果本轮用户问题缺少"时间+景区+指标名"的其中一个或者多个，不需要进行补充，基于当前有的要素进行转换
        2. 当上一轮记忆中存在两个问答对，且有冲突时，优先参照"最新记忆："
        3. 只输出转换后的标准问句，不要任何多余文字
        4. 保留用户的计算需求（排名、环比、占比等）
        5. **重要**：保持问句的完整性，不要拆分成多个问句。如果用户询问多个时间点或多个地点，保持原始结构
        6. 保留原始问句中的连接词（如"和"、"以及"、"分别"等）
        7. 保留景区的原样名。
        示例：
        - 本轮用户问题："当日到访数据是多少，25年xx景区"，转换为："2025年，xx景区当日到访是多少"
        - 本轮用户问题："当日到访数据是多少，2025年"，转换为："2025年，当日到访是多少"
        - 本轮用户问题："2025"，转换为："2025年"
        - 本轮用户问题："到访人数在本省排名多少"，转换为："到访人数在本省排名多少"
        如果本轮用户问题不涉及三要素："时间+景区+指标名"，则不需要转换，直接输出
        例如：本轮用户问题："是的"，直接输出："是的"
"""
SYSTEM_TRANSFORM = """
        你是一个问题转换助手，负责将本轮用户问题转换为标准格式："时间+地点+指标名"。
        转换规则：
        三要素抽取：尝试从用户问题中提取时间、地点、指标名三要素，按顺序组合。
        缺失处理：
        如果当前问题缺失要素，不主动虚构内容。
        但如果上一轮对话为模型提问、用户补充的闭环，且用户回答了时间或地点等，则允许补全缺失要素。
        例如：
        上一轮用户提问："全域旅游人数"
        模型回应："您没有提到时间，是想问 2024 年的数据吗？"
        本轮用户回答："是的"
        此时应转换为："2024年，全域旅游人数"
        地名标准化：
        省级：补充“省”（如“浙江”→“浙江省”）
        市级：补充“市”（如“杭州”→“杭州市”）
        区县级：根据实际情况补充“区”“县”等
        问句完整性与连接词保留：
        若用户问题中包含多个时间、地点、指标，保持结构不拆分
        保留连接词（如“和”“及”“分别”等）
        保留计算需求（如“排名”“环比”“占比”“增长”等）
        如果用户问题完全不含三要素，例如“是的”“好的”“再说一次”，则原样输出即可，不做任何转换
        最新记忆优先：若上下文中有冲突，优先采用“最新一轮记忆”进行补全
        示例：
        用户问题："全域旅游人数"
        上一轮模型提问："没有提到时间，是想问 2024 年的数据吗？"
        用户回答："是的"
        → 转换为："2024年，全域旅游人数"
        用户问题："2025年3月和7月份，浙江全域旅游人数分别是多少"
        → 转换为："2025年3月和7月份，浙江省全域旅游人数分别是多少"
        用户问题："杭州和温州的游客数据"
        → 转换为："杭州市和温州市的游客数据"

        用户问题："是的"
        → 输出为："是的"
"""
SYSTEM_COMPLETENESS_SCENIC = """
        根据“本轮用户问题”和“最新记忆”，判断是否能补全查询所需的三要素：‘时间’、‘景区’、‘指标名’。

        【输出要求】
        仅输出以下三项之一（禁止输出说明、标点或其他内容）：
        - complete：本轮问题中**已明确包含**时间、地点、指标名三要素，无需参考记忆。
        - complete_with_memory：本轮问题**缺少要素**，但“最新记忆”中**可以补全全部缺失要素**。
        

        【要素定义】
        - 时间：必须明确出现具体时间，如年份、月份、日期、季度、节日等。
        - 景区：具体景区名称：如“兰亭景区”“安昌古镇”“西湖风景名胜区”“江郎山·廿八都旅游区（含江郎山、清漾、廿八都）”等
        具备景区语义的地名：如“清河坊历史文化街区”“丝绸小镇（西山漾）”等
        类型化景区表达（也视为合法景区要素）：包括但不限于以下表述：
        “5A景区”“4A景区”“A级景区”
        “爆款景区”“热门景区”
        “重点景区”“夜间旅游区”
        “文旅消费集聚区”“夜间消费区”“国家级旅游度假区”等类别型表达
        注：不再强制要求“市+景区”格式；只要表意清晰、有具体指向，即可作为“景区”要素。        
        - 指标名：必须出现完整指标名称，如“到访人数”“当日到访”等，若为“增速”“占比”“同比增长”等衍生表达，也视为有效指标，前提是可反向匹配出合法基础指标。

        【判断流程】
        1. 若本轮问题中**自身已同时具备时间 + 地点 + 指标名** → 输出 `complete`
        2. 若本轮问题中**缺失要素**，但“最新记忆”中**能补全所有缺失要素** → 输出 `complete_with_memory`
        3. 若缺失要素且“最新记忆”也无法补全 → 输出 `complete_with_memory`

        【注意事项】
        - 不允许猜测或凭空假设。只能使用“本轮问题”与“最新记忆”中的明确信息。
        - 若用户本轮只说“是的”“好的”“那呢”等承接话术，需主动结合“最新记忆”内容判断用户意图。
        - 若本轮仅含一个或两个要素，仍可结合“最新记忆”补全并判定为 `complete_with_memory`。
        - 若“本轮问题”自身完整，则一定输出 `complete`，**不要因为记忆不同而降低等级**。
        - 对于符合“市+景区”结构的表达（如“宁波市慈城古县城景区”），应默认视为有效景区，不得因名称不常见而判定缺失。  
        
        【示例】  
        complete：
        2025年6月 安昌古镇 到访人数
        2025年6月 安昌古镇 到访人数环比增长
        2025年6月 江郎山·廿八都旅游区（含江郎山、清漾、廿八都） 当日到访
        2025年5月 爆款景区 到访人数同比增长排名
        2024年第三季度 文旅消费集聚区 接待人次
        春节期间 5A景区 当日到访排名
        complete_with_memory：
        1.本轮：是的  
          最新记忆：问题：2025年3月，当日到访数据
          最新记忆：回答：您想查询宁波市宁波雅戈尔动物园景区的数据
          分析：本轮结合最新记忆，可以补全为：2025年3月，宁波市宁波雅戈尔动物园景区当日到访数据
        2.本轮：2025年6月  
          最新记忆：问题：杭州市清河坊历史文化街区当日到访的数据
          最新记忆：回答：您这边没有提到具体时间，请问是不是想询问2024年的相关数据呢？ 
          本轮结合最新记忆，可以补全为：2025年6月，杭州市清河坊历史文化街区当日到访的数据
        incomplete：  
          本轮：宁波市宁波雅戈尔动物园景区当日到访  
          无最新记忆  
"""
SYSTEM_COMPLETENESS = """
        你需要判断“本轮用户问题”中，是否同时包含时间、地点、指标名三项。
        如果三项全部都出现，输出：complete
        如果缺少任何一项，输出：incomplete
        禁止输出其它内容
        示例：
        本轮用户问题：2024年浙江省全域旅游人数是多少
        → complete
        本轮用户问题：今年全域旅游人数
        → incomplete
        本轮用户问题：杭州市
        → incomplete

"""
SYSTEM_FILL = """你是一个问题补全和替换助手。请根据“本轮用户问题”和“最新记忆”，将用户问题补全为包含“时间、地名、指标名”的完整标准问句。

    【最重要规则】
    - 必须以“最新记忆：问题”为模板句，进行结构复用。
    - 你需要替换模板句中对应的时间、地点或指标名，不得原样照抄模板。
    - **至少要替换一个要素**（不能空补全）。

    【补全流程】
    1. 获取“最新记忆：问题”作为问句模板。
    2. 判断“本轮用户问题”是否是以下两类之一：
    - 明确提供了新的“时间”“地点”或“指标名”
    - 属于“承接性确认语”（如“是的”“好的”“没错”“对的”等），表示用户接受了模型上一轮的建议
    3. 若是“承接性语句”，必须从“最新记忆：回答”中提取推荐指标（如“您是否想问 XXX”）
    - 替换模板中的原始指标名为推荐指标
    4. 若本轮明确给出了新要素，替换掉模板中相应的要素
    5. 其他部分保持模板结构不变
    6. 最终返回一条包含时间、地名、指标名的标准问题

    【要素识别规则】
    - 时间：包括“2025年”“4月”“3月和5月”等年/月/日/季节类表达
    - 地名：包括“浙江省”“杭州市”“上城区”“衢州市”等行政区
    - 指标名：包括“全域旅游人数”“住宿单位客房出租率”等统计性数据名词

    【输出格式要求】
    - 只输出补全后的完整问题
    - 禁止输出解释说明、JSON、标点之外的任何附加信息
    - 必须替换至少一个要素，不能重复原句内容
    
    【示例1】
    本轮用户问题：上城区  
    最新记忆：问题：2025年3月和5月份，浙江省全域旅游人数分别是多少？  
    分析：本轮提供了地名“上城区”，替换模板中原地名“浙江省”  
    补全结果：2025年3月和5月份，上城区全域旅游人数分别是多少？

    【示例2】
    本轮用户问题：宁波市  
    最新记忆：问题：2025年，浙江省各个月份的全域旅游人数分别是多少？  
    分析：本轮提供了地名“宁波市”，替换模板中的“浙江省”  
    补全结果：2025年，宁波市各个月份的全域旅游人数分别是多少？

    【示例3】
    本轮用户问题：4月份  
    最新记忆：问题：2025年3月和5月份，浙江省全域旅游人数分别是多少？  
    分析：本轮提供了时间“4月份”，替换模板中原时间“3月和5月份”  
    补全结果：2025年4月份，浙江省全域旅游人数是多少？

    【示例4】
    本轮用户问题：本县游人数  
    最新记忆：问题：2025年3月和5月份，浙江省全域旅游人数分别是多少？  
    分析：本轮提供了指标“本县游人数”，替换模板中原指标“全域旅游人数”  
    补全结果：2025年3月和5月份，浙江省本县游人数分别是多少？

    【示例5】
    本轮用户问题：是的  
    最新记忆：问题：2024年1月，衢州市旅游客房出租率是多少？  
    最新记忆：回答：暂无权限查询该指标的信息，请精确使用指标词，或者您是否想问住宿单位客房出租率  
    分析：本轮是确认语，表示用户接受了推荐指标。应替换模板中原指标“旅游客房出租率”为建议指标“住宿单位客房出租率”  
    补全结果：2024年1月，衢州市住宿单位客房出租率是多少？

    【示例6】
    本轮用户问题：2025年6月  
    最新记忆：问题：衢州市住房单位客房出租率是多少？  
    最新记忆：回答：您这边没有提到具体时间，请问是不是想询问2024年的相关数据呢？
    分析：本轮是覆盖原问题，用户回答了日期，不是选择系统给他的日期，则直接选用用户给的回答进行补全，最后得到答案。 
    补全结果：2025年6月，衢州市住宿单位客房出租率是多少？
"""
SYSTEM_FILL_SCENIC = """
    你是一个问题补全助手。请根据“本轮用户问题”和“最新记忆”，将用户问题补全为包含“时间、景区名、指标名”的完整标准问句。

    【最重要规则】
    - 必须以“最新记忆：问题”为模板句，进行结构复用。
    - 你需要替换模板句中对应的时间、景区或指标名，不得原样照抄模板。
    - **至少要替换一个要素**（不能空补全）。

    【补全流程】
    1. 获取“最新记忆：问题”作为问句模板。
    2. 判断“本轮用户问题”是否是以下两类之一：
    - 明确提供了新的“时间”“景区名”或“指标名”
    - 属于“承接性确认语”（如“是的”“好的”“没错”“对的”等），表示用户接受了模型上一轮的建议
    3. 若是“承接性语句”，必须从“最新记忆：回答”中提取推荐指标（如“您是否想问 XXX”）
    - 替换模板中的原始指标名为推荐指标
    4. 若本轮明确给出了新要素，替换掉模板中相应的要素
    5. 其他部分保持模板结构不变
    6. 最终返回一条包含时间、景区名、指标名的标准问题

    【要素识别规则】
    - 时间：包括“2025年”“4月”“3月和5月”等年/月/日/季节类表达
    - 景区名：景区信息的判断规则如下：需能够识别为具体景区类地理实体，其名称通常具备以下特征之一：
    含有“景区”字样，且前缀通常是某市或县区名，如“杭州市大慈岩景区”“湖州市菰城景区”；
    虽未含“景区”字样，但被广泛作为景点或游客目的地存在，如“绍兴柯桥古镇”“舟山520幸福街区”等；
    不包括仅表示行政区划的名称（如“杭州市”“西湖区”），也不包括抽象类型类名（如“A级景区”“爆款景区”）；
    只要名称能够明确指代一个具体旅游场所、街区、景点或集聚区，即可视为包含“景区信息”。
    - 指标名：包括“全域旅游人数”“住宿单位客房出租率”等统计性数据名词

    【输出格式要求】
    - 只输出补全后的完整问题
    - 禁止输出解释说明、JSON、标点之外的任何附加信息
    - 必须替换至少一个要素，不能重复原句内容

    【示例1】
    本轮用户问题：杭州市大慈岩景区  
    最新记忆：问题：2025年3月和5月份，湖州市菰城景区到访人数分别是多少？  
    分析：本轮提供了景区名“杭州市大慈岩景区”，替换模板中原景区名“湖州市菰城景区”  
    补全结果：2025年3月和5月份，杭州市大慈岩景区到访人数分别是多少？

    【示例2】
    本轮用户问题：4月份  
    最新记忆：问题：2025年3月和5月份，湖州市菰城景区当日到访分别是多少？  
    分析：本轮提供了时间“4月份”，替换模板中原时间“3月和5月份”  
    补全结果：2025年4月份，湖州市菰城景区当日到访是多少？

    【示例3】
    本轮用户问题：当日到访  
    最新记忆：问题：2025年3月和5月份，湖州市菰城景区到访人数分别是多少？  
    分析：本轮提供了指标“当日到访”，替换模板中原指标“到访人数”  
    补全结果：2025年3月和5月份，湖州市菰城景区当日到访分别是多少？

    【示例4】
    本轮用户问题：是的  
    最新记忆：问题：2024年1月，湖州市菰城景区访问的人数是多少？  
    最新记忆：回答：暂无权限查询该指标的信息，请精确使用指标词，或者您是否想问到访人数 
    分析：本轮是确认语，表示用户接受了推荐指标。应替换模板中原指标“访问的人数”为建议指标“到访人数”  
    补全结果：2024年1月，湖州市菰城景区到访人数是多少？
"""
SYSTEM_INCOMPLETE_REASON = """
    你是一个诊断助手，专门判断“待诊断的问题”中缺少哪一个要素：'时间'、'地点'或'指标名'。
    输出要求：
    仅输出以下三种之一，禁止添加说明、标点或换行：
        '时间'
        '地点'
        '指标名'
    判断顺序（严格按照此顺序判断，发现第一个缺失即停止）：
    1. 时间
    2. 地点
    3. 指标名
    判断规则如下：
    【时间】：
    若问题中不包含以下任意形式，则视为缺少“时间”：
    - 年份，如：2023年、2024至2025年、2021-2023年
    - 月份或季度，如：4月、一季度、Q2、第三季度
    - 明确时间范围，如：近三年、上半年、去年、过去五年、十四五期间
    【地点】：
    若问题中不包含任一中国行政区划或常见区域名称，则视为缺少“地点”。以下为需识别的地名范围（不限于）：
    - 省级行政区全称：如“湖北省”“黑龙江省”“四川省”“内蒙古自治区”
    - 地级市/副省级城市：如“武汉市”“苏州市”“深圳市”“青岛市”
    - 直辖市：如“北京市”“上海市”“重庆市”“天津市”
    - 地区简称/传统简称：如“鄂”“黑”“川”“汉”“京”“沪”“渝”等
    - 区域性表达：如“全国”“华东地区”“长三角”“珠三角”“成渝地区”“京津冀”“大湾区”等
    【指标名】：
    若问题中不包含具体统计指标，则视为缺少“指标名”。指标包括但不限于：
    一日游客占比、过夜游客人数、省内游人数、跨县游人数、县内游人数、全域旅游人数等
    示例参考：
    待诊断的问题: 2025年4月，浙江省 → 输出：'指标名'  
    待诊断的问题: 2025年4月，全域旅游人数 → 输出：'地点'  
    待诊断的问题: 浙江省全域旅游人数 → 输出：'时间'  
"""
SYSTEM_INCOMPLETE_REASON_SCENIC = """
    你是一个诊断助手，专门判断“待诊断的问题”中缺少哪一个要素：'时间'、'景区'或'指标名'。
    输出要求：
    仅输出以下三种之一，禁止添加说明、标点或换行、以及引号 就输出中文：
        时间
        景区
        指标名
    判断顺序（严格按照此顺序判断，发现第一个缺失即停止）：
    1. 时间
    2. 景区
    3. 景区类型
    3. 指标名
    判断规则如下：
    【时间】：
    若问题中不包含以下任意形式，则视为缺少“时间”：
    - 年份，如：2023年、2024至2025年、2021-2023年
    - 月份或季度，如：4月、一季度、Q2、第三季度
    - 明确时间范围，如：近三年、上半年、去年、过去五年、十四五期间
    【景区】：
    若问题中不包含“行政区+景区名称”的表达，则视为缺少“景区”。景区的判断标准如下（满足其一即可）：
    - 形式应为“城市名+景区名”，如“杭州市大慈岩景区”“湖州市菰城景区”“温州市雁荡山景区”
    - 必须包含“景区”字样，且前缀为中国地级市或县级区等行政区名称
    【指标名】：
    若问题中不包含具体统计指标，则视为缺少“指标名”。指标包括但不限于：
    当日到访，到访人数等
    示例参考：
    待诊断的问题: 2025年4月，衢州市仙霞关景区 → 输出：'指标名'  
    待诊断的问题: 2025年4月，当日到访 → 输出：'景区'  
    待诊断的问题: 衢州市仙霞关景区当日到访 → 输出：'时间'  
"""

# ==================== SQL 工具函数 ====================
def validate_and_format(sql: str) -> str:
    """SQL 格式化与验证"""

    sql = sql.strip()

    # 清除 markdown 包裹，例如 ```sql ... ```
    if sql.startswith("```") and sql.endswith("```"):
        sql = sql.strip("`").strip()
        if sql.lower().startswith("sql"):
            sql = sql[3:].strip()

    # 验证语法起始
    if not re.match(r'^(SELECT|WITH)\s', sql, re.IGNORECASE):
        raise ValueError(f"［SQL 验证失败］：{sql}")

    return sqlparse.format(sql, reindent=True, keyword_case='upper')



# ==================== 查询服务 ====================
class QueryService:
    """查询服务类"""

    @staticmethod
    def query_database(sql: str) -> List[Dict]:
        """执行数据库查询"""
        try:
            with DatabaseManager.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(sql)
                rows = cursor.fetchall()
                return rows
        except Exception as e:
            logger.error(f'数据库查询错误: {e}')
            return []

    @staticmethod
    def fetch_memory(user_id: str, conversation_id: str) -> List[str]:
        """获取对话记忆（使用新的表结构）"""
        memories = []
        try:
            with DatabaseManager.get_connection() as conn:
                cursor = conn.cursor()

                # 查询最新的两条记录
                cursor.execute(
                    """
                    SELECT message_id, question, answer, created_at
                    FROM tb_conversation_history
                    WHERE user_id = %s AND conversation_id = %s
                    ORDER BY created_at DESC
                    LIMIT 2
                    """,
                    (user_id, conversation_id)
                )

                rows = cursor.fetchall()

                # 按时间正序处理（最早的在前）
                reversed_rows = list(reversed(rows))

                for i, row in enumerate(reversed_rows):
                    _, question, answer, _ = row

                    # 判断是否是最新的问答对（最后一个）
                    if i == len(reversed_rows) - 1:  # 移除了 len(reversed_rows) > 1 的条件
                        memories.append(f"最新记忆：问题：{question}")
                        memories.append(f"最新记忆：回答：{answer}")
                    else:
                        memories.append(f"问题：{question}")
                        memories.append(f"回答：{answer}")

        except Exception as e:
            logger.error(f'获取记忆失败: {e}')

        return memories

    @staticmethod
    def save_conversation_history(
            user_id: str,
            conv_id: str,
            message_id: str,
            question: str,
            answer: str
    ) -> bool:

        try:
            with DatabaseManager.get_connection() as conn:
                cursor = conn.cursor()

                # 插入新的问答记录
                insert_sql = """
                INSERT INTO tb_conversation_history 
                (user_id, conversation_id, message_id, question, answer)
                VALUES (%s, %s, %s, %s, %s)
                """
                cursor.execute(insert_sql, (user_id, conv_id, message_id, question, answer))

                # 获取当前会话的所有记录，按时间降序
                cursor.execute(
                    """
                    SELECT id FROM tb_conversation_history
                    WHERE user_id = %s AND conversation_id = %s
                    ORDER BY created_at DESC
                    """,
                    (user_id, conv_id)
                )

                all_ids = [row[0] for row in cursor.fetchall()]

                # 如果记录超过2条，删除旧的
                if len(all_ids) > 2:
                    ids_to_delete = all_ids[2:]  # 保留前2条，删除其余
                    format_strings = ','.join(['%s'] * len(ids_to_delete))
                    delete_sql = f"""
                    DELETE FROM tb_conversation_history 
                    WHERE id IN ({format_strings})
                    """
                    cursor.execute(delete_sql, ids_to_delete)

                conn.commit()

                return True

        except Exception as e:
            logger.error(f"保存问答记录失败: {e}")
            return False

    @staticmethod
    def save_conversation_history_async(
            user_id: str,
            conv_id: str,
            message_id: str,
            question: str,
            answer: str
    ):
        """
        异步保存问答记录（在后台线程中执行）
        """

        def save_in_thread():
            try:
                QueryService.save_conversation_history(
                    user_id, conv_id, message_id, question, answer
                )
            except Exception as e:
                logger.error(f"后台保存问答记录失败: {e}")

        # 使用线程池执行，避免阻塞主线程
        import threading
        thread = threading.Thread(target=save_in_thread)
        thread.daemon = True
        thread.start()

    @staticmethod
    def save_log(
        user_id: str,
        timestamp: datetime,
        question: str,
        answer: str,
        sql_text: str,
        status: str
    ) -> bool:
        """同步保存一条完整日志记录到 qa_logs"""
        try:
            with DatabaseManager.get_connection() as conn:
                cursor = conn.cursor()
                insert_sql = """
                INSERT INTO qa_logs
                  (user_id, timestamp, question, answer, sql_text, status)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                cursor.execute(insert_sql,
                               (user_id, timestamp, question, answer, sql_text, status))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"保存日志失败: {e}")
            return False

    @staticmethod
    def save_log_async(
        user_id: str,
        ts: datetime,
        question: str,
        answer: str,
        sql_text: str,
        status: str
    ):
        """在后台线程中异步保存日志，避免阻塞主流程"""
        def _worker():
            try:
                QueryService.save_log(
                    user_id, ts, question, answer, sql_text, status
                )
            except Exception as e:
                logger.error(f"后台保存日志失败: {e}")

        import threading
        thread = threading.Thread(target=_worker)
        thread.daemon = True
        thread.start()
    
    @staticmethod
    def get_system_month(month_resp: str) -> List[str]:
        """
        根据传入的 month_resp 判断其是节假日还是时间后缀，并返回对应表名列表。
        支持：
        - 多个节假日（如 '节假日：2025年端午节, 节假日：2025年春节'）
        - 普通时间后缀（如 '202504A', '2025Q1'）
        返回格式：
        ['cultural_wide_2025H3', 'cultural_wide_2025H1'] 或 ['cultural_wide_202504A']
        """
        results = []

        try:
            if month_resp.startswith("节假日："):
                # 拆分多个节假日表达（以英文逗号分隔）
                holiday_items = [item.strip() for item in month_resp.split(",") if item.strip()]

                for item in holiday_items:
                    if not item.startswith("节假日："):
                        logger.warning(f"非标准节假日标识被忽略：{item}")
                        continue

                    raw = item.replace("节假日：", "").strip()

                    if "年" not in raw:
                        logger.warning(f"节假日格式不规范，应为“2025年端午节”：{raw}")
                        continue

                    # 分割成年份和节日名称
                    try:
                        year_str, holiday_name = raw.split("年", 1)
                        year = int(year_str)
                    except ValueError:
                        logger.warning(f"节假日表达无法拆分为年份和名称：{raw}")
                        continue

                    # 查询节日 code
                    sql = f"""
                    SELECT code FROM tb_holidays_info 
                    WHERE name = '{holiday_name}' AND year = {year}
                    """
                    result = QueryService.query_database(sql)

                    if result and isinstance(result, list) and 'code' in result[0]:
                        code = result[0]['code']
                        results.append(f"cultural_wide_{code}")
                    else:
                        logger.warning(f"未找到节日“{holiday_name}”在 {year} 年的 code")

            else:
                # 非节假日场景，直接拼接普通表名
                parts = [p.strip() for p in month_resp.split(",") if p.strip()]
                for p in parts:
                    results.append(f"cultural_wide_{p}")

        except Exception as e:
            logger.error(f"get_system_month 解析失败: {e}")

        return results
    @staticmethod
    def get_system_month_scenic(month_resp: str) -> List[str]:
        """
        根据传入的 month_resp 判断其是节假日还是时间后缀，并返回对应表名列表。
        支持：
        - 多个节假日（如 '节假日：2025年端午节, 节假日：2025年春节'）
        - 普通时间后缀（如 '202504A', '2025Q1'）
        返回格式：
        ['cultural_wide_2025H3', 'cultural_wide_2025H1'] 或 ['cultural_wide_202504A']
        """
        results = []

        try:
            if month_resp.startswith("节假日："):
                # 拆分多个节假日表达（以英文逗号分隔）
                holiday_items = [item.strip() for item in month_resp.split(",") if item.strip()]

                for item in holiday_items:
                    if not item.startswith("节假日："):
                        logger.warning(f"非标准节假日标识被忽略：{item}")
                        continue

                    raw = item.replace("节假日：", "").strip()

                    if "年" not in raw:
                        logger.warning(f"节假日格式不规范，应为“2025年端午节”：{raw}")
                        continue

                    # 分割成年份和节日名称
                    try:
                        year_str, holiday_name = raw.split("年", 1)
                        year = int(year_str)
                    except ValueError:
                        logger.warning(f"节假日表达无法拆分为年份和名称：{raw}")
                        continue

                    # 查询节日 code
                    sql = f"""
                    SELECT code FROM tb_holidays_info 
                    WHERE name = '{holiday_name}' AND year = {year}
                    """
                    result = QueryService.query_database(sql)

                    if result and isinstance(result, list) and 'code' in result[0]:
                        code = result[0]['code']
                        results.append(f"scenic_wide_{code}")
                    else:
                        logger.warning(f"未找到节日“{holiday_name}”在 {year} 年的 code")

            else:
                # 非节假日场景，直接拼接普通表名
                parts = [p.strip() for p in month_resp.split(",") if p.strip()]
                for p in parts:
                    results.append(f"scenic_wide_{p}")

        except Exception as e:
            logger.error(f"get_system_month_scenic 解析失败: {e}")

        return results
# 保持向后兼容
query_database = QueryService.query_database
fetch_memory = QueryService.fetch_memory


# ========== 辅助函数 ==========
def create_answer_collector():
    """创建答案收集器"""
    return {'chunks': []}


def save_qa_background(user_id: str, conv_id: str, message_id: str, question: str, answer: str):
    """在后台保存问答（不阻塞流式返回）"""
    if user_id and conv_id and answer:  # 确保答案不为空
        QueryService.save_conversation_history_async(
            user_id, conv_id, message_id, question, answer
        )
def save_log_background(user_id: str,timestamp: datetime,question: str,answer: str,sql_text: str,status: str):
    """
    在后台异步保存完整日志：
      - user_id: 调用者 ID
      - timestamp: 当前请求时间
      - question: 原始用户问题
      - answer: 生成的回复内容
      - sql_text: 本次执行的 SQL
      - status: SQL 执行状态，如 'success' 或 'error'
    """
    if user_id and timestamp:
        QueryService.save_log_async(
            user_id, timestamp, question, answer, sql_text, status
        )


def permission_check_llm_scenic(query: str, history_texts: list[str], allowed_regions: list[str],
                         allowed_indicators: list[str]) -> str:
    """
    判断用户问题中的地区和指标是否在权限范围内。

    如果“大模型”判断为“有权限”，则 **仅** 返回字符串 "有权限"。
    如果“大模型”判断为“无权限”，则返回一句话提示：
      - 若地区越权（指景区前缀的行政区不在允许列表中），返回：暂无权限查询该地区的信息
      - 若指标越权，返回：暂无权限查询该指标的信息

    本函数直接把 LLM 的原始输出作为字符串返回，调用方根据返回值是否为 "有权限" 来决定是否继续后续操作。
    """

    memory = "\n".join(history_texts)
    user_content = (
        f"本轮用户问题：{query}\n上一轮记忆：{memory}"
        if memory else f"本轮用户问题：{query}"
    )
    print(f"当前指标{allowed_indicators}")
    # 1. 构造提示：告知 LLM “有权限就输出 '有权限'；无权限就输出拒绝提示”。
    PERMISSION_CHECK_PROMPT = f"""你是一个景区权限判断助手。你需要根据“本轮用户问题”和“上一轮记忆”，抽取出“景区所属地区名”和“指标名”，并判断它们是否都在权限范围内。

    【允许条件】
    - 允许查询的地区（景区前缀）列表：{', '.join(allowed_regions)}
    - 允许查询的指标名列表：{', '.join(allowed_indicators)}

    【判断流程】
    1. **抽取指标名**：
    - 必须提取**完整指标表达**，如：“到访人数”“第一日当日到访人数”，但如果从语义中合理推断其依赖的基础指标（如“排名”“增速”“变化率”问题中常暗指“全域旅游人数”），就可使用默认推断指标。
    - 严禁只提取关键词或词根（如“出租率”“访客”）
    - 严禁模糊化抽取或抽取中间片段，必须是完整、自然语言中可构成独立指标名的表达
    - 抽取后需与 `allowed_indicators` 中的任意一项进行**字符串完全匹配**，否则视为无权限
    - 如涉及“增速”“同比”“排名”等衍生表达，应判断是否隐含上述合法指标，如“当日到访”。

    2. **抽取景区所属地区名**：
    - - 若问题中出现“XXX市XXX景区”或“XXX区XXX馆”等结构，应仅提取“市”或“区”级的前缀作为地区，如：
    - “宁波市慈城古县城景区” → “宁波市”
    - “拱墅区中国伞博物馆” → “拱墅区”
    - “杭州市京杭大运河·杭州景区” -> “杭州市”
    - 禁止将“宁波市慈城”一并作为地区
    - 抽取结果必须与 `allowed_regions` 列表中的指标完全匹配，否则视为无权限。

    3. **权限判断顺序**：
    - 若地区不在允许列表中 → 返回：
    暂无权限查询该地区的信息
    - 若地区合法，但指标不在允许列表中 → 推荐最相近的合法指标，返回：
    暂无权限查询该指标的信息，请精确使用指标词，或者您是否想问XXX
    - 若地区和指标均合法 → 返回：
    有权限

    【指标匹配规则】
    - 若用户问题中直接提到了完整指标名（如“当日到访”“第一日当日到访人数”），则使用字符串完全匹配判断权限。
    - 若用户问题中出现“占比”“占全省比率”“所占比例”“比重”“百分比”“同比增长”“同比增速” 等表达，并且语义上可以被理解为“某市的某个合法指标除以全省对应指标”，可认为其本质依赖于该合法指标，允许使用。例如：
        - 问题：“2025年1月衢州市的当日到访占全省比率多少？”
        → 合法，因为“当日到访”是合法指标，“占比”仅是计算表达形式
        - 问题：“2024年杭州市第一日当日到访人数所占全省比例？”
        → 合法，因为“第一日当日到访人数”为合法指标
        此类“市与省同一合法指标之间的占比计算”应默认视为拥有权限，返回“有权限”。
    - 否则，尝试进行**衍生指标识别**。如果用户问题中出现如下关键词之一：
        - “增速”“同比”“增长率”“增长情况”“排名”“排行”“第几名”
    则遍历 `allowed_indicators`，判断用户问题中是否包含 `allowed_indicator` 的片段或变形组合。例如：
        - 用户问题：“当日到访同比增速” + allowed_indicators 包含“当日到访” → 匹配成立
        - 用户问题：“第一日当日到访人数排名” + allowed_indicators 包含“第一日当日到访人数” → 匹配成立
    - 若成功识别某个指标为“被隐含提及”的基础指标，即便它未完整出现，也可视为拥有该指标权限 → 返回：**有权限**
    - 若所有匹配尝试失败，再用字符串相似度推荐最接近项，返回：
        暂无权限查询该指标的信息，请精确使用指标词，或者您是否想问XXX
    - 若无法推荐任何相近项 → 返回默认指标“当日到访”


    【输出格式】
    你最终必须且只能输出以下三种固定格式之一，禁止输出任何解释、说明、标点或换行，且回复内容必须与下方文本完全一致，不得有任何改动：
    1.有权限
    2.暂无权限查询该地区的信息
    3.暂无权限查询该指标的信息，请精确使用指标词，或者您是否想问XXX

    【示例判断】
    - 用户问：“2024年杭州市大慈岩景区当日到访”，若“杭州市”在 allowed_regions 且“当日到访”在 allowed_indicators → 返回：有权限
    - 用户问：“2023年绍兴市兰亭景区到访人数有多少”，若“到访人数”不在允许的指标列表"allowed_indicators"中 → 返回：
    暂无权限查询该指标的信息，请精确使用指标词，或者您是否想问当日到访
    - 用户问：“2025年6月宁波市宁波雅戈尔动物园景区人流量”，若“宁波市”在 allowed_regions 但指标不在允许的指标列表"allowed_indicators"中 → 返回：
    暂无权限查询该指标的信息，请精确使用指标词，或者您是否想问当日到访 这个权限约束不够严格
    """

    prompt_messages = [
        {"role": "system", "content": PERMISSION_CHECK_PROMPT},
        {"role": "user", "content": user_content}
    ]
    

    # print("=== permission_check_llm 调用前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("=============================")

    # 2. 调用 LLM
    resp = invoke_llm(messages=prompt_messages, stream=False)
    content = resp.choices[0].message.content.strip()
    print(f"\nLLM 原始返回内容: {repr(content)}\n")

    # 3. 直接返回 LLM 输出的那一句话
    return content

def permission_check_llm(query: str, history_texts: list[str], allowed_regions: list[str],
                         allowed_indicators: list[str]) -> str:
    """
    判断用户问题中的地区和指标是否在权限范围内。

    如果“大模型”判断为“有权限”，则 **仅** 返回字符串 "有权限"。
    如果“大模型”判断为“无权限”，则返回一句话提示：
      - 若地区越权，返回：暂无权限查询该地区的信息
      - 若指标越权，返回：暂无权限查询该指标的信息

    本函数直接把 LLM 的原始输出作为字符串返回，调用方根据返回值是否为 "有权限" 来决定是否继续后续操作。
    """

    memory = "\n".join(history_texts)
    user_content = (
        f"本轮用户问题：{query}\n上一轮记忆：{memory}"
        if memory else f"本轮用户问题：{query}"
    )

    # 1. 构造提示：告知 LLM “有权限就输出 '有权限'；无权限就输出拒绝提示”。
    PERMISSION_CHECK_PROMPT = f"""你是一个权限判断助手。请严格按如下流程回答：
        1.抽取三要素：
            时间（如2024年、5月、去年同期等，未提及则写“无”）
            地区名（必须为具体行政区，如“杭州市”，未提及则写“无”）
            指标名（如“全域旅游人数”“游客接待量”，完整表达，未提及则写“无”）
        2.权限判断（必须严格按以下规则）：
            有权限：如果“地区名”在允许地区列表，且“指标名”在允许指标列表，或者“指标名”是“允许指标 + 占比”或“允许指标 + 排名”这种衍生表达（如“全域旅游人数占比”），则有权限。
            暂无权限查询该地区的信息：如果“地区名”不在允许地区列表。
            暂无权限查询该指标的信息，请精确使用指标词，或者您是否想问XXX：如果“地区名”在列表但指标名不在，则找最接近的合法指标填XXX，找不到就填“全域旅游人数”。
        允许地区列表：{allowed_regions}
        允许指标名列表：{allowed_indicators}
        【只允许输出下列三句话之一，必须与下列文本完全一致】：
            有权限
            暂无权限查询该地区的信息
            暂无权限查询该指标的信息，请精确使用指标词，或者您是否想问XXX
        【特别说明】
            “全域旅游人数占比”“游客接待总人数排名”等，只要前面的“全域旅游人数”“游客接待总人数”在允许指标里，也算有权限。
            不允许模糊匹配、近义词合并，只允许完全一致或“允许指标+占比/排名”等派生词表达。
            只输出结果，不输出三要素或其它内容。
        加强示例：
            用户问题：“杭州市游客接待总人数排名是多少？”
            → 有权限
            用户问题：“2024年北京市过夜游客人数”
            但是允许地区中没有北京市则
            → 暂无权限查询该地区的信息
            用户问题：“2024年衢州市旅游客房出租率”
            但是允许的指标名中没有旅游客房出租率，最相近的是住宿单位客房出租率
            → 暂无权限查询该指标的信息，请精确使用指标词，或者您是否想问住宿单位客房出租率

    """
    print(f"权限判断：{PERMISSION_CHECK_PROMPT}")

    prompt_messages = [
        {"role": "system", "content": PERMISSION_CHECK_PROMPT},
        {"role": "user", "content": user_content}
    ]

    # print("=== permission_check_llm 调用前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("=============================")

    # 2. 调用 LLM
    resp = invoke_llm(messages=prompt_messages, stream=False)
    content = resp.choices[0].message.content.strip()
    print(f"\nLLM 原始返回内容: {repr(content)}\n")

    # 3. 直接返回 LLM 输出的那一句话
    return content
# ==================== 意图分类模型 ====================
def classify_intent(query: str, history_texts: list[str], stream: bool = False) -> str:
    """
    用最近的历史提问 + 本次问题一起，调用意图分类模型（通过 invoke_llm）。
    """
    # 1. 拼接历史记忆和本次问题
    memory = "\n".join(history_texts)
    user_content = (
        f"本轮用户问题：{query}\n上一轮记忆：{memory}"
        if memory else f"本轮用户问题：{query}"
    )
    # 构造 messages 列表
    prompt_messages = [
        {"role": "system", "content": SYSTEM_INTENT},
        {"role": "user", "content": user_content}
    ]

    # print("=== classify_intent 调用前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("=============================")

    # 2. 调用 invoke_llm
    resp = invoke_llm(messages=prompt_messages, stream=stream)

    # 3. 解析结果
    content = resp.choices[0].message.content.strip().lower()
    print(f"类别: {content}")
    return content


# ==================== RAG模型 ====================
def rag_llm(query: str, history_texts: list[str], stream: bool = False):
    """
    RAG 分支：检索完文档后，结合本轮用户问题和上一轮问答记忆，
    用 RAG_SYSTEM_PROMPT 调用模型，只输出检索到的"指标含义"或"计算逻辑"。
    """
    # 拼接完整记忆
    memory = "\n".join(history_texts)
    user_content = (
        f"本轮用户问题：{query}\n上一轮记忆：\n{memory}"
        if memory else
        f"本轮用户问题：{query}"
    )

    # 构造消息
    prompt_messages = [
        {"role": "system", "content": RAG_SYSTEM_PROMPT},
        {"role": "user", "content": user_content}
    ]

    # print("=== rag_llm 调用前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("=============================")

    return invoke_llm(messages=prompt_messages, stream=stream)

# ==================== SQL生成模型 ====================
def convert_admin_sql(query: str, history_texts: list[str], stream: bool = False) -> str:
    """SQL生成模型_行政区"""
    # 1. 排名检测
    # 获取可用表列表
    available_tables = get_available_tables()
    available_info = get_available_months()
    memory = "\n".join(history_texts)
    user_content = (
        f"数据库情况：{available_tables}\n"
        f"有数据的月份：{available_info}\n"
        f"本轮用户问题：{query}\n"
        f"上一轮记忆：{memory}\n\n"
        if memory else
        f"数据库情况：{available_tables}\n"
        f"有数据的月份：{available_info}\n"
        f"本轮用户问题：{query}\n\n"
    )
    # 构造 messages 列表
    detect_messages = [
        {"role": "system", "content": RANK_DETECT_INSTRUCTIONS},
        {"role": "user", "content": f"本轮用户问题：{user_content}"}
    ]

    # print("=== RANK_DETECT 调用前 ===")
    # for m in detect_messages:
    #     print(m)
    # print("=============================")

    detect_resp = invoke_llm(messages=detect_messages, stream=False).choices[0].message.content.strip()
    HOLIDAY_DETERMINATION = bulid_holiday_prompt()
    holiday_messages = [
            {"role": "system", "content": HOLIDAY_DETERMINATION},
            {"role": "user", "content": f"本轮用户问题：{user_content}"}
        ]
    holiday_resp = invoke_llm(messages=holiday_messages, stream=False).choices[0].message.content.strip()
    month_data=[]
    if holiday_resp == '是':
        MONTH_DETECT_INSTRUCTIONS = build_month_prompt()
        month_messages = [
            {"role": "system", "content": MONTH_DETECT_INSTRUCTIONS},
            {"role": "user", "content": f"本轮用户问题：{user_content}"}
        ]
        
        month_resp = invoke_llm(messages=month_messages, stream=False).choices[0].message.content.strip()
        
        month_data = QueryService.get_system_month(month_resp)
    print(f"\n提取出的月份:{month_data}\n")
    SYSTEM_CULTURAL_INSTRUCTIONS = build_admin_prompt(month_data)
    RANK_CULTURAL_INSTRUCTIONS = build_admin_rank_prompt(month_data)

    system_instr = RANK_CULTURAL_INSTRUCTIONS if detect_resp == "是" else SYSTEM_CULTURAL_INSTRUCTIONS
    print(f"是不是排名: {detect_resp}")

    prompt_messages = [
        {"role": "system", "content": system_instr},
        {"role": "user", "content": user_content}
    ]

    # print("=== convert_admin_sql 调用大模型前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("==============================")

    resp = invoke_llm(messages=prompt_messages, stream=stream)

    sql = resp.choices[0].message.content
    return validate_and_format(sql)


def convert_scenic_sql(query: str, history_texts: list[str], stream: bool = False) -> str:
    """SQL生成模型_景区"""
    # 1. 排名检测

    memory = "\n".join(history_texts)
    user_content = (
        f"本轮用户问题：{query}\n上一轮记忆：{memory}"
        if memory else f"本轮用户问题：{query}"
    )
    # 构造 messages 列表
    detect_messages = [
        {"role": "system", "content": RANK_DETECT_INSTRUCTIONS},
        {"role": "user", "content": f"本轮用户问题：{user_content}"}
    ]

    # print("=== RANK_scenic 调用前 ===")
    # for m in detect_messages:
    #     print(m)
    # print("=============================")


    detect_resp = invoke_llm(messages=detect_messages, stream=False).choices[0].message.content.strip()
    MONTH_DETECT_INSTRUCTIONS = build_month_prompt()
    month_messages = [
        {"role": "system", "content": MONTH_DETECT_INSTRUCTIONS},
        {"role": "user", "content": f"本轮用户问题：{user_content}"}
    ]
    
    month_resp = invoke_llm(messages=month_messages, stream=False).choices[0].message.content.strip()
    
    month_data = QueryService.get_system_month_scenic(month_resp)
    print(f"\n提取出的月份:{month_data}\n")

    SYSTEM_SCENIC_INSTRUCTIONS = build_scenic_prompt(month_data)
    RANK_SCENIC_INSTRUCTIONS = build_scenic_rank_prompt(month_data)

    system_instr = RANK_SCENIC_INSTRUCTIONS if detect_resp == "是" else SYSTEM_SCENIC_INSTRUCTIONS
    print(f"是不是排名: {detect_resp}")

    # 3. 调用模型
    prompt_messages = [
        {"role": "system", "content": system_instr},
        {"role": "user", "content": user_content}
    ]


    # print("=== convert_scenic_sql 调用大模型前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("======================================")

    resp = invoke_llm(messages=prompt_messages, stream=stream)

    # 4. 格式化并返回
    sql = resp.choices[0].message.content
    return validate_and_format(sql)


# ==================== 其他模型 ====================
def other_llm(query: str, stream: bool = False):
    """闲聊/其他意图调用"""
    prompt_messages = [
        {"role": "system", "content": OTHER_SYSTEM_PROMPT},
        {"role": "user", "content": query}
    ]

    # print("=== other_llm 调用大模型前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("==============================")

    return invoke_llm(messages=prompt_messages, stream=stream)


def transform_llm(q: str, history_texts: list[str], stream: bool = False):
    """标准化/补全本轮用户问题为"时间+地点+指标名"格式"""
    memory = "\n".join(history_texts)
    prompt_body = (
        f"本轮用户问题：{q}"
    )

    prompt_messages = [
        {"role": "system", "content": SYSTEM_TRANSFORM},
        {"role": "user", "content": prompt_body}
    ]

    # print("=== transform_llm 调用大模型前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("==============================")

    resp = invoke_llm(messages=prompt_messages, stream=stream)
    if not stream:
        return resp.choices[0].message.content.strip()
    return resp
def transform_llm_scenic(q: str, history_texts: list[str], stream: bool = False):
    """标准化/补全本轮用户问题为"时间+地点+指标名"格式"""
    memory = "\n".join(history_texts)
    prompt_body = (
        f"本轮用户问题：{q}"
    )

    prompt_messages = [
        {"role": "system", "content": SYSTEM_TRANSFORM_SCENIC},
        {"role": "user", "content": prompt_body}
    ]

    # print("=== transform_llm 调用大模型前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("==============================")

    resp = invoke_llm(messages=prompt_messages, stream=stream)
    if not stream:
        return resp.choices[0].message.content.strip()
    return resp
def completeness_llm_scenic(transformed_q: str, history_texts: list[str], stream: bool = False):
    """判定标准化后的问句是否已具备日期+地名+指标名三要素"""
    memory = "\n".join(history_texts)
    prompt_body = (
        f"本轮用户问题：{transformed_q}\n上一轮记忆：{memory}"
        if memory else
        f"本轮用户问题：{transformed_q}"
    )
    prompt_messages = [
        {"role": "system", "content": SYSTEM_COMPLETENESS_SCENIC},
        {"role": "user", "content": prompt_body}
    ]

    print("=== completeness_llm_scenic 调用大模型前 ===")
    # for m in prompt_messages:
    #    print(m)
    print("==============================")

    resp = invoke_llm(messages=prompt_messages, stream=stream)
    if not stream:
        return resp.choices[0].message.content.strip().lower()
    return resp


def incomplete_reason_llm_scenic(transformed_q: str, history_texts: list[str]) -> str:
    """当 completeness_llm 判定为 incomplete 时，进一步判断缺少的要素"""
    memory = "\n".join(history_texts)
    user_content = (
        f"待诊断的问题：{transformed_q}\n上一轮记忆：{memory}"
        if memory else f"待诊断的问题：{transformed_q}"
    )

    # print("=== incomplete_reason_llm 调用大模型前 ===")
    # print("system_prompt:", SYSTEM_INCOMPLETE_REASON)
    # print("user_prompt:\n", user_content)
    # print("=====================================")

    resp = invoke_llm(
        system_prompt=SYSTEM_INCOMPLETE_REASON_SCENIC,
        user_content=user_content,
        stream=False
    )
    print(resp.choices[0].message.content.strip().lower())
    return resp.choices[0].message.content.strip().lower()

def completeness_llm(transformed_q: str, stream: bool = False):
    """判定标准化后的问句是否已具备日期+地名+指标名三要素"""

    prompt_body = (
        f"本轮用户问题：{transformed_q}"
    )
    prompt_messages = [
        {"role": "system", "content": SYSTEM_COMPLETENESS},
        {"role": "user", "content": prompt_body}
    ]

    #print("=== completeness_llm 调用大模型前 ===")
    #for m in prompt_messages:
        #print(m)
    #print("==============================")

    resp = invoke_llm(messages=prompt_messages, stream=stream)
    if not stream:
        return resp.choices[0].message.content.strip().lower()
    return resp


def incomplete_reason_llm(transformed_q: str, history_texts: list[str]) -> str:
    """当 completeness_llm 判定为 incomplete 时，进一步判断缺少的要素"""
    memory = "\n".join(history_texts)
    user_content = (
        f"待诊断的问题：{transformed_q}\n上一轮记忆：{memory}"
        if memory else f"待诊断的问题：{transformed_q}"
    )

    # print("=== incomplete_reason_llm 调用大模型前 ===")
    # print("system_prompt:", SYSTEM_INCOMPLETE_REASON)
    # print("user_prompt:\n", user_content)
    # print("=====================================")

    resp = invoke_llm(
        system_prompt=SYSTEM_INCOMPLETE_REASON,
        user_content=user_content,
        stream=False
    )
    print(resp.choices[0].message.content.strip().lower())
    return resp.choices[0].message.content.strip().lower()


def fill_llm(transformed_q: str, history_texts: list[str], stream: bool = False):
    """当判定为 need fill 时，用记忆补全问句"""
    memory = "\n".join(history_texts)
    prompt_body = (
        f"本轮用户问题：{transformed_q}\n上一轮记忆：{memory}"
        if memory else
        f"本轮用户问题：{transformed_q}"
    )
    prompt_messages = [
        {"role": "system", "content": SYSTEM_FILL},
        {"role": "user", "content": prompt_body}
    ]

    # print("=== fill_llm 调用大模型前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("==============================")

    resp = invoke_llm(messages=prompt_messages, stream=stream)
    if not stream:
        return resp.choices[0].message.content.strip()
    return resp

def fill_llm_scenic(transformed_q: str, history_texts: list[str], stream: bool = False):
    """当判定为 need fill 时，用记忆补全问句"""
    memory = "\n".join(history_texts)
    prompt_body = (
        f"本轮用户问题：{transformed_q}\n上一轮记忆：{memory}"
        if memory else
        f"本轮用户问题：{transformed_q}"
    )
    prompt_messages = [
        {"role": "system", "content": SYSTEM_FILL_SCENIC},
        {"role": "user", "content": prompt_body}
    ]

    # print("=== fill_llm 调用大模型前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("==============================")

    resp = invoke_llm(messages=prompt_messages, stream=stream)
    if not stream:
        return resp.choices[0].message.content.strip()
    return resp

def summary_llm(current_text: str, history_texts: list[str], stream: bool = False):
    """汇总/总结意图调用"""
    prompt_body = (
        f"本轮用户问题：{current_text}"
    )
    prompt_messages = [
        {"role": "system", "content": SUMMARY_SYSTEM_PROMPT},
        {"role": "user", "content": prompt_body}
    ]

    # print("=== summary_llm 调用大模型前 ===")
    # for m in prompt_messages:
    #     print(m)
    # print("==============================")

    resp = invoke_llm(messages=prompt_messages, stream=stream)
    if not stream:
        return resp.choices[0].message.content
    return resp


# ==================== FastAPI 应用 & 流式接口 ====================
app = FastAPI()
query_semaphore = asyncio.Semaphore(Config.MAX_CONCURRENT_REQUESTS)

_last_emit_time: Optional[datetime] = None


@app.post("/check-change")
async def check_change(request: Request):
    """
    处理指标变更检查的API接口
    接收POST请求，根据请求参数决定是否需要更新指标文件
    """
    try:
        # 获取并打印原始请求体，用于调试
        body = await request.json()
        
        print("接收到 /check-change 请求体:", body)

        # 从请求体中解析关键字段
        # is_change: 是否有变更 (0=无变更, 1=有变更)
        # type:
        is_change = int(body.get("is_change", 0))
        change_type = int(body.get("type", 0))
        print(f"解析后 is_change: {is_change}, type: {change_type}")

        # 只有当 is_change = 1 时才进行指标更新
        if is_change == 1:
            if change_type == 1:
                # 区域指标更新
                print("触发区域指标更新")
                export_indicators_to_file()
                return {"status": "success", "message": "区域指标已更新并导出"}
            elif change_type == 2:
                # 景区指标更新
                print("触发景区指标更新")
                export_scenic_indicators_to_file()
                return {"status": "success", "message": "景区指标已更新并导出"}
            else:
                # 未知的变更类型
                print(f"未知的 change_type: {change_type}")
                return {"status": "error", "message": f"未知的 type 值: {change_type}"}
        else:
            # 没有变更，无需处理
            print("指标未更新，无需进一步处理")
            return {"status": "success", "message": "指标未更新，无需处理"}

    except Exception as e:
        # 捕获所有异常，防止服务崩溃
        print("处理 /check-change 接口时出错：", e)
        return {"status": "error", "message": "服务器内部错误"}

@app.post("/query-stream")
async def query_stream(request: Request):
    # —— 解析 JSON，并打印 ——
    body = await request.json()

    # —— 提取并打印关键字段 ——
    q = body.get("query", "").strip()
    user_id = body.get("userId")
    conv_id = body.get("conversationId")

    message_id = str(uuid.uuid4())
    # 并发控制：尝试在10秒内获取信号量，否则超时返回
    try:
        await asyncio.wait_for(query_semaphore.acquire(), timeout=10)
    except asyncio.TimeoutError as e:
        now=datetime.now()
        save_log_background(user_id, now, "query_stream", "并发超时", "", "error 503")
        return JSONResponse(
            status_code=503,
            content={"answer": "您的操作过于频繁，为了保障服务质量，请稍作休息后再试。感谢您的理解！"}
        )
    try:
        
        allowed_regions = body.get("region", [])
        allowed_indicators = body.get("indicator", [])

        print(f"当前问题: {q}")
        print(f"允许查询的地区: {allowed_regions}")
        print(f"允许查询的指标: {allowed_indicators}")

        # print(f"user_id: {user_id}, conversation_id: {conv_id}")

        # —— 以下为既有逻辑 ——
        N = 2
        history_texts = fetch_memory(user_id, conv_id)
        print(f"记忆：{history_texts}")
        window_history = history_texts[-N:] if len(history_texts) > N else history_texts
        print(f"窗口记忆：{window_history}")

        if not q:
            return {"error": "请在 JSON body 里提供 query 字段"}

        # 意图分类
        # new_q = question_generate(q, history_texts, stream=False)
        intent = classify_intent(q, history_texts)

        # —— RAG 分支 ——
        if intent == 'rag':
            docs = retriever.invoke(q)
            print("=== RAG 检索到的文档 ===")
            for i, d in enumerate(docs, 1):
                print(f"[{i}] {d.page_content}\n")
            context = "\n\n".join(d.page_content for d in docs)

            stream_resp = rag_llm(q + ',学习到的内容:' + context, history_texts, stream=True)

            collector = create_answer_collector()

            async def sse_rag():
                global _last_emit_time
                try:
                    for chunk in stream_resp:
                        token = getattr(chunk.choices[0].delta, "content", None)
                        if not token:
                            continue

                        # 收集答案
                        collector['chunks'].append(token)

                        now_dt = datetime.now()
                        if _last_emit_time and now_dt <= _last_emit_time:
                            now_dt = _last_emit_time + timedelta(milliseconds=1)
                        _last_emit_time = now_dt
                        now = now_dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                        print(token, end="", flush=True)
                        yield f"data: {json.dumps({'time': now, 'answer': token}, ensure_ascii=False)}\n\n"
                finally:
                    # 流式结束后在后台保存问答
                    full_answer = ''.join(collector['chunks'])
                    save_qa_background(user_id, conv_id, message_id, q, full_answer)
                    now = datetime.now()
                    save_log_background(user_id, now, q, full_answer, "", "success")
            return StreamingResponse(sse_rag(), media_type="text/event-stream")

        # —— 闲聊/其他分支 ——
        elif intent == 'other':
            stream_resp = other_llm(q, stream=True)

            collector = create_answer_collector()

            async def sse_other():
                global _last_emit_time
                try:
                    for chunk in stream_resp:
                        token = getattr(chunk.choices[0].delta, "content", None)
                        if not token:
                            continue

                        # 收集答案
                        collector['chunks'].append(token)

                        now_dt = datetime.now()
                        if _last_emit_time and now_dt <= _last_emit_time:
                            now_dt = _last_emit_time + timedelta(milliseconds=1)
                        _last_emit_time = now_dt
                        now = now_dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                        print(token, end="", flush=True)
                        yield f"data: {json.dumps({'time': now, 'answer': token}, ensure_ascii=False)}\n\n"
                finally:
                    # 流式结束后在后台保存问答
                    full_answer = ''.join(collector['chunks'])
                    save_qa_background(user_id, conv_id, message_id, q, full_answer)
                    now = datetime.now()
                    save_log_background(user_id, now, q, full_answer, "", "success")
            return StreamingResponse(sse_other(), media_type="text/event-stream")
        
        # —— 景区查询分支 ——
        elif intent == 'scenic':
            transformed_q = transform_llm_scenic(q, history_texts)
            print(f"转换后标准问句: {transformed_q}")

            # 2. 判定三要素完整性
            comp_resp = completeness_llm_scenic(transformed_q, history_texts)
            print(f"三要素判定结果: {comp_resp}")

            # 3. 若三要素不完整，则进一步诊断并给出建议
            if comp_resp == 'incomplete':
                missing = incomplete_reason_llm_scenic(transformed_q, history_texts)
                print(f"缺失为: {missing}")
                # 根据诊断结果生成建议
                if missing == '景区':
                    suggestion = "您这边没有提到具体景区，请问是不是想询问杭州市垂云通天河景区的相关数据呢？"
                elif missing == '时间':
                    suggestion = "您这边没有提到具体时间，请问是不是想询问2024年的相关数据呢？"
                elif missing == '指标名':
                    suggestion = "您这边没有提到具体指标名，请问是不是想询问到访人数的相关数据呢？"
                else:
                    suggestion = "请补充时间、地点或指标名中的一个要素，以便我帮您查询。"
                print(f"回答为: {suggestion}")
                async def sse_incomplete():
                    yield (
                            "data: "
                            + json.dumps({"answer": suggestion}, ensure_ascii=False)
                            + "\n\n"
                    )

                    # 在后台保存问答（使用原始问题）
                    save_qa_background(user_id, conv_id, message_id, q, suggestion)
                    now = datetime.now()
                    save_log_background(user_id, now, q, suggestion, "", "success")
                return StreamingResponse(sse_incomplete(), media_type="text/event-stream")

            # 4. 若需记忆补全
            if comp_resp == 'complete_with_memory':
                transformed_q = fill_llm_scenic(transformed_q, history_texts)
                print(f"补全后标准问句: {transformed_q}")

            # # 5. 问题资格检测
            # permission_result = permission_check_llm_scenic(transformed_q, history_texts, allowed_regions, allowed_indicators)

            # if permission_result != "有权限":
            #     async def sse_denied():
            #         yield (
            #                 "data: "
            #                 + json.dumps({"answer": permission_result}, ensure_ascii=False)
            #                 + "\n\n"
            #         )

            #         # 在后台保存问答（使用转换后的问题）
            #         save_qa_background(user_id, conv_id, message_id, transformed_q, permission_result)
            #         now = datetime.now()
            #         save_log_background(user_id, now, transformed_q, permission_result, "", "success")
            #     return StreamingResponse(sse_denied(), media_type="text/event-stream")
            sql = convert_scenic_sql(transformed_q, history_texts, stream=False)
            print(f"Scenic SQL:\n{sql}\n")
            rows = query_database(sql)
            print(f"查询结果:\n{rows}\n")
            print(f"上一轮记忆：\n{history_texts}\n")
            payload = (
                f"{transformed_q}\n"
                f"SQL语句：{sql}\n查询结果：{rows}\n"
                f"上一轮记忆：{''.join(history_texts)}"
            )

            # 创建答案收集器
            collector = create_answer_collector()

            stream_resp = summary_llm(payload, history_texts, stream=True)


            async def sse_summary():
                global _last_emit_time
                buffer = ""  # 添加缓冲区
                print(">>> sse_summary() 启动了")
                try:
                    for chunk in stream_resp:
                        token = getattr(chunk.choices[0].delta, "content", None)
                        if not token:
                            continue
                        collector['chunks'].append(token)
                        buffer += token  # 累积到缓冲区

                        # 每10个字符或遇到标点符号就发送
                        if len(buffer) >= 10 or any(p in buffer for p in ['。', '！', '？', '\n']):
                            now_dt = datetime.now()
                            if _last_emit_time and now_dt <= _last_emit_time:
                                now_dt = _last_emit_time + timedelta(milliseconds=1)
                            _last_emit_time = now_dt
                    
                            print(buffer, end="", flush=True)
                            yield f"data: {json.dumps({'answer': buffer}, ensure_ascii=False)}\n\n"
                            buffer = ""  # 清空缓冲区

                            # 添加延迟，避免发送太快
                            await asyncio.sleep(0.05)
                    # 发送剩余内容
                    if buffer:
                        yield f"data: {json.dumps({'answer': buffer}, ensure_ascii=False)}\n\n"
                finally:
                    # 流式结束后在后台保存问答
                    full_answer = ''.join(collector['chunks'])
                    save_qa_background(user_id, conv_id, message_id, transformed_q, full_answer)
                    now = datetime.now()
                    save_log_background(user_id, now, q, full_answer, sql, "success")
            return StreamingResponse(sse_summary(), media_type="text/event-stream")
        # —— 行政区查询分支 ——
        elif intent == 'admin':
            transformed_q = transform_llm(q, history_texts)
            print(f"转换后标准问句: {transformed_q}")

            # 2. 判定三要素完整性
            comp_resp = completeness_llm(transformed_q)
            print(f"三要素判定结果: {comp_resp}")

            # 3. 若三要素不完整，则进一步诊断并给出建议
            if comp_resp == 'incomplete':
                filled_q = fill_llm(transformed_q, history_texts)
                # 4. 再次判定“补全后”是否齐全
                comp_filled = completeness_llm(filled_q)
                if  comp_filled == 'incomplete':
                    missing = incomplete_reason_llm(transformed_q, history_texts)
                    # 根据诊断结果生成建议
                    if missing == '地点':
                        suggestion = "您这边没有提到具体地点，请问是不是想询问浙江省的相关数据呢？"
                    elif missing == '时间':
                        suggestion = "您这边没有提到具体时间，请问是不是想询问2024年的相关数据呢？"
                    elif missing == '指标名':
                        suggestion = "您这边没有提到具体指标名，请问是不是想询问全域旅游人数的相关数据呢？"
                    else:
                        suggestion = "请补充时间、地点或指标名中的一个要素，以便我帮您查询。"

                    async def sse_incomplete():
                        yield (
                                "data: "
                                + json.dumps({"answer": suggestion}, ensure_ascii=False)
                                + "\n\n"
                        )

                        # 在后台保存问答（使用原始问题）
                        save_qa_background(user_id, conv_id, message_id, q, suggestion)
                        now = datetime.now()
                        save_log_background(user_id, now, q, suggestion, "", "success")
                    return StreamingResponse(sse_incomplete(), media_type="text/event-stream")
                else:
                    print(f"补全后标准问句: {filled_q}")
                    transformed_q = filled_q

            # 5. 问题资格检测
            permission_result = permission_check_llm(transformed_q, history_texts, allowed_regions, allowed_indicators)

            if permission_result != "有权限":
                async def sse_denied():
                    yield (
                            "data: "
                            + json.dumps({"answer": permission_result}, ensure_ascii=False)
                            + "\n\n"
                    )

                    # 在后台保存问答（使用转换后的问题）
                    save_qa_background(user_id, conv_id, message_id, transformed_q, permission_result)
                    now = datetime.now()
                    save_log_background(user_id, now, transformed_q, permission_result, "", "success")
                return StreamingResponse(sse_denied(), media_type="text/event-stream")

            #match_result = indicator_match_llm(transformed_q, history_texts)

            # if match_result == "完全匹配":
            #     # 如果完全匹配，直接生成简单的查询SQL（不需要计算）
            #     # 这里需要一个专门的函数来生成直接查询的SQL
            #     sql = convert_direct_query_sql(transformed_q, history_texts, stream=False)
            #     print(f"直接查询 SQL:\n{sql}\n")
            #     rows = query_database(sql)
            #     print(f"查询结果:\n{rows}\n")
            # else:
                # 6. 生成 Admin SQL 并查询（原有逻辑）
            sql = convert_admin_sql(transformed_q, history_texts, stream=False)
            print(f"Admin SQL:\n{sql}\n")
            rows = query_database(sql)
            print(f"查询结果:\n{rows}\n")

            # 7. 汇总并流式返回
            payload = (
                f"{transformed_q}\n"
                f"SQL语句：{sql}\n查询结果：{rows}\n"
                f"上一轮记忆：{''.join(history_texts)}"
            )
            stream_resp = summary_llm(payload, history_texts, stream=True)

            # 创建答案收集器
            collector = create_answer_collector()

            # 流式返回
            async def sse_summary():
                global _last_emit_time
                buffer = ""  # 添加缓冲区

                try:
                    for chunk in stream_resp:
                        token = getattr(chunk.choices[0].delta, "content", None)
                        if not token:
                            continue

                        # 收集答案
                        collector['chunks'].append(token)
                        buffer += token  # 累积到缓冲区

                        # 每10个字符或遇到标点符号就发送
                        if len(buffer) >= 10 or any(p in buffer for p in ['。', '！', '？', '\n']):
                            now_dt = datetime.now()
                            if _last_emit_time and now_dt <= _last_emit_time:
                                now_dt = _last_emit_time + timedelta(milliseconds=1)
                            _last_emit_time = now_dt

                            print(buffer, end="", flush=True)
                            yield f"data: {json.dumps({'answer': buffer}, ensure_ascii=False)}\n\n"
                            buffer = ""  # 清空缓冲区

                            # 添加延迟，避免发送太快
                            await asyncio.sleep(0.05)

                    # 发送剩余内容
                    if buffer:
                        yield f"data: {json.dumps({'answer': buffer}, ensure_ascii=False)}\n\n"

                finally:
                    # 流式结束后在后台保存问答
                    full_answer = ''.join(collector['chunks'])
                    save_qa_background(user_id, conv_id, message_id, transformed_q, full_answer)
                    now = datetime.now()
                    save_log_background(user_id, now, q, full_answer, sql, "success")
            return StreamingResponse(sse_summary(), media_type="text/event-stream")

        # —— 未知意图 ——
        else:
            return {"error": f"无法识别的意图类型: {intent}"}

    except Exception as e:
        # 打印日志，方便排查真正的异常
        print("处理请求时出错：", e)
        # 直接返回固定提示
        now = datetime.now()
        save_log_background(user_id, now, q, f"流程中断：{e}", "", "error")
        return {"answer": "抱歉，我现在暂时无法回答您的问题"}
    finally:
        # 释放信号量，允许下一个排队请求进入
        try:
            query_semaphore.release()
        except ValueError:
            pass

@app.post("/parse-document")
async def parse_document(body: dict = Body(...)):
    file_url = body.get("file_url")
    if not file_url:
        return JSONResponse(content={
            "code": 1,
            "msg": "缺少 file_url 参数",
            "data": None
        })

    import uuid, os, requests
    suffix = os.path.splitext(file_url)[-1]
    tmp_name = f"/tmp/{uuid.uuid4()}{suffix}"
    try:
        resp = requests.get(file_url, timeout=30)
        resp.raise_for_status()
        with open(tmp_name, "wb") as f:
            f.write(resp.content)
    except Exception as e:
        return JSONResponse(content={
            "code": 1,
            "msg": f"文件下载失败: {str(e)}",
            "data": None
        })
    print(f"[INFO] 文件已下载至: {tmp_name}")

    segments = split_document_to_segments(tmp_name)
    return JSONResponse(content={
        "code": 0,
        "msg": "success",
        "data": {"segments": segments}
    })

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)