"use client";

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Nested<PERSON>ie<PERSON><PERSON>, Simple<PERSON>ie<PERSON><PERSON> } from "@/components/chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { D3ChartContainerProps, D3ComboChartConfig, D3NestedPieChartConfig } from "@/components/chart/types/chartTypes";

/**
 * 智能图表渲染器 - D3.js版本
 * 根据chartConfig.type动态渲染对应的D3图表组件
 */
export default function SmartChartRenderer({
  chartConfig,
  width = 800,
  height = 500,
  className = "",
  onEdit,
  onDelete
}: D3ChartContainerProps) {
  
  // 如果没有配置，显示占位符
  if (!chartConfig) {
    return (
      <Card className={`w-full ${className}`}>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center text-gray-500">
            <div className="text-4xl mb-2">📊</div>
            <p>暂无图表数据</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  /**
   * 渲染组合图（柱状图+折线图）
   */
  const renderComboChart = (config: D3ComboChartConfig) => {
    // 转换数据格式为ComboChart组件期望的格式
    const comboData = config.categories.map((category, index) => {
      const barDataset = config.datasets.find(d => d.type === 'bar');
      const lineDataset = config.datasets.find(d => d.type === 'line');
      
      return {
        city: category,
        visitors: barDataset?.data[index] || 0,
        growth: lineDataset?.data[index] || 0
      };
    });

    return (
      <ComboChart
        data={comboData}
        width={width}
        height={height}
        title={config.title}
        barColor="#4A90E2"
        lineColor="#7ED321"
        className=""
      />
    );
  };

  /**
   * 渲染嵌套饼图
   */
  const renderNestedPieChart = (config: D3NestedPieChartConfig) => {
    // 检查是否是新的数据结构格式
    if ('data' in config && config.data) {
      // 新格式：直接使用后端提供的数据结构
      const data = {
        outer: config.data.outer.map((item: any, index: number) => ({
          name: item.name,
          value: item.value,
          color: getColorByIndex(index)
        })),
        inner: config.data.inner.map((item: any, index: number) => ({
          name: item.name,
          value: item.value,
          color: getColorByIndex(index, 1),
          parent: item.parent
        }))
      };

      return (
        <NestedPieChart
          data={data}
          width={width}
          height={height}
          title={config.title}
          showPercentages={true}
          className=""
        />
      );
    }

    // 旧格式：转换数据格式为NestedPieChart组件期望的格式（向后兼容）
    const outerData = (config.datasets || []).map((item, index) => ({
      name: item.name,
      value: item.value,
      color: getColorByIndex(index)
    }));

    const innerData = (config.datasets || [])
      .filter(item => item.children && item.children.length > 0)
      .flatMap((item, parentIndex) => 
        item.children!.map((child, childIndex) => ({
          name: child.name,
          value: child.value,
          color: getColorByIndex(parentIndex, childIndex + 1),
          parent: item.name
        }))
      );

    const nestedData = {
      outer: outerData,
      inner: innerData
    };

    return (
      <NestedPieChart
        data={nestedData}
        width={width}
        height={height}
        title={config.title}
        showPercentages={true}
        className=""
      />
    );
  };

  /**
   * 渲染简单饼图
   */
  const renderSimplePieChart = (config: any) => {
    const data = config.datasets.map((item: any, index: number) => ({
      name: item.name,
      value: item.value,
      color: getColorByIndex(index)
    }));

    return (
      <SimplePieChart
        data={data}
        width={width}
        height={height}
        title={config.title}
        showPercentages={true}
        showLegend={true}
        className=""
      />
    );
  };

  /**
   * 根据索引获取颜色
   */
  const getColorByIndex = (index: number, variant: number = 0): string => {
    const colors = [
      ['#4A90E2', '#6BA3F0'],
      ['#7ED321', '#92E639'], 
      ['#F5A623', '#F7B955'],
      ['#D0021B', '#E74C3C'],
      ['#9013FE', '#B39DDB'],
      ['#50E3C2', '#7FECDC']
    ];
    
    const colorSet = colors[index % colors.length];
    return colorSet?.[variant] || colorSet?.[0] || '#4A90E2';
  };

  /**
   * 渲染图表标题和操作按钮
   */
  const renderHeader = () => (
    <CardHeader>
      <CardTitle className="flex justify-between items-center">
        <span className="text-lg font-semibold">{chartConfig.title}</span>
        <div className="flex gap-2">
          {onEdit && (
            <Button variant="outline" size="sm" onClick={onEdit}>
              编辑
            </Button>
          )}
          {onDelete && (
            <Button variant="outline" size="sm" onClick={onDelete}>
              删除
            </Button>
          )}
        </div>
      </CardTitle>
    </CardHeader>
  );

  /**
   * 根据配置类型渲染对应的图表组件
   */
  const renderChart = () => {
    switch (chartConfig.type) {
      case 'combo':
        return renderComboChart(chartConfig as D3ComboChartConfig);
      
      case 'nested-pie':
        return renderNestedPieChart(chartConfig as D3NestedPieChartConfig);
      
      case 'simple-pie':
        return renderSimplePieChart(chartConfig as any);
      
      default:
        return (
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <div className="text-4xl mb-2">⚠️</div>
              <p>不支持的图表类型: {(chartConfig as any).type}</p>
            </div>
          </div>
        );
    }
  };

  return (
    <Card className={`w-full ${className}`}>
      {renderHeader()}
      <CardContent>
        <div className="w-full flex justify-center">
          {renderChart()}
        </div>
        
        {/* 开发模式下显示配置信息 */}
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4 p-3 bg-gray-50 rounded text-xs">
            <summary className="cursor-pointer font-medium text-gray-700">
              🔧 开发信息 (点击展开)
            </summary>
            <pre className="mt-2 overflow-auto text-gray-600">
              {JSON.stringify(chartConfig, null, 2)}
            </pre>
          </details>
        )}
      </CardContent>
    </Card>
  );
}
