import { apiClient } from './client';

export interface ChartConfig {
  type: string;
  data: any;
  options?: any;
}

export interface ChartDataRequest {
  query: string;
  parameters?: any;
}

// 图表相关API
export const chartsApi = {
  // 获取图表数据
  async getChartData(config: ChartDataRequest): Promise<any> {
    const response = await apiClient.post('/api/charts/data', config);
    return response.data;
  },

  // 保存图表配置
  async saveChartConfig(config: ChartConfig): Promise<any> {
    const response = await apiClient.post('/api/charts/config', config);
    return response.data;
  },

  // 获取图表配置
  async getChartConfig(id: string): Promise<ChartConfig> {
    const response = await apiClient.get<ChartConfig>(`/api/charts/config/${id}`);
    return response.data;
  },

  // 删除图表配置
  async deleteChartConfig(id: string): Promise<{ success: boolean }> {
    await apiClient.delete(`/api/charts/config/${id}`);
    return { success: true };
  },
}; 