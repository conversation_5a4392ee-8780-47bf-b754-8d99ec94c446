"use client";

import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';
import { SimplePieChartProps } from './types/chartTypes';

export default function SimplePieChart({
  data,
  width = 500,
  height = 400,
  className = "",
  title = "",
  showPercentages = true,
  innerRadius = 0,
  outerRadius,
  showLabels = true,
  labelPosition = 'outside',
  animate = true,
  animationDuration = 750,
  colors = [],
  showLegend = true,
  interactive = true
}: SimplePieChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data || data.length === 0) return;

    // 清空之前的内容
    d3.select(svgRef.current).selectAll("*").remove();

    const svg = d3.select(svgRef.current);
    const margin = { top: 60, right: 120, bottom: 60, left: 120 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // 计算半径
    const radius = Math.min(innerWidth, innerHeight) / 2;
    const actualOuterRadius = outerRadius || radius;
    const actualInnerRadius = innerRadius;

    // 创建主容器
    const container = svg.append('g')
      .attr('transform', `translate(${width / 2}, ${height / 2})`);

    // 创建颜色比例尺
    const colorScale = d3.scaleOrdinal<string>()
      .domain(data.map(d => d.name))
      .range(colors.length > 0 ? colors : [
        '#4A90E2', '#7ED321', '#F5A623', '#D0021B', '#9013FE', 
        '#50E3C2', '#B8E986', '#F8E71C', '#BD10E0', '#B8E986'
      ]);

    // 创建饼图生成器
    const pie = d3.pie<any>()
      .value(d => d.value)
      .sort(null);

    // 创建弧生成器
    const arc = d3.arc<any>()
      .innerRadius(actualInnerRadius)
      .outerRadius(actualOuterRadius);

    // 创建标签弧生成器
    const labelArc = d3.arc<any>()
      .innerRadius(actualOuterRadius * 1.2)
      .outerRadius(actualOuterRadius * 1.2);

    // 创建工具提示
    const tooltip = interactive ? d3.select('body').append('div')
      .attr('class', 'pie-tooltip')
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background', 'rgba(0, 0, 0, 0.8)')
      .style('color', 'white')
      .style('padding', '8px 12px')
      .style('border-radius', '4px')
      .style('font-size', '12px')
      .style('pointer-events', 'none')
      .style('z-index', '1000') : null;

    // 计算总值和百分比
    const total = d3.sum(data, d => d.value);
    const dataWithPercentages = data.map(d => ({
      ...d,
      percentage: (d.value / total) * 100
    }));

    // 绘制饼图
    const arcs = container.selectAll('.arc')
      .data(pie(dataWithPercentages))
      .enter()
      .append('g')
      .attr('class', 'arc');

    // 绘制扇形
    const paths = arcs.append('path')
      .attr('fill', d => d.data.color || colorScale(d.data.name))
      .style('cursor', interactive ? 'pointer' : 'default')
      .attr('stroke', 'white')
      .attr('stroke-width', 2);

    // 动画
    if (animate) {
      paths
        .attr('d', d => {
          const startArc = { ...d, startAngle: 0, endAngle: 0 };
          return arc(startArc);
        })
        .transition()
        .duration(animationDuration)
        .ease(d3.easeQuadInOut)
        .attrTween('d', function(d) {
          const interpolate = d3.interpolate({ startAngle: 0, endAngle: 0 }, d);
          return function(t) {
            return arc(interpolate(t)) || '';
          };
        });
    } else {
      paths.attr('d', arc);
    }

    // 添加交互效果
    if (interactive && tooltip) {
      paths
        .on('mouseover', function(event, d) {
          d3.select(this).style('opacity', 0.8);
          tooltip
            .style('visibility', 'visible')
            .html(`
              <strong>${d.data.name}</strong><br/>
              值: ${d.data.value.toLocaleString()}<br/>
              占比: ${d.data.percentage.toFixed(1)}%
            `)
            .style('left', `${event.pageX + 10}px`)
            .style('top', `${event.pageY - 10}px`);
        })
        .on('mouseout', function() {
          d3.select(this).style('opacity', 1);
          tooltip.style('visibility', 'hidden');
        });
    }

    // 添加标签
    if (showLabels) {
      const labels = arcs.append('text')
        .attr('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', '#374151')
        .style('font-weight', 'bold');

      if (labelPosition === 'outside') {
        labels
          .attr('transform', d => `translate(${labelArc.centroid(d)})`)
          .text(d => {
            if (showPercentages) {
              return `${d.data.name} (${d.data.percentage.toFixed(1)}%)`;
            }
            return d.data.name;
          });

        // 添加连接线
        arcs.append('polyline')
          .attr('stroke', '#999')
          .attr('stroke-width', 1)
          .attr('fill', 'none')
          .attr('points', d => {
            const pos = labelArc.centroid(d);
            const midAngle = d.startAngle + (d.endAngle - d.startAngle) / 2;
            pos[0] = actualOuterRadius * 0.95 * (midAngle < Math.PI ? 1 : -1);
            return [arc.centroid(d), labelArc.centroid(d), pos].map(point => point.join(',')).join(' ');
          });
      } else {
        labels
          .attr('transform', d => `translate(${arc.centroid(d)})`)
          .text(d => {
            if (showPercentages) {
              return `${d.data.percentage.toFixed(1)}%`;
            }
            return d.data.name;
          })
          .style('fill', 'white')
          .style('font-size', '11px');
      }
    }

    // 添加标题
    if (title) {
      svg.append('text')
        .attr('x', width / 2)
        .attr('y', 30)
        .attr('text-anchor', 'middle')
        .style('font-size', '16px')
        .style('font-weight', 'bold')
        .style('fill', '#374151')
        .text(title);
    }

    // 添加图例
    if (showLegend) {
      const legend = svg.append('g')
        .attr('class', 'legend')
        .attr('transform', `translate(${width - 100}, 80)`);

      const legendItems = legend.selectAll('.legend-item')
        .data(dataWithPercentages)
        .enter()
        .append('g')
        .attr('class', 'legend-item')
        .attr('transform', (d, i) => `translate(0, ${i * 20})`);

      legendItems.append('rect')
        .attr('width', 12)
        .attr('height', 12)
        .attr('fill', d => d.color || colorScale(d.name));

      legendItems.append('text')
        .attr('x', 18)
        .attr('y', 9)
        .style('font-size', '12px')
        .style('fill', '#374151')
        .text(d => `${d.name} (${d.percentage.toFixed(1)}%)`);
    }

    // 清理函数
    return () => {
      if (tooltip) {
        tooltip.remove();
      }
    };

  }, [data, width, height, title, showPercentages, innerRadius, outerRadius, showLabels, labelPosition, animate, animationDuration, colors, showLegend, interactive]);

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="border border-gray-200 rounded max-w-full h-auto"
        viewBox={`0 0 ${width} ${height}`}
      />
    </div>
  );
}

// 导出示例数据和使用方法
export const exampleData = [
  { name: "产品A", value: 35, color: "#4A90E2" },
  { name: "产品B", value: 25, color: "#7ED321" },
  { name: "产品C", value: 20, color: "#F5A623" },
  { name: "产品D", value: 15, color: "#D0021B" },
  { name: "产品E", value: 5, color: "#9013FE" }
];

// 使用示例组件
export function SimplePieChartExample() {
  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-bold mb-8 text-center">单一饼图示例</h1>
      
      {/* 标准饼图 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">标准饼图</h2>
        <SimplePieChart 
          data={exampleData}
          width={600}
          height={400}
          title="产品市场份额"
          className="max-w-4xl mx-auto"
        />
      </div>

      {/* 环形图 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">环形图</h2>
        <SimplePieChart 
          data={exampleData}
          width={600}
          height={400}
          title="产品市场份额（环形）"
          innerRadius={80}
          labelPosition="inside"
          className="max-w-4xl mx-auto"
        />
      </div>

      {/* 无图例饼图 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">无图例饼图</h2>
        <SimplePieChart 
          data={exampleData}
          width={500}
          height={400}
          title="产品市场份额（无图例）"
          showLegend={false}
          className="max-w-4xl mx-auto"
        />
      </div>
    </div>
  );
} 