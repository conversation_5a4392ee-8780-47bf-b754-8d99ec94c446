import { apiClient } from './client';

export interface Report {
  id: string;
  title: string;
  content: string;
  created_at: string;
  updated_at: string;
}

export interface CreateReportRequest {
  title: string;
  content?: string;
}

export interface UpdateReportRequest {
  title?: string;
  content?: string;
}

// 报告相关API
export const reportsApi = {
  // 获取报告列表
  async getReports(): Promise<Report[]> {
    const response = await apiClient.get<Report[]>('/api/reports');
    return response.data;
  },

  // 获取单个报告
  async getReport(id: string): Promise<Report> {
    const response = await apiClient.get<Report>(`/api/reports/${id}`);
    return response.data;
  },

  // 创建新报告
  async createReport(data: CreateReportRequest): Promise<Report> {
    const response = await apiClient.post<Report>('/api/reports', data);
    return response.data;
  },

  // 更新报告
  async updateReport(id: string, data: UpdateReportRequest): Promise<Report> {
    const response = await apiClient.put<Report>(`/api/reports/${id}`, data);
    return response.data;
  },

  // 删除报告
  async deleteReport(id: string): Promise<{ success: boolean }> {
    await apiClient.delete(`/api/reports/${id}`);
    return { success: true };
  },

  // 保存报告的Puck数据
  async saveReportPuckData(reportId: string, puckData: any): Promise<Report> {
    const reportData = {
      content: JSON.stringify(puckData),
      updated_at: new Date().toISOString(),
    };
    
    return this.updateReport(reportId, reportData);
  },

  // 加载报告的Puck数据
  async loadReportPuckData(reportId: string): Promise<any> {
    const report = await this.getReport(reportId);
    if (report.content) {
      try {
        return JSON.parse(report.content);
      } catch (e) {
        console.warn('无法解析Puck数据，返回空结构');
        return { content: [], root: { props: {} } };
      }
    }
    return { content: [], root: { props: {} } };
  },
}; 