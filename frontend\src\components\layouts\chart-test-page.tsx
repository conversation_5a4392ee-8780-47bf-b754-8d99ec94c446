"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RefreshCw, Upload, Download } from "lucide-react";
import SmartChartRenderer from "../features/report-editor/smart-chart-renderer";
import { wordApi } from "@/api/word";
import { D3ChartConfig } from "@/components/chart/types/chartTypes";

interface ChartConfigInfo {
  id: string;
  title: string;
  type: string;
  configFile: string;
  lastModified: number;
}

export default function ChartTestPage() {
  const [availableCharts, setAvailableCharts] = useState<ChartConfigInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadResult, setUploadResult] = useState<any>(null);

  // 加载可用图表配置
  const loadAvailableCharts = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("http://localhost:8000/api/charts/configs");
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        setAvailableCharts(result.charts || []);
      } else {
        throw new Error("获取图表列表失败");
      }
    } catch (err) {
      console.error("加载图表配置失败:", err);
      setError(err instanceof Error ? err.message : "加载失败");
    } finally {
      setLoading(false);
    }
  };

  // 处理文件上传
  const handleFileUpload = async () => {
    if (!selectedFile) return;

    try {
      setLoading(true);
      setError(null);
      setUploadResult(null);

      // 使用标准处理接口
      const result = await wordApi.uploadAndProcessDocument(selectedFile);
      setUploadResult(result);
      console.log('处理结果:', result);
      
      // 上传成功后刷新图表列表
      await loadAvailableCharts();
    } catch (err) {
      console.error("文件上传失败:", err);
      setError(err instanceof Error ? err.message : "上传失败");
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载图表
  useEffect(() => {
    loadAvailableCharts();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            智能报告生成系统 - 图表测试
          </h1>
          <p className="text-gray-600">
            测试Word文档上传、图表提取和D3.js渲染功能
          </p>
          <div className="mt-2 text-sm text-blue-600">
            🆕 支持标准化图表配置，包括组合图和嵌套饼图
          </div>
        </div>

        {/* 文件上传区域 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="w-5 h-5 mr-2" />
              Word文档上传测试
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input
                  type="file"
                  accept=".docx,.doc"
                  onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                  className="flex-1 p-2 border rounded"
                  disabled={loading}
                />
                <Button
                  onClick={handleFileUpload}
                  disabled={!selectedFile || loading}
                  className="flex items-center"
                >
                  {loading ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="w-4 h-4 mr-2" />
                  )}
                  处理文档
                </Button>
              </div>

              {/* 处理结果 */}
              {uploadResult && (
                <div className="bg-green-50 border border-green-200 rounded p-4">
                  <h3 className="font-medium text-green-800 mb-2">
                    ✅ 文档处理完成
                  </h3>
                  <div className="text-sm text-green-700">
                    <p>状态: {uploadResult.success ? '成功' : '失败'}</p>
                    <p>图表数量: {uploadResult.charts?.length || 0}</p>
                    <p>表格数量: {uploadResult.tables?.length || 0}</p>
                    {uploadResult.errors && uploadResult.errors.length > 0 && (
                      <div className="mt-2">
                        <p className="font-medium">错误信息:</p>
                        <ul className="list-disc list-inside">
                          {uploadResult.errors.map((error: string, index: number) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 控制面板 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>可用图表配置</span>
              <Button
                onClick={loadAvailableCharts}
                disabled={loading}
                variant="outline"
                size="sm"
              >
                <RefreshCw
                  className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
                />
                刷新
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {error && (
              <div className="bg-red-50 border border-red-200 rounded p-4 mb-4">
                <p className="text-red-700">❌ {error}</p>
              </div>
            )}

            {loading && availableCharts.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <RefreshCw className="w-8 h-8 mx-auto mb-2 animate-spin" />
                加载中...
              </div>
            ) : availableCharts.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>暂无图表配置文件</p>
                <p className="text-sm mt-2">请先上传Word文档进行图表提取</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableCharts.map((chart) => (
                  <div key={chart.id} className="border rounded p-4 bg-white">
                    <h3 className="font-medium mb-2">{chart.title}</h3>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>ID: {chart.id}</p>
                      <p>类型: {chart.type}</p>
                      <p>
                        更新:{" "}
                        {new Date(chart.lastModified * 1000).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 图表展示 */}
        {uploadResult && uploadResult.success && uploadResult.charts && uploadResult.charts.length > 0 && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">图表展示</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {uploadResult.charts.map((chart: any) => (
                <div key={chart.id} className="border rounded-lg p-4">
                  <div className="mb-4">
                    <h3 className="font-semibold text-lg">{chart.title}</h3>
                    <div className="text-sm text-gray-500 flex gap-4">
                      <span>ID: {chart.id}</span>
                      <span>类型: {chart.config.type}</span>
                    </div>
                  </div>
                  
                  <SmartChartRenderer
                    chartConfig={chart.config}
                    width={500}
                    height={350}
                    onEdit={() => {
                      console.log("编辑图表:", chart.id);
                      alert(`编辑图表: ${chart.id}`);
                    }}
                    onDelete={() => {
                      console.log("删除图表:", chart.id);
                      alert(`删除图表: ${chart.id}`);
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 测试数据展示 */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>示例代码块测试</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-100 p-4 rounded text-sm font-mono">
              <pre>{`<!-- D3.js图表占位符 -->
\`\`\`d3js
{
  "chartId": "chart_1",
  "title": "图1 2025年1-3月浙江省全域游客客源结构",
  "description": "省内游客市场份额占比超七成",
  "configFile": "/api/charts/chart_1_config.json"
}
\`\`\`

*图表支持SVG导出和交互功能*`}</pre>
            </div>
          </CardContent>
        </Card>

        {/* 系统状态 */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>系统状态检查</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="p-4 bg-green-50 rounded">
                <div className="text-2xl font-bold text-green-600">✅</div>
                <div className="text-sm font-medium">前端组件</div>
              </div>
              <div className="p-4 bg-green-50 rounded">
                <div className="text-2xl font-bold text-green-600">✅</div>
                <div className="text-sm font-medium">D3.js集成</div>
              </div>
              <div className="p-4 bg-blue-50 rounded">
                <div className="text-2xl font-bold text-blue-600">
                  {availableCharts.length}
                </div>
                <div className="text-sm font-medium">可用图表</div>
              </div>
              <div className="p-4 bg-purple-50 rounded">
                <div className="text-2xl font-bold text-purple-600">🚀</div>
                <div className="text-sm font-medium">第三阶段</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
