"use client";

import React, { useState, useEffect } from "react";
import {
  DndContext,
  closestCenter,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
  DragEndEvent,
} from "@dnd-kit/core";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { wordApi, WordParseResultD3 } from '@/api/word';
import { D3ChartConfig } from '@/components/chart/types/chartTypes';
import SmartChartRenderer from './smart-chart-renderer';
import dynamic from "next/dynamic";
import Link from "next/link";

const Split = dynamic(() => import("@uiw/react-split"), {
  ssr: false,
});
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Save, Download, Loader2, X } from "lucide-react";

// 导入子组件
import MarkdownEditor from "./markdown-editor";
import ChatPanel from "./chat-panel";
import TemplateAssetList from "./template-asset-list";

interface ReportTemplate {
  id: number;
  title: string;
  structure: any;
  content: string;
  dataBindings: { id: string; title: string }[];
  chartBindings: { id: string; title: string }[];
  charts?: Array<{
    id: string;
    chartId: string;
    title: string;
    config: D3ChartConfig;
  }>;
}

interface ReportBuilderProps {
  initialTemplate?: ReportTemplate;
}

export default function ReportBuilder({ initialTemplate }: ReportBuilderProps) {
  const [template, setTemplate] = useState<ReportTemplate | null>(
    initialTemplate || null
  );
  const [markdownContent, setMarkdownContent] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingList, setIsFetchingList] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDirty, setIsDirty] = useState(false);
  const [leftTab, setLeftTab] = useState("tables");
  const [availableTemplates, setAvailableTemplates] = useState<
    { id: number; filename: string }[]
  >([]);
  const [chartConfigs, setChartConfigs] = useState<D3ChartConfig[]>([]);
  const [selectedChartIndex, setSelectedChartIndex] = useState<number | null>(null);
  const [selectedText, setSelectedText] = useState<string>("");

  // 设置 dnd-kit 传感器
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor)
  );

  // 获取可用模板列表
  useEffect(() => {
    const fetchTemplates = async () => {
      setIsFetchingList(true);
      setError(null);
      try {
        const data = await wordApi.getProcessedDocuments();
        setAvailableTemplates(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "发生了未知错误");
        console.error("获取模板列表失败:", err);
      } finally {
        setIsFetchingList(false);
      }
    };

    fetchTemplates();
  }, []);

  // 加载模板数据
  useEffect(() => {
    if (template) {
      // setMarkdownContent(template.content);
    }
  }, [template]);

  // 处理模板选择
  const handleTemplateSelect = async (templateIdStr: string) => {
    const templateId = Number(templateIdStr);
    if (isNaN(templateId)) return;

    setIsLoading(true);
    setError(null);
    console.log(`Fetching template: ${templateId}`);

    try {
      const response = await fetch(
        `http://localhost:8000/api/word-format/documents/${templateId}`
      );
      if (!response.ok) {
        throw new Error(`获取模板详情失败 (ID: ${templateId})`);
      }
      const data = await response.json();

      // 检查是否有图表数据
      const hasCharts = data.charts && data.charts.length > 0;
      console.log('Template data loaded:', {
        hasCharts,
        chartCount: hasCharts ? data.charts.length : 0,
        charts: data.charts
      });

      const fullTemplateData: ReportTemplate = {
        id: data.id,
        title: data.filename,
        content: data.markdown_content,
        dataBindings: data.tables || [],
        chartBindings: data.charts || [],
        charts: data.charts || [],
        structure: {}, // 设置为空对象
      };

      setTemplate(fullTemplateData);
      setMarkdownContent(fullTemplateData.content);
      
      // 设置图表配置
      if (hasCharts) {
        const chartConfigs = data.charts.map((chart: any) => chart.config);
        console.log(`Setting ${chartConfigs.length} chart configs`);
        setChartConfigs(chartConfigs);
      } else {
        setChartConfigs([]);
        console.log('No charts found in template');
      }
      
      setIsDirty(false); // 新模板加载后重置脏状态
    } catch (err) {
      setError(err instanceof Error ? err.message : "获取模板详情时发生错误");
      console.error("获取模板详情失败:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理文档上传
  const handleDocumentUpload = async (file: File) => {
    setIsLoading(true);
    setError(null);
    console.log(`Uploading document: ${file.name}`);

    try {
      const result = await wordApi.uploadAndProcessDocument(file);
      
      if (result.success) {
        // 创建新的模板数据
        const fullTemplateData: ReportTemplate = {
          id: Date.now(), // 临时ID
          title: file.name,
          content: result.markdown_content,
          dataBindings: result.tables || [],
          chartBindings: result.charts.map(chart => ({
            id: chart.id,
            title: chart.title
          })),
          charts: result.charts,
          structure: {},
        };

        setTemplate(fullTemplateData);
        setMarkdownContent(fullTemplateData.content);
        
        // 设置图表配置
        const chartConfigs = result.charts.map(chart => chart.config);
        setChartConfigs(chartConfigs);
        
        setIsDirty(false);
        console.log(`文档处理成功，生成了${result.charts.length}个图表`);
      } else {
        throw new Error(`文档处理失败: ${result.errors?.join(', ')}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "文档处理时发生错误");
      console.error("文档处理失败:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // 保存报告
  const handleSave = async () => {
    if (!template) return;

    setIsLoading(true);
    try {
      const updatedTemplate = {
        ...template,
        content: markdownContent,
        lastModified: new Date().toISOString(),
      };

      const response = await fetch("/api/reports/save", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedTemplate),
      });

      if (response.ok) {
        setIsDirty(false);
        alert("保存成功");
      } else {
        throw new Error("保存失败");
      }
    } catch (error) {
      console.error("保存错误:", error);
      alert("保存失败，请重试");
    } finally {
      setIsLoading(false);
    }
  };

  // 导出报告
  const handleExport = async (format: "word" | "pdf") => {
    if (!template) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/reports/export/${format}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: markdownContent,
          template: template,
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `report.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        throw new Error("导出失败");
      }
    } catch (error) {
      console.error("导出错误:", error);
      alert("导出失败，请重试");
    } finally {
      setIsLoading(false);
    }
  };

  // 处理内容变化
  const handleContentChange = (content: string) => {
    setMarkdownContent(content);
    setIsDirty(true);
  };

  // 处理文本选择
  const handleTextSelect = (text: string) => {
    setSelectedText(text);
  };

  // 处理AI对话结果
  const handleAIResponse = (suggestion: string) => {
    // 将AI建议应用到内容中
    const updatedContent = markdownContent + "\n\n" + suggestion;
    handleContentChange(updatedContent);
  };

  // 处理组件拖放 (保持兼容性)
  const handleComponentDrop = (item: { type: string; id: string }) => {
    let componentMarkdown = "";

    switch (item.type) {
      case "chart":
        componentMarkdown = `\n{{CHART_${item.id}}}\n`;
        break;
      case "table":
        componentMarkdown = `\n{{TABLE_${item.id}}}\n`;
        break;
      case "data":
        componentMarkdown = `{{DATA_${item.id}}}`;
        break;
      default:
        // Potentially handle other component types or do nothing
        break;
    }

    // For simplicity, appending to the end.
    // A more sophisticated approach would involve cursor position.
    const newContent = markdownContent + componentMarkdown;
    handleContentChange(newContent);
  };

  // 处理拖拽结束事件
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    const item = active.data.current as { type: string; id: string };
    handleComponentDrop(item);
  };

  // 处理图表点击预览
  const handleChartClick = (index: number) => {
    setSelectedChartIndex(index);
  };

  // 关闭图表预览
  const closeChartPreview = () => {
    setSelectedChartIndex(null);
  };

  // 监控图表配置变化
  useEffect(() => {
    console.log(`Chart configs updated: ${chartConfigs.length} charts available`);
    if (chartConfigs.length > 0) {
      console.log('Chart types:', chartConfigs.map(c => c.type).join(', '));
    }
  }, [chartConfigs]);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <div className="h-screen flex flex-col bg-gray-50">
        {/* 顶部工具栏 */}
        <div className="bg-white border-b px-4 py-2 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold">智能报告编辑器</h1>
            <Select
              value={template?.id?.toString() || ""}
              onValueChange={handleTemplateSelect}
            >
              <SelectTrigger
                className="w-[280px]"
                disabled={isLoading || isFetchingList}
              >
                <SelectValue
                  placeholder={
                    isFetchingList ? "正在加载模板..." : "选择一个报告模板..."
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {availableTemplates.map((tmpl) => (
                  <SelectItem key={tmpl.id} value={tmpl.id.toString()}>
                    {tmpl.filename}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {isLoading && <Loader2 className="h-5 w-5 animate-spin" />}
            {error && <span className="text-sm text-red-500">{error}</span>}
            {template && (
              <span className="text-sm text-gray-500">
                {isDirty && <span className="text-red-500 ml-1">*</span>}
              </span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {/* 上传模板按钮 */}
            <Link href="/word-format">
              <Button variant="outline">上传新模板</Button>
            </Link>

            {/* 保存按钮 */}
            <Button
              onClick={handleSave}
              disabled={!template || !isDirty || isLoading}
              className="flex items-center"
            >
              <Save className="w-4 h-4 mr-2" />
              保存
            </Button>

            {/* 导出按钮 */}
            <div className="flex space-x-1">
              <Button
                onClick={() => handleExport("word")}
                disabled={!template || isLoading}
                variant="outline"
                size="sm"
              >
                导出Word
              </Button>
              <Button
                onClick={() => handleExport("pdf")}
                disabled={!template || isLoading}
                variant="outline"
                size="sm"
              >
                导出PDF
              </Button>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 flex overflow-hidden">
          <Split mode="horizontal" className="flex-1">
            {/* 左侧栏 */}
            <div
              style={{ width: "20%", minWidth: "250px" }}
              className="bg-white border-r flex flex-col h-full"
            >
              <Tabs
                value={leftTab}
                onValueChange={setLeftTab}
                className="flex-1 flex flex-col"
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="tables">表格</TabsTrigger>
                  <TabsTrigger value="charts">图表</TabsTrigger>
                </TabsList>
                <TabsContent
                  value="tables"
                  className="flex-1 overflow-y-auto p-2"
                >
                  <TemplateAssetList
                    assets={template?.dataBindings || []}
                    assetType="table"
                  />
                </TabsContent>
                <TabsContent
                  value="charts"
                  className="flex-1 overflow-hidden flex flex-col"
                >
                  <div className="overflow-y-auto flex-1 p-2" style={{ maxHeight: 'calc(100vh - 150px)' }}>
                    <div className="text-sm font-medium text-gray-700 mb-2 sticky top-0 bg-white py-2 z-10">
                      图表 ({chartConfigs.length})
                      {process.env.NODE_ENV === 'development' && (
                        <span className="ml-2 text-xs text-blue-500">
                          {chartConfigs.map((c, i) => `#${i+1}`).join(', ')}
                        </span>
                      )}
                    </div>
                    {chartConfigs.length > 0 ? (
                      <div className="space-y-8 pb-6">
                        {Array.from({ length: chartConfigs.length }).map((_, index) => (
                          <div 
                            key={`chart-${index}`} 
                            onClick={() => handleChartClick(index)}
                            className="cursor-pointer hover:opacity-90 transition-opacity border border-gray-100 rounded-lg shadow-sm"
                          >
                            <div className="p-2 bg-gray-50 rounded-t-lg text-sm font-medium">
                              图表 #{index+1}: {chartConfigs[index]?.title || `Chart ${index+1}`}
                            </div>
                            {chartConfigs[index] && (
                              <SmartChartRenderer
                                chartConfig={chartConfigs[index]}
                                width={300}
                                height={250}
                                className="mb-2"
                              />
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 p-4">
                        <div className="text-2xl mb-2">📊</div>
                        <p className="text-sm">暂无图表</p>
                        <p className="text-xs text-gray-400 mt-1">
                          上传包含图表的Word文档来查看图表
                        </p>
                      </div>
                    )}
                    <TemplateAssetList
                      assets={template?.chartBindings || []}
                      assetType="chart"
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* 中间编辑区域 */}
            <div
              style={{ width: "55%", minWidth: "400px" }}
              className="bg-white flex flex-col"
            >
              <div className="border-b px-4 py-2">
                <h2 className="text-lg font-medium">报告编辑器</h2>
              </div>
              <div className="flex-1 overflow-y-auto">
                {template ? (
                  <MarkdownEditor
                    content={markdownContent}
                    onChange={handleContentChange}
                    onComponentDrop={handleComponentDrop}
                    onTextSelect={handleTextSelect}
                    template={template}
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <p>请选择一个报告模板开始编辑</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 右侧面板 */}
            <div
              style={{ width: "25%", minWidth: "300px" }}
              className="bg-white border-l flex flex-col"
            >
              <div className="flex-1 flex flex-col">
                <div className="p-4 border-b">
                  <h3 className="font-semibold">AI助手</h3>
                </div>
                <div className="flex-1 overflow-y-auto">
                  <ChatPanel
                    template={template}
                    onAIResponse={handleAIResponse}
                    selectedText={selectedText}
                  />
                </div>
              </div>
            </div>
          </Split>
        </div>
        
        {/* 图表预览模态框 */}
        {selectedChartIndex !== null && chartConfigs[selectedChartIndex] && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-4 max-w-4xl w-full max-h-[90vh] overflow-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">{chartConfigs[selectedChartIndex].title || '图表预览'}</h3>
                <Button variant="ghost" size="sm" onClick={closeChartPreview}>
                  <X className="h-5 w-5" />
                </Button>
              </div>
              <div className="flex justify-center">
                <SmartChartRenderer
                  chartConfig={chartConfigs[selectedChartIndex]}
                  width={800}
                  height={500}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </DndContext>
  );
}
