import os
import io
from typing import List, Dict, Any
from docx import Document
from docx.document import Document as DocumentObject
from docx.text.paragraph import Paragraph
from docx.table import Table
import zipfile
import xml.etree.ElementTree as ET
import json
import re
import base64

# Pillow 是 python-docx 用于图片处理的推荐依赖项
try:
    from PIL import Image
except ImportError:
    Image = None

class ExcelChartExtractor:
    """Excel图表提取器 - 提取并格式化Excel图表XML数据"""
    
    def __init__(self):
        self.chart_counter = 1
    
    def extract_chart_info(self, chart_xml: str, context: str = "") -> Dict[str, Any]:
        """
        提取Excel图表信息，为AI处理准备数据
        
        Args:
            chart_xml: Excel图表的XML数据
            context: 图表上下文
            
        Returns:
            包含图表信息的字典
        """
        try:
            # 基础图表信息
            chart_info = {
                "type": "excel_chart",
                "title": self._extract_title_from_context(context),
                "context": context,
                "xml_data": chart_xml,
                "xml_summary": self._create_xml_summary(chart_xml),
                "chart_id": f"chart_{self.chart_counter}"
            }
            
            self.chart_counter += 1
            return chart_info
            
        except Exception as e:
            print(f"[警告] 提取图表信息时出错: {e}")
            return {
                "type": "excel_chart",
                "title": "图表",
                "context": context,
                "xml_data": chart_xml,
                "error": str(e),
                "chart_id": f"chart_{self.chart_counter}"
            }
    
    def _extract_title_from_context(self, context: str) -> str:
        """从上下文中提取可能的图表标题"""
        if not context:
            return "图表"
        
        # 查找"图"字相关的标题
        lines = context.split('\n')
        for line in lines[-3:]:  # 查看最近的3行
            line = line.strip()
            if '图' in line and len(line) < 50:
                return line
        
        # 如果没找到，使用上下文的前50个字符
        return context[:50] + "..." if len(context) > 50 else context
    
    def _create_xml_summary(self, chart_xml: str) -> Dict[str, Any]:
        """创建XML的概要信息，便于AI理解"""
        try:
            # 统计XML中的关键信息
            summary = {
                "xml_length": len(chart_xml),
                "contains_data": "data" in chart_xml.lower(),
                "contains_series": "series" in chart_xml.lower(),
                "contains_categories": "cat" in chart_xml.lower(),
                "chart_type_hints": []
            }
            
            # 检测可能的图表类型
            chart_types = {
                "barChart": "柱状图",
                "lineChart": "折线图", 
                "pieChart": "饼图",
                "areaChart": "面积图",
                "scatterChart": "散点图"
            }
            
            for xml_type, chinese_name in chart_types.items():
                if xml_type.lower() in chart_xml.lower():
                    summary["chart_type_hints"].append(chinese_name)
            
            return summary
            
        except Exception as e:
            return {"error": str(e)}

def extract_images_from_docx(docx_path: str, output_dir: str) -> List[Dict[str, Any]]:
    """
    从 .docx 文件中提取所有图片和图表，并返回图片信息列表。

    Args:
        docx_path (str): Word 文件的路径。
        output_dir (str): 图片保存的目录路径。

    Returns:
        List[Dict[str, Any]]: 一个包含图片信息的字典列表。
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    doc = Document(docx_path)
    images_info: List[Dict[str, Any]] = []
    image_counter = 1
    last_text_paragraph = ""
    processed_rids = set()

    print(f"[信息] 开始提取图片和图表，文档：{docx_path}")
    
    # 初始化图表提取器
    chart_extractor = ExcelChartExtractor()
    
    # 检查文档的related_parts中是否有图片或图表
    image_parts = {}
    chart_parts = {}
    
    for rel_id, part in doc.part.related_parts.items():
        # 普通图片
        if hasattr(part, 'partname') and any(ext in part.partname.lower() for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff']):
            image_parts[rel_id] = part
        
        # Excel图表
        elif hasattr(part, 'partname') and 'chart' in part.partname.lower():
            chart_parts[rel_id] = part
    
    print(f"[信息] 找到 {len(image_parts)} 个图片，{len(chart_parts)} 个图表")

    def find_and_save_images(p: Paragraph, doc_obj: DocumentObject):
        nonlocal image_counter
        
        # 检查段落是否包含图片或图表
        paragraph_xml = p._p.xml
        
        # 检查多种可能的图片/图表标记
        indicators = ['drawing', 'pic:', 'blip', 'embed', 'r:embed', 'chart']
        has_indicator = any(indicator in paragraph_xml for indicator in indicators)
        
        if not has_indicator:
            return

        for run in p.runs:
            run_xml = run._r.xml
            
            # 多种模式匹配图片/图表引用
            patterns = [
                r'r:embed="([^"]+)"',
                r'embed="([^"]+)"',
                r'r:id="([^"]+)"'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, run_xml)
                
                for embed_id in matches:
                    if embed_id in processed_rids:
                        continue
                        
                    try:
                        processed_rids.add(embed_id)
                        
                        if embed_id not in doc_obj.part.related_parts:
                            continue
                            
                        related_part = doc_obj.part.related_parts[embed_id]
                        
                        # 处理普通图片
                        if any(ext in related_part.partname.lower() for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff']):
                            image_data = related_part.blob
                            
                            image_ext = os.path.splitext(related_part.partname)[1].lower()
                            if not image_ext:
                                image_ext = ".png"

                            image_filename = f"image_{image_counter}{image_ext}"
                            image_path = os.path.join(output_dir, image_filename)
                            
                            with open(image_path, "wb") as img_file:
                                img_file.write(image_data)
                            
                            images_info.append({
                                "image_path": image_path,
                                "preceding_text": last_text_paragraph,
                                "type": "image"
                            })
                            print(f"[保存] 图片已保存: {image_filename}")
                            image_counter += 1
                        
                        # 处理Excel图表 - 只提取XML数据，不渲染图片
                        elif 'chart' in related_part.partname.lower():
                            try:
                                chart_xml = related_part.blob.decode('utf-8')
                                
                                # 使用图表提取器处理
                                chart_info = chart_extractor.extract_chart_info(chart_xml, last_text_paragraph)
                                
                                # 保存XML数据到文件（可选，用于调试）
                                xml_filename = f"chart_{image_counter}.xml"
                                xml_path = os.path.join(output_dir, xml_filename)
                                with open(xml_path, "w", encoding='utf-8') as xml_file:
                                    xml_file.write(chart_xml)
                                
                                # 创建图表信息记录
                                chart_record = {
                                    "chart_id": embed_id,
                                    "chart_part": related_part.partname,
                                    "preceding_text": last_text_paragraph,
                                    "type": "chart",
                                    "chart_info": chart_info,
                                    "xml_file": xml_path  # XML文件路径，供后续处理使用
                                }
                                
                                images_info.append(chart_record)
                                print(f"[提取] Excel图表XML已提取: chart_{image_counter}.xml")
                                image_counter += 1
                                
                            except Exception as chart_error:
                                print(f"[错误] 处理Excel图表失败: {chart_error}")
                                continue
                            
                    except (KeyError, AttributeError, Exception) as e:
                        print(f"[警告] 处理图片/图表时出错: {e}")
                        continue

    # 遍历文档中的所有段落和表格
    for element in doc.element.body:
        if element.tag.endswith('p'):
            p = Paragraph(element, doc)
            find_and_save_images(p, doc)
            
            # 如果这个段落没有图片/图表，更新上下文文本
            if not any(indicator in p._p.xml for indicator in ['drawing', 'pic:', 'blip', 'chart']):
                current_text = p.text.strip()
                if current_text:
                    last_text_paragraph = current_text
                    
        elif element.tag.endswith('tbl'):
            table = Table(element, doc)
            for row in table.rows:
                for cell in row.cells:
                    for p_in_cell in cell.paragraphs:
                        find_and_save_images(p_in_cell, doc)
                        
                        # 如果这个段落没有图片/图表，更新上下文文本
                        if not any(indicator in p_in_cell._p.xml for indicator in ['drawing', 'pic:', 'blip', 'chart']):
                            current_text = p_in_cell.text.strip()
                            if current_text:
                                last_text_paragraph = current_text
    
    print(f"[完成] 提取完成，共找到 {len(images_info)} 个图片/图表")
    
    # 统计结果
    image_count = len([info for info in images_info if info["type"] == "image"])
    chart_count = len([info for info in images_info if info["type"] == "chart"])
    print(f"[统计] 图片: {image_count} 个, Excel图表: {chart_count} 个")
    
    return images_info