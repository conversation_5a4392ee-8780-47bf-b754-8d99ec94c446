'use client'

import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Send, Bot, User, Lightbulb, Database, BarChart3, FileText, Loader2 } from "lucide-react"
import { aiChatApi } from '@/api/ai-chat'

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
  actions?: any[]
}

interface ChatPanelProps {
  template: any
  onAIResponse: (suggestion: string) => void
  selectedText?: string
}

export default function ChatPanel({ template, onAIResponse, selectedText }: ChatPanelProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // 预设的快速操作
  const quickActions = [
    {
      icon: <FileText className="w-4 h-4" />,
      label: "优化内容",
      prompt: "请帮我优化当前选中的内容，使其更加专业和易读"
    },
    {
      icon: <Database className="w-4 h-4" />,
      label: "查询数据",
      prompt: "我需要查询相关的数据来支撑我的分析"
    },
    {
      icon: <BarChart3 className="w-4 h-4" />,
      label: "创建图表",
      prompt: "帮我创建一个图表来可视化这些数据"
    },
    {
      icon: <Lightbulb className="w-4 h-4" />,
      label: "内容建议",
      prompt: "请为我的报告提供一些内容建议和改进意见"
    }
  ]

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 初始化欢迎消息
  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        type: 'ai',
        content: `👋 您好！我是您的智能报告助手。\n\n我可以帮助您：\n• 📝 优化报告内容\n• 📊 查询和分析数据\n• 📈 创建可视化图表\n• 🔧 调整报告结构\n• 💡 提供专业建议\n\n请告诉我您需要什么帮助？`,
        timestamp: new Date()
      }
      setMessages([welcomeMessage])
    }
  }, [messages.length])

  // 监听文本选择事件
  useEffect(() => {
    const handleTextSelection = () => {
      const selection = window.getSelection()
      if (selection && !selection.isCollapsed) {
        const text = selection.toString().trim()
        if (text) {
          // 不再在这里设置selectedText，因为现在是从props接收
        }
      }
    }

    document.addEventListener('mouseup', handleTextSelection)
    document.addEventListener('keyup', handleTextSelection)

    return () => {
      document.removeEventListener('mouseup', handleTextSelection)
      document.removeEventListener('keyup', handleTextSelection)
    }
  }, [])

  // 发送消息
  const sendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      // 使用新的API客户端
      const aiResponse = await aiChatApi.sendMessage(content, {
        template_id: template?.id,
        template_type: template?.metadata?.template_type,
        current_section: "未知",
        selected_content: selectedText || ""
      })
      
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.message,
        timestamp: new Date(),
        actions: aiResponse.action ? [aiResponse.action] : []
      }

      setMessages(prev => [...prev, aiMessage])

      // 如果有建议的操作，调用回调
      if (aiResponse.suggestion) {
        onAIResponse(aiResponse.suggestion)
      }
    } catch (error) {
      console.error('AI对话错误:', error)
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: '抱歉，我暂时无法处理您的请求。请稍后重试或检查网络连接。',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  // 处理快速操作
  const handleQuickAction = (prompt: string) => {
    // 如果有选中文本，则在快速操作提示后添加选中的文本
    if (selectedText && prompt.includes("选中的内容")) {
      sendMessage(`${prompt}\n\n选中的内容: "${selectedText}"`)
    } else {
      sendMessage(prompt)
    }
  }

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage(inputValue)
    }
  }

  // 执行AI建议的操作
  const executeAction = (action: any) => {
    switch (action.type) {
      case 'insert_content':
        onAIResponse(action.content)
        break
      case 'insert_data':
        // 处理数据插入
        break
      case 'insert_chart':
        // 处理图表插入
        break
      default:
        console.log('未知操作类型:', action.type)
    }
  }

  // 渲染消息
  const renderMessage = (message: Message) => (
    <div key={message.id} className={`mb-4 ${message.type === 'user' ? 'ml-8' : 'mr-8'}`}>
      <div className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
        <div className={`flex items-start space-x-2 max-w-[85%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
          {/* 头像 */}
          <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
            message.type === 'user' 
              ? 'bg-blue-500 text-white' 
              : 'bg-green-500 text-white'
          }`}>
            {message.type === 'user' ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
          </div>
          
          {/* 消息内容 */}
          <div className={`rounded-lg p-3 ${
            message.type === 'user'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-800'
          }`}>
            <div className="whitespace-pre-wrap text-sm">{message.content}</div>
            
            {/* 操作按钮 */}
            {message.actions && message.actions.length > 0 && (
              <div className="mt-2 space-y-1">
                {message.actions.map((action, index) => (
                  <Button
                    key={index}
                    size="sm"
                    variant="outline"
                    className="text-xs"
                    onClick={() => executeAction(action)}
                  >
                    {action.type === 'insert_content' && '📝 插入内容'}
                    {action.type === 'insert_data' && '📊 插入数据'}
                    {action.type === 'insert_chart' && '📈 插入图表'}
                  </Button>
                ))}
              </div>
            )}
            
            <div className="text-xs opacity-70 mt-1">
              {message.timestamp.toLocaleTimeString()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 快速操作栏 */}
      <div className="p-3 border-b bg-gray-50">
        <div className="grid grid-cols-2 gap-2">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              className="flex items-center justify-start text-xs h-8"
              onClick={() => handleQuickAction(action.prompt)}
              disabled={isLoading}
            >
              {action.icon}
              <span className="ml-1 truncate">{action.label}</span>
            </Button>
          ))}
        </div>
      </div>

      {/* 选中文本提示 */}
      {selectedText && (
        <div className="px-3 py-2 bg-blue-50 border-b border-blue-100 text-xs text-blue-700 flex justify-between items-center">
          <div className="truncate flex-1">
            <span className="font-medium">已选中文本: </span>
            <span className="italic">{selectedText.length > 50 ? `${selectedText.substring(0, 50)}...` : selectedText}</span>
          </div>
        </div>
      )}

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-1">
        {messages.map(renderMessage)}
        
        {/* 加载指示器 */}
        {isLoading && (
          <div className="flex justify-start mr-8">
            <div className="flex items-start space-x-2">
              <div className="w-8 h-8 rounded-full bg-green-500 text-white flex items-center justify-center">
                <Bot className="w-4 h-4" />
              </div>
              <div className="bg-gray-100 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-gray-600">正在思考...</span>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="p-3 border-t bg-white">
        <div className="flex space-x-2">
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={selectedText ? "输入您的问题或需求，已选中文本将作为上下文..." : "输入您的问题或需求..."}
            disabled={isLoading}
            className="flex-1"
          />
          <Button
            onClick={() => sendMessage(inputValue)}
            disabled={!inputValue.trim() || isLoading}
            size="sm"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
        
        <div className="text-xs text-gray-500 mt-2">
          按 Enter 发送消息，Shift + Enter 换行
        </div>
      </div>
    </div>
  )
} 