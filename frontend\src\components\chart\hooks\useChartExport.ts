import { useCallback } from 'react';
import { ExportOptions } from '../types/chartTypes';

export function useChartExport() {
  // 导出SVG
  const exportSVG = useCallback((
    svgElement: SVGSVGElement,
    options: ExportOptions = {}
  ) => {
    const {
      filename = 'chart',
      backgroundColor = 'white'
    } = options;

    // 克隆SVG元素以避免修改原始元素
    const clonedSvg = svgElement.cloneNode(true) as SVGSVGElement;
    
    // 设置背景色
    if (backgroundColor) {
      const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      rect.setAttribute('width', '100%');
      rect.setAttribute('height', '100%');
      rect.setAttribute('fill', backgroundColor);
      clonedSvg.insertBefore(rect, clonedSvg.firstChild);
    }

    // 序列化SVG
    const serializer = new XMLSerializer();
    const svgString = serializer.serializeToString(clonedSvg);
    
    // 添加XML声明和DOCTYPE
    const fullSvgString = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
${svgString}`;

    // 创建下载链接
    const blob = new Blob([fullSvgString], { type: 'image/svg+xml;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.svg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, []);

  // 导出PNG
  const exportPNG = useCallback((
    svgElement: SVGSVGElement,
    options: ExportOptions = {}
  ) => {
    const {
      filename = 'chart',
      quality = 1,
      backgroundColor = 'white'
    } = options;

    // 获取SVG尺寸
    const rect = svgElement.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;

    // 创建canvas
    const canvas = document.createElement('canvas');
    canvas.width = width * 2; // 2x分辨率
    canvas.height = height * 2;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      console.error('无法获取canvas上下文');
      return;
    }

    // 设置高DPI
    ctx.scale(2, 2);

    // 设置背景色
    if (backgroundColor) {
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(0, 0, width, height);
    }

    // 克隆SVG并设置样式
    const clonedSvg = svgElement.cloneNode(true) as SVGSVGElement;
    clonedSvg.setAttribute('width', width.toString());
    clonedSvg.setAttribute('height', height.toString());

    // 序列化SVG
    const serializer = new XMLSerializer();
    const svgString = serializer.serializeToString(clonedSvg);
    const svgBlob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
    const svgUrl = URL.createObjectURL(svgBlob);

    // 创建图片
    const img = new Image();
    img.onload = () => {
      ctx.drawImage(img, 0, 0, width, height);
      
      // 导出PNG
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${filename}.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        }
      }, 'image/png', quality);
      
      URL.revokeObjectURL(svgUrl);
    };
    
    img.onerror = () => {
      console.error('图片加载失败');
      URL.revokeObjectURL(svgUrl);
    };
    
    img.src = svgUrl;
  }, []);

  // 导出PDF (需要额外的库，这里提供基础实现)
  const exportPDF = useCallback((
    svgElement: SVGSVGElement,
    options: ExportOptions = {}
  ) => {
    console.warn('PDF导出功能需要额外的库支持，如jsPDF');
    // 这里可以集成jsPDF等库来实现PDF导出
    exportPNG(svgElement, { ...options, filename: options.filename || 'chart' });
  }, [exportPNG]);

  // 复制到剪贴板
  const copyToClipboard = useCallback(async (svgElement: SVGSVGElement) => {
    try {
      // 序列化SVG
      const serializer = new XMLSerializer();
      const svgString = serializer.serializeToString(svgElement);
      
      // 复制到剪贴板
      await navigator.clipboard.writeText(svgString);
      console.log('SVG已复制到剪贴板');
    } catch (error) {
      console.error('复制到剪贴板失败:', error);
    }
  }, []);

  // 获取SVG字符串
  const getSVGString = useCallback((svgElement: SVGSVGElement): string => {
    const serializer = new XMLSerializer();
    return serializer.serializeToString(svgElement);
  }, []);

  return {
    exportSVG,
    exportPNG,
    exportPDF,
    copyToClipboard,
    getSVGString
  };
}

export default useChartExport; 