import NextAuth from "next-auth"
import type { NextAuthConfig } from "next-auth"
import Credentials from "next-auth/providers/credentials"

export const authConfig: NextAuthConfig = {
  pages: {
    signIn: '/login',
  },
  providers: [
    Credentials({
      async authorize(credentials) {
        try {
          const body = new URLSearchParams();
          body.append('username', credentials.email as string);
          body.append('password', credentials.password as string);

          const res = await fetch(`${process.env.BACKEND_API_URL}/api/v1/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: body,
          });

          if (!res.ok) {
            // 如果后端返回错误（例如 401 Unauthorized），则返回 null
            return null;
          }

          const user = await res.json();

          // 2. 如果后端返回了用户数据，则将其返回
          //    NextAuth.js 将用它来创建会话和 JWT
          if (user) {
            return user; // 必须返回一个包含 id, name, email 等信息的对象
          }
        } catch (error) {
          console.error("Authorize error:", error);
          return null;
        }
        
        // 3. 如果验证失败，返回 null
        return null;
      },
    }),
  ],
  callbacks: {
    // authorized 回调用于验证对受保护路由的访问
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnLoginPage = nextUrl.pathname.startsWith('/login');

      if (isLoggedIn && isOnLoginPage) {
        return Response.redirect(new URL('/', nextUrl));
      }

      if (!isLoggedIn && !isOnLoginPage) {
        return false;
      }

      return true;
    },
    // jwt 回调在创建或更新 JWT 时被调用
    async jwt({ token, user }) {
      // 首次登录时，user 对象是可用的。将其中的信息添加到 token 中。
      if (user) {
        token.id = user.id;
        // 您可以添加任何您想在 token 中存储的用户信息
        // token.role = user.role; 
      }
      return token;
    },
    // session 回调在检查会话时被调用
    async session({ session, token }) {
      // 将 token 中的自定义数据添加到 session 对象中，使其在客户端可用
      if (token && session.user) {
        session.user.id = token.id as string;
        // session.user.role = token.role;
      }
      return session;
    },
  },
};

export const { auth, signIn, signOut, handlers } = NextAuth(authConfig);
