# 贡献指南

我们非常欢迎并感谢您对 zh-kit 项目的贡献！您的帮助对我们至关重要。

在开始之前，请确保您已阅读并同意遵守我们的 [行为准则](CODE_OF_CONDUCT.md)。

## 如何贡献

您可以通过多种方式为项目做出贡献：

*   **报告 Bug**: 如果您在使用过程中发现了任何问题或错误，请通过 [GitHub Issues](https://github.com/ctypst/zh-kit/issues) 提交 Bug 报告。请尽可能详细地描述问题，包括复现步骤、期望行为和实际行为。
*   **提出功能建议**: 如果您有关于新功能或改进现有功能的想法，也请通过 [GitHub Issues](https://github.com/ctypst/zh-kit/issues) 告诉我们。
*   **提交代码 (Pull Requests)**:
    *   如果您希望直接贡献代码，请先 Fork 本仓库。
    *   在您的 Fork 中创建一个新的分支进行修改。
    *   确保您的代码遵循项目的编码规范 (如果项目有定义的话)。
    *   添加必要的测试用例。
    *   提交 Pull Request 到主仓库的 `main` 或 `master` 分支。请在 PR 描述中清晰地说明您的更改内容和原因。
*   **改进文档**: 如果您发现文档中有任何不清晰、错误或可以改进的地方，欢迎提交 Pull Request 来更新文档。

感谢您的贡献！