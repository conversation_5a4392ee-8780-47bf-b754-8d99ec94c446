"use client";

import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';
import { SimpleBarChartProps } from './types/chartTypes';

export default function SimpleBarChart({
  data,
  width = 800,
  height = 500,
  className = "",
  title = "",
  barColor = "#4A90E2",
  showValues = true,
  orientation = 'vertical',
  xAxisLabel = "",
  yAxisLabel = "",
  animate = true,
  animationDuration = 750,
  colors = [],
  showLegend = false,
  interactive = true
}: SimpleBarChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data || data.length === 0) return;

    // 清空之前的内容
    d3.select(svgRef.current).selectAll("*").remove();

    const svg = d3.select(svgRef.current);
    const margin = { top: 60, right: 80, bottom: 60, left: 80 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // 创建主容器
    const container = svg.append('g')
      .attr('transform', `translate(${margin.left}, ${margin.top})`);

    // 准备数据
    const maxValue = d3.max(data, d => d.value) || 0;
    const minValue = d3.min(data, d => d.value) || 0;

    // 创建比例尺
    let xScale: d3.ScaleBand<string> | d3.ScaleLinear<number, number>;
    let yScale: d3.ScaleBand<string> | d3.ScaleLinear<number, number>;

    if (orientation === 'vertical') {
      xScale = d3.scaleBand()
        .domain(data.map(d => d.name))
        .range([0, innerWidth])
        .padding(0.1);

      yScale = d3.scaleLinear()
        .domain([Math.min(0, minValue), maxValue])
        .range([innerHeight, 0])
        .nice();
    } else {
      xScale = d3.scaleLinear()
        .domain([Math.min(0, minValue), maxValue])
        .range([0, innerWidth])
        .nice();

      yScale = d3.scaleBand()
        .domain(data.map(d => d.name))
        .range([0, innerHeight])
        .padding(0.1);
    }

    // 创建坐标轴
    if (orientation === 'vertical') {
      // X轴
      container.append('g')
        .attr('class', 'x-axis')
        .attr('transform', `translate(0, ${innerHeight})`)
        .call(d3.axisBottom(xScale as d3.ScaleBand<string>))
        .selectAll('text')
        .style('font-size', '12px')
        .style('fill', '#374151');

      // Y轴
      container.append('g')
        .attr('class', 'y-axis')
        .call(d3.axisLeft(yScale as d3.ScaleLinear<number, number>))
        .selectAll('text')
        .style('font-size', '12px')
        .style('fill', '#374151');
    } else {
      // X轴
      container.append('g')
        .attr('class', 'x-axis')
        .attr('transform', `translate(0, ${innerHeight})`)
        .call(d3.axisBottom(xScale as d3.ScaleLinear<number, number>))
        .selectAll('text')
        .style('font-size', '12px')
        .style('fill', '#374151');

      // Y轴
      container.append('g')
        .attr('class', 'y-axis')
        .call(d3.axisLeft(yScale as d3.ScaleBand<string>))
        .selectAll('text')
        .style('font-size', '12px')
        .style('fill', '#374151');
    }

    // 添加轴标签
    if (xAxisLabel) {
      container.append('text')
        .attr('class', 'x-axis-label')
        .attr('x', innerWidth / 2)
        .attr('y', innerHeight + 40)
        .attr('text-anchor', 'middle')
        .style('font-size', '14px')
        .style('fill', '#374151')
        .text(xAxisLabel);
    }

    if (yAxisLabel) {
      container.append('text')
        .attr('class', 'y-axis-label')
        .attr('transform', 'rotate(-90)')
        .attr('x', -innerHeight / 2)
        .attr('y', -40)
        .attr('text-anchor', 'middle')
        .style('font-size', '14px')
        .style('fill', '#374151')
        .text(yAxisLabel);
    }

    // 创建工具提示
    const tooltip = interactive ? d3.select('body').append('div')
      .attr('class', 'chart-tooltip')
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background', 'rgba(0, 0, 0, 0.8)')
      .style('color', 'white')
      .style('padding', '8px 12px')
      .style('border-radius', '4px')
      .style('font-size', '12px')
      .style('pointer-events', 'none')
      .style('z-index', '1000') : null;

    // 绘制柱状图
    const bars = container.selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .style('cursor', interactive ? 'pointer' : 'default');

    if (orientation === 'vertical') {
      bars
        .attr('x', d => (xScale as d3.ScaleBand<string>)(d.name) || 0)
        .attr('y', innerHeight)
        .attr('width', (xScale as d3.ScaleBand<string>).bandwidth())
        .attr('height', 0)
        .attr('fill', (d, i) => {
          if (colors && colors.length > 0) return colors[i % colors.length] || '#4A90E2';
          return barColor || '#4A90E2';
        });

      // 动画
      if (animate) {
        bars.transition()
          .duration(animationDuration)
          .ease(d3.easeQuadInOut)
          .attr('y', d => (yScale as d3.ScaleLinear<number, number>)(d.value))
          .attr('height', d => innerHeight - (yScale as d3.ScaleLinear<number, number>)(d.value));
      } else {
        bars
          .attr('y', d => (yScale as d3.ScaleLinear<number, number>)(d.value))
          .attr('height', d => innerHeight - (yScale as d3.ScaleLinear<number, number>)(d.value));
      }
    } else {
      bars
        .attr('x', 0)
        .attr('y', d => (yScale as d3.ScaleBand<string>)(d.name) || 0)
        .attr('width', 0)
        .attr('height', (yScale as d3.ScaleBand<string>).bandwidth())
        .attr('fill', (d, i) => {
          if (colors && colors.length > 0) return colors[i % colors.length] || '#4A90E2';
          return barColor || '#4A90E2';
        });

      // 动画
      if (animate) {
        bars.transition()
          .duration(animationDuration)
          .ease(d3.easeQuadInOut)
          .attr('width', d => (xScale as d3.ScaleLinear<number, number>)(d.value));
      } else {
        bars
          .attr('width', d => (xScale as d3.ScaleLinear<number, number>)(d.value));
      }
    }

    // 添加交互效果
    if (interactive && tooltip) {
      bars
        .on('mouseover', function(event, d) {
          d3.select(this).style('opacity', 0.8);
          tooltip
            .style('visibility', 'visible')
            .html(`<strong>${d.name}</strong><br/>值: ${d.value.toLocaleString()}`)
            .style('left', `${event.pageX + 10}px`)
            .style('top', `${event.pageY - 10}px`);
        })
        .on('mouseout', function() {
          d3.select(this).style('opacity', 1);
          tooltip.style('visibility', 'hidden');
        });
    }

    // 添加数值标签
    if (showValues) {
      const labels = container.selectAll('.value-label')
        .data(data)
        .enter()
        .append('text')
        .attr('class', 'value-label')
        .attr('text-anchor', 'middle')
        .style('font-size', '11px')
        .style('fill', '#374151')
        .style('font-weight', 'bold');

      if (orientation === 'vertical') {
        labels
          .attr('x', d => ((xScale as d3.ScaleBand<string>)(d.name) || 0) + (xScale as d3.ScaleBand<string>).bandwidth() / 2)
          .attr('y', d => (yScale as d3.ScaleLinear<number, number>)(d.value) - 5)
          .text(d => d.value.toLocaleString());
      } else {
        labels
          .attr('x', d => (xScale as d3.ScaleLinear<number, number>)(d.value) + 5)
          .attr('y', d => ((yScale as d3.ScaleBand<string>)(d.name) || 0) + (yScale as d3.ScaleBand<string>).bandwidth() / 2 + 4)
          .text(d => d.value.toLocaleString());
      }
    }

    // 添加标题
    if (title) {
      svg.append('text')
        .attr('x', width / 2)
        .attr('y', 30)
        .attr('text-anchor', 'middle')
        .style('font-size', '16px')
        .style('font-weight', 'bold')
        .style('fill', '#374151')
        .text(title);
    }

    // 清理函数
    return () => {
      if (tooltip) {
        tooltip.remove();
      }
    };

  }, [data, width, height, title, barColor, showValues, orientation, xAxisLabel, yAxisLabel, animate, animationDuration, colors, interactive]);

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="border border-gray-200 rounded max-w-full h-auto"
        viewBox={`0 0 ${width} ${height}`}
      />
    </div>
  );
}

// 导出示例数据和使用方法
export const exampleData = [
  { name: "产品A", value: 120 },
  { name: "产品B", value: 190 },
  { name: "产品C", value: 150 },
  { name: "产品D", value: 80 },
  { name: "产品E", value: 200 },
  { name: "产品F", value: 110 }
];

// 使用示例组件
export function SimpleBarChartExample() {
  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-bold mb-8 text-center">单一柱状图示例</h1>
      
      {/* 垂直柱状图 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">垂直柱状图</h2>
        <SimpleBarChart 
          data={exampleData}
          width={800}
          height={400}
          title="产品销售数据"
          barColor="#4A90E2"
          xAxisLabel="产品"
          yAxisLabel="销售量"
          className="max-w-4xl mx-auto"
        />
      </div>

      {/* 水平柱状图 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">水平柱状图</h2>
        <SimpleBarChart 
          data={exampleData}
          width={800}
          height={400}
          title="产品销售数据（水平）"
          orientation="horizontal"
          barColor="#7ED321"
          xAxisLabel="销售量"
          yAxisLabel="产品"
          className="max-w-4xl mx-auto"
        />
      </div>
    </div>
  );
} 