"""
Pydantic数据模型
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, EmailStr


class IntentType(str, Enum):
    """意图类型枚举"""
    ADMIN = "admin"
    SCENIC = "scenic"
    RAG = "rag"
    OTHER = "other"


class QueryRequest(BaseModel):
    """查询请求模型"""
    query: str = Field(..., description="用户查询问题", min_length=1, max_length=1000)
    user_id: Optional[str] = Field(None, description="用户ID")
    conversation_id: Optional[str] = Field(None, description="对话ID")
    stream: bool = Field(default=True, description="是否流式响应")


class QueryResponse(BaseModel):
    """查询响应模型"""
    answer: str = Field(..., description="回答内容")
    intent: IntentType = Field(..., description="识别的意图类型")
    sql: Optional[str] = Field(None, description="生成的SQL语句")
    data: Optional[List[Dict[str, Any]]] = Field(None, description="查询结果数据")
    confidence: Optional[float] = Field(None, description="置信度分数")
    processing_time: float = Field(..., description="处理时间(秒)")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")


class StreamChunk(BaseModel):
    """流式响应块"""
    content: str = Field(..., description="内容片段")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    is_final: bool = Field(default=False, description="是否为最后一块")


class SQLQualityMetrics(BaseModel):
    """SQL质量指标"""
    syntax_score: float = Field(..., description="语法正确性分数", ge=0, le=1)
    execution_score: float = Field(..., description="执行成功分数", ge=0, le=1)
    result_score: float = Field(..., description="结果合理性分数", ge=0, le=1)
    performance_score: float = Field(..., description="性能分数", ge=0, le=1)
    overall_score: float = Field(..., description="综合质量分数", ge=0, le=1)


class KnowledgeExample(BaseModel):
    """知识库示例"""
    question: str = Field(..., description="问题")
    sql: str = Field(..., description="SQL语句")
    quality_score: Optional[float] = Field(None, description="质量分数")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


class IntentClassificationResult(BaseModel):
    """意图分类结果"""
    intent: IntentType = Field(..., description="分类结果")
    confidence: float = Field(..., description="置信度", ge=0, le=1)
    reasoning: Optional[str] = Field(None, description="分类理由")


class DatabaseQueryResult(BaseModel):
    """数据库查询结果"""
    data: List[Dict[str, Any]] = Field(..., description="查询数据")
    row_count: int = Field(..., description="行数")
    execution_time: float = Field(..., description="执行时间(秒)")
    columns: List[str] = Field(..., description="列名列表")


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="版本号")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="检查时间")
    components: Dict[str, str] = Field(..., description="组件状态")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error_code: str = Field(..., description="错误代码")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="错误时间")


class ConfigUpdateRequest(BaseModel):
    """配置更新请求"""
    ragflow_settings: Optional[Dict[str, Any]] = Field(None, description="RAGFlow配置")
    quality_settings: Optional[Dict[str, Any]] = Field(None, description="质量配置")
    llm_settings: Optional[Dict[str, Any]] = Field(None, description="LLM配置")


class StatsResponse(BaseModel):
    """统计信息响应"""
    total_queries: int = Field(..., description="总查询数")
    successful_queries: int = Field(..., description="成功查询数")
    failed_queries: int = Field(..., description="失败查询数")
    average_response_time: float = Field(..., description="平均响应时间")
    intent_distribution: Dict[str, int] = Field(..., description="意图分布")
    quality_stats: Dict[str, float] = Field(..., description="质量统计")
    last_updated: datetime = Field(default_factory=datetime.now, description="最后更新时间")


# ==================== 用户认证相关模型 ====================

class User(BaseModel):
    """应用内部使用的用户模型"""
    id: str
    name: str
    email: str
    hashed_password: str

class UserLoginSchema(BaseModel):
    """用户登录请求体模型"""
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., description="密码")

class Token(BaseModel):
    """JWT令牌模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")

class TokenData(BaseModel):
    """JWT令牌数据负载模型"""
    email: Optional[str] = None


class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"


class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(..., description="用户名", min_length=3, max_length=50)
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, description="全名", max_length=100)
    role: UserRole = Field(default=UserRole.USER, description="用户角色")
    is_active: bool = Field(default=True, description="是否激活")


class UserCreate(UserBase):
    """用户创建模型"""
    password: str = Field(..., description="密码", min_length=6, max_length=128)


class UserUpdate(BaseModel):
    """用户更新模型"""
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    full_name: Optional[str] = Field(None, description="全名", max_length=100)
    role: Optional[UserRole] = Field(None, description="用户角色")
    is_active: Optional[bool] = Field(None, description="是否激活")
    password: Optional[str] = Field(None, description="新密码", min_length=6, max_length=128)


class UserResponse(UserBase):
    """用户响应模型"""
    id: int = Field(..., description="用户ID")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")
    last_login: Optional[str] = Field(None, description="最后登录时间")

    class Config:
        from_attributes = True

    @classmethod
    def from_db_user(cls, user):
        """从数据库用户对象创建响应模型"""
        return cls(
            id=user.id,
            username=user.username,
            email=user.email,
            full_name=user.full_name,
            role=user.role,
            is_active=user.is_active,
            created_at=user.created_at.isoformat() if user.created_at else "",
            updated_at=user.updated_at.isoformat() if user.updated_at else "",
            last_login=user.last_login.isoformat() if user.last_login else None
        )


class UserStats(BaseModel):
    """用户统计模型"""
    total_queries: int = Field(default=0, description="总查询数")
    successful_queries: int = Field(default=0, description="成功查询数")
    failed_queries: int = Field(default=0, description="失败查询数")
    avg_response_time: float = Field(default=0.0, description="平均响应时间")
    last_query_time: Optional[datetime] = Field(None, description="最后查询时间")


class DashboardStats(BaseModel):
    """数据大屏统计模型"""
    total_users: int = Field(..., description="总用户数")
    active_users: int = Field(..., description="活跃用户数")
    total_queries: int = Field(..., description="总查询数")
    successful_queries: int = Field(..., description="成功查询数")
    failed_queries: int = Field(..., description="失败查询数")
    avg_response_time: float = Field(..., description="平均响应时间")
    system_health: str = Field(..., description="系统健康状态")
    ragflow_status: str = Field(..., description="RAGFlow状态")
    database_status: str = Field(..., description="数据库状态")
    last_updated: datetime = Field(default_factory=datetime.now, description="最后更新时间")


# ==================== Word文档处理相关模型 ====================


class ProcessedDocumentBase(BaseModel):
    """已处理文档的基础模型"""
    filename: str = Field(..., description="原始文件名")
    markdown_content: str = Field(..., description="解析后的Markdown内容，包含占位符")
    tables: List[Dict[str, Any]] = Field(default_factory=list, description="提取的结构化表格数据")
    charts: List[Dict[str, Any]] = Field(default_factory=list, description="提取并处理后的图表配置")


class ProcessedDocumentCreate(ProcessedDocumentBase):
    """已处理文档的创建模型"""
    pass

class ProcessedDocumentMetadata(BaseModel):
    """已处理文档的元数据响应模型"""
    id: int
    filename: str
    created_at: datetime

    class Config:
        from_attributes = True

class ProcessedDocumentDetail(ProcessedDocumentMetadata):
    """已处理文档的详细响应模型"""
    markdown_content: str
    tables: List[Dict[str, Any]]
    charts: List[Dict[str, Any]]

    class Config:
        from_attributes = True
# ==================== Word解析与图表提取相关模型 ====================

class ChartDataSet(BaseModel):
    """图表数据集模型，兼容Chart.js"""
    label: str = Field(..., description="数据集标签")
    data: List[Union[float, int]] = Field(..., description="数据点列表")
    backgroundColor: Optional[Union[str, List[str]]] = Field(None, description="背景颜色")
    borderColor: Optional[Union[str, List[str]]] = Field(None, description="边框颜色")
    borderWidth: Optional[int] = Field(1, description="边框宽度")

class ExtractedChartData(BaseModel):
    """从Word中提取的结构化图表数据"""
    chart_type: str = Field(default="bar", description="图表类型 (e.g., 'bar', 'line', 'pie')")
    title: Optional[str] = Field(None, description="图表标题")
    labels: List[str] = Field(..., description="X轴或饼图的标签")
    datasets: List[ChartDataSet] = Field(..., description="Y轴的数据系列")