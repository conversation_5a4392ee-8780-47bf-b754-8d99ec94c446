{"name": "ai-report-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@babel/runtime": "^7.27.6", "@dnd-kit/core": "^6.1.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@types/hast": "^3.0.4", "@uiw/react-split": "^5.9.3", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "date-fns": "^4.1.0", "framer-motion": "^11.15.0", "lucide-react": "^0.468.0", "next": "14.2.30", "next-auth": "5.0.0-beta.25", "postcss": "^8.5.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "unist-util-visit": "^5.0.0", "zod": "^3.24.1"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/eslint-parser": "^7.28.0", "@types/d3": "^7.4.3", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^8.57.1", "eslint-config-next": "14.2.30", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "typescript": "^5.7.2"}, "engines": {"node": ">=18.0.0"}}