# -*- coding: utf-8 -*-
"""
将从Word文档解压出的图表XML，直接转换为特定D3.js组件所需的props
基于确定性的数据转换，无需AI介入
"""
from typing import Dict, Any, List, Optional
import logging
import xmltodict
import json

logger = logging.getLogger(__name__)

class D3JSGenerator:
    """
    通过解析Office Open XML (chart.xml) 来确定性地生成D3.js图表配置
    """
    
    def __init__(self):
        self.color_palettes = {
            "professional": ["#4A90E2", "#7ED321", "#F5A623", "#D0021B", "#9013FE", "#50E3C2"],
            "warm": ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD"],
            "cool": ["#74B9FF", "#0984E3", "#00B894", "#00CEC9", "#6C5CE7", "#A29BFE"]
        }

    def generate_d3_config(self, parsed_data: Dict[str, Any], chart_title: str = "", color_palette: str = "professional") -> Dict[str, Any]:
        """
        根据图表XML内容，生成D3组件的配置
        
        Args:
            parsed_data: 包含 'xml_content' 或直接传入XML字符串，或包含 'xml_data' 的字典
            chart_title: 图表标题 (如果提供，将覆盖从XML解析的标题)
            color_palette: 颜色方案
            
        Returns:
            D3兼容的图表配置JSON
        """
        # 处理不同的输入格式
        xml_content = None
        if isinstance(parsed_data, str):
            # 直接传入XML字符串
            xml_content = parsed_data
        elif isinstance(parsed_data, dict):
            # 尝试多种可能的键名
            xml_content = (
                parsed_data.get("xml_content") or 
                parsed_data.get("xml_data") or 
                parsed_data.get("xml")
            )
        
        if not xml_content:
            return self._generate_error_config("XML内容为空")

        try:
            chart_dict = xmltodict.parse(xml_content)
            plot_area = chart_dict.get('c:chartSpace', {}).get('c:chart', {}).get('c:plotArea', {})
            
            actual_chart_type = self._get_chart_type_from_dict(plot_area)
            logger.info(f"从XML中检测到的图表类型: {actual_chart_type}")

            title = chart_title or parsed_data.get("title", "未命名图表") if isinstance(parsed_data, dict) else chart_title or "未命名图表"

            if actual_chart_type == "combo":
                return self._generate_combo_config(plot_area, title, color_palette)
            elif actual_chart_type == "ofPie":
                return self._generate_nested_pie_config(plot_area, title, color_palette)
            elif actual_chart_type == "pie":
                return self._generate_simple_pie_config(plot_area, title, color_palette)
            elif actual_chart_type == "bar":
                return self._generate_bar_config(plot_area, title, color_palette)
            else:
                logger.warning(f"不支持的图表类型: {actual_chart_type}")
                return self._generate_error_config(f"不支持的图表类型: {actual_chart_type}")
                
        except Exception as e:
            logger.error(f"生成D3配置时发生错误: {e}", exc_info=True)
            return self._generate_error_config(f"配置生成失败: {str(e)}")

    def _get_chart_type_from_dict(self, plot_area: Dict[str, Any]) -> str:
        """从解析后的XML字典中判断图表类型"""
        if 'c:ofPieChart' in plot_area:
            return "ofPie"
        if 'c:pieChart' in plot_area:
            return "pie"
        
        has_bar = 'c:barChart' in plot_area
        has_line = 'c:lineChart' in plot_area

        if has_bar and has_line:
            return "combo"
        if has_bar:
            return "bar"
        if has_line:
            return "line" # 可以添加 _generate_line_config
            
        return "unknown"

    def _extract_series_data(self, series: Dict[str, Any]) -> Dict[str, Any]:
        """从单个系列(ser)的字典中提取数据"""
        # 提取系列名称
        name = "系列"
        if 'c:tx' in series:
            tx_data = series['c:tx']
            if 'c:strRef' in tx_data and 'c:f' in tx_data['c:strRef']:
                # 从Excel引用中提取更友好的名称
                ref_name = tx_data['c:strRef']['c:f']
                # 简化Excel引用名称
                if '!' in ref_name:
                    # 提取最后一部分作为名称
                    parts = ref_name.split('!')
                    if len(parts) > 1:
                        name = f"数据系列{parts[-1]}"
                    else:
                        name = ref_name
                else:
                    name = ref_name
            elif 'c:v' in tx_data:
                name = tx_data['c:v']
        
        # 提取分类数据
        categories = []
        if 'c:cat' in series:
            cat_data = series['c:cat']
            if 'c:strRef' in cat_data and 'c:strCache' in cat_data['c:strRef']:
                str_cache = cat_data['c:strRef']['c:strCache']
                if 'c:pt' in str_cache:
                    cat_points = str_cache['c:pt']
                    if not isinstance(cat_points, list):
                        cat_points = [cat_points]
                    categories = [pt.get('c:v', '') for pt in cat_points]

        # 提取数值数据
        values = []
        if 'c:val' in series:
            val_data = series['c:val']
            if 'c:numRef' in val_data and 'c:numCache' in val_data['c:numRef']:
                num_cache = val_data['c:numRef']['c:numCache']
                if 'c:pt' in num_cache:
                    val_points = num_cache['c:pt']
                    if not isinstance(val_points, list):
                        val_points = [val_points]
                    values = [float(pt.get('c:v', 0)) for pt in val_points]
            
        return {"name": name, "categories": categories, "values": values}

    def _generate_combo_config(self, plot_area: Dict[str, Any], title: str, palette: str) -> Dict[str, Any]:
        """生成组合图配置"""
        bar_chart = plot_area.get('c:barChart', {})
        line_chart = plot_area.get('c:lineChart', {})

        bar_series_list = bar_chart.get('c:ser', [])
        if not isinstance(bar_series_list, list):
            bar_series_list = [bar_series_list]
            
        line_series_list = line_chart.get('c:ser', [])
        if not isinstance(line_series_list, list):
            line_series_list = [line_series_list]

        if not bar_series_list or not line_series_list:
            return self._generate_error_config("组合图数据系列不完整")

        bar_series_data = self._extract_series_data(bar_series_list[0])
        line_series_data = self._extract_series_data(line_series_list[0])
        
        categories = bar_series_data["categories"] or line_series_data["categories"]
        
        datasets = [
            {
                "name": bar_series_data["name"],
                "type": "bar",
                "data": bar_series_data["values"],
                "options": {"yAxis": "left"}
            },
            {
                "name": line_series_data["name"],
                "type": "line",
                "data": line_series_data["values"],
                "options": {"yAxis": "right"}
            }
        ]
        
        return {
            "type": "combo",
            "title": title,
            "categories": categories,
            "datasets": datasets,
            "options": {
                "y_axes": {
                    "left": {"label": bar_series_data["name"]},
                    "right": {"label": line_series_data["name"]}
                }
            }
        }

    def _generate_nested_pie_config(self, plot_area: Dict[str, Any], title: str, palette: str) -> Dict[str, Any]:
        """生成嵌套饼图配置"""
        of_pie_chart = plot_area.get('c:ofPieChart', {})
        series = of_pie_chart.get('c:ser', {})
        
        series_data = self._extract_series_data(series)
        categories = series_data["categories"]
        raw_values = series_data["values"]
        
        # 获取分割位置
        split_pos = 3  # 默认值
        if 'c:splitPos' in of_pie_chart:
            split_pos_elem = of_pie_chart['c:splitPos']
            if '@val' in split_pos_elem:
                split_pos = int(split_pos_elem['@val'])
        
        # 计算内外圈数据
        num_total_points = len(categories)
        num_inner_points = split_pos
        num_outer_points_explicit = num_total_points - num_inner_points

        # 内圈数据（省内游客的细分）
        inner_categories = categories[num_outer_points_explicit:]
        raw_inner_values = raw_values[num_outer_points_explicit:]
        sum_raw_inner = sum(raw_inner_values)

        # 外圈显式数据
        outer_explicit_categories = categories[:num_outer_points_explicit]
        raw_outer_explicit_values = raw_values[:num_outer_points_explicit]

        # 根据数据推断父级名称
        parent_name = "省内游客"  # 可以根据实际数据动态确定

        # 构建内圈数据
        inner_data = []
        if sum_raw_inner > 0:
            for i, category in enumerate(inner_categories):
                inner_data.append({
                    "name": category,
                    "value": raw_inner_values[i] / sum_raw_inner,  # 转换为比例
                    "parent": parent_name
                })

        # 构建外圈数据
        total_raw_value = sum(raw_values)
        outer_data = []
        if total_raw_value > 0:
            for i, category in enumerate(outer_explicit_categories):
                outer_data.append({
                    "name": category,
                    "value": raw_outer_explicit_values[i] / total_raw_value
                })
            # 添加省内游客总体
            outer_data.append({
                "name": parent_name,
                "value": sum_raw_inner / total_raw_value
            })

        return {
            "type": "nested-pie",
            "title": title,
            "data": {
                "outer": outer_data,
                "inner": inner_data
            }
        }

    def _generate_simple_pie_config(self, plot_area: Dict[str, Any], title: str, palette: str) -> Dict[str, Any]:
        """生成简单饼图配置"""
        pie_chart = plot_area.get('c:pieChart', {})
        series = pie_chart.get('c:ser', {})
        if not isinstance(series, list):
            series = [series]
        
        if not series:
            return self._generate_error_config("饼图没有数据系列")

        series_data = self._extract_series_data(series[0])
        
        # 转换为D3饼图组件期望的格式
        datasets = []
        for cat, val in zip(series_data["categories"], series_data["values"]):
            datasets.append({"name": cat, "value": val})
        
        return {
            "type": "simple-pie",
            "title": title,
            "datasets": datasets
        }

    def _generate_bar_config(self, plot_area: Dict[str, Any], title: str, palette: str) -> Dict[str, Any]:
        """生成柱状图配置"""
        bar_chart = plot_area.get('c:barChart', {})
        series_list = bar_chart.get('c:ser', [])
        if not isinstance(series_list, list):
            series_list = [series_list]

        if not series_list:
            return self._generate_error_config("柱状图没有数据系列")
        
        datasets = []
        all_categories = []
        for series in series_list:
            series_data = self._extract_series_data(series)
            if not all_categories:
                all_categories = series_data["categories"]
            
            datasets.append({
                "name": series_data["name"],
                "type": "bar",
                "data": series_data["values"],
                "options": {"yAxis": "left"}
            })
        
        return {
            "type": "combo", # 重用组合图组件以简化实现
            "title": title,
            "categories": all_categories,
            "datasets": datasets,
            "options": {
                "y_axes": {
                    "left": {"label": "数值"}
                }
            }
        }

    def _generate_error_config(self, error_message: str) -> Dict[str, Any]:
        """生成错误配置"""
        return {
            "type": "error",
            "title": "图表生成错误",
            "error": error_message,
            "datasets": []
        }