import asyncio
from database.connection import SessionLocal
from services.auth_service import AuthService
from models.schemas import UserCreate, UserRole

async def create_test_user():
    """创建一个用于测试的临时用户"""
    
    db = SessionLocal()
    try:
        print("正在创建测试用户...")
        test_user = UserCreate(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            password="password", # 设置一个简单的密码
            role=UserRole.USER
        )
        
        # 检查用户是否已存在
        existing_user = AuthService.get_user_by_email(db, email=test_user.email)
        if existing_user:
            print(f"用户 {test_user.email} 已存在，无需重复创建。")
        else:
            created_user = AuthService.create_user(db, test_user)
            print(f"成功创建用户: {created_user.email}")
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(create_test_user())
