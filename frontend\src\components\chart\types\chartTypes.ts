// 基础图表属性接口
export interface BaseChartProps {
  width?: number;
  height?: number;
  className?: string;
  title?: string;
  colors?: string[];
  showLegend?: boolean;
  interactive?: boolean;
}

// 基础数据点接口
export interface BaseDataPoint {
  name: string;
  value: number;
}

// 单一柱状图相关类型
export interface SimpleBarChartData extends BaseDataPoint {}

export interface SimpleBarChartProps extends BaseChartProps {
  data: SimpleBarChartData[];
  barColor?: string;
  showValues?: boolean;
  orientation?: 'vertical' | 'horizontal';
  xAxisLabel?: string;
  yAxisLabel?: string;
  animate?: boolean;
  animationDuration?: number;
}

// 单一饼图相关类型
export interface SimplePieChartData extends BaseDataPoint {
  color?: string;
}

export interface SimplePieChartProps extends BaseChartProps {
  data: SimplePieChartData[];
  showPercentages?: boolean;
  innerRadius?: number; // 0为饼图，>0为环形图
  outerRadius?: number;
  showLabels?: boolean;
  labelPosition?: 'inside' | 'outside';
  animate?: boolean;
  animationDuration?: number;
}

// 组合图表数据类型 (已存在，这里重新定义以保持一致性)
export interface ComboChartData {
  city: string;
  visitors: number;
  growth: number;
}

export interface ComboChartProps extends BaseChartProps {
  data: ComboChartData[];
  barColor?: string;
  lineColor?: string;
  xAxisLabel?: string;
  yAxisLeftLabel?: string;
  yAxisRightLabel?: string;
}

// 嵌套饼图数据类型 (已存在，这里重新定义以保持一致性)
export interface NestedPieData {
  outer: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  inner: Array<{
    name: string;
    value: number;
    color: string;
    parent: string;
  }>;
}

export interface NestedPieChartProps extends BaseChartProps {
  data: NestedPieData;
}

// 图表配置接口
export interface ChartConfig {
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  backgroundColor?: string;
  fontFamily?: string;
  fontSize?: number;
  textColor?: string;
}

// Hook相关类型
export interface UseD3ChartOptions extends ChartConfig {
  width?: number;
  height?: number;
}

export interface UseD3ChartReturn {
  svgRef: React.RefObject<SVGSVGElement>;
  containerRef: React.RefObject<HTMLDivElement>;
  dimensions: {
    width: number;
    height: number;
    innerWidth: number;
    innerHeight: number;
  };
}

// 导出功能类型
export interface ExportOptions {
  filename?: string;
  format?: 'svg' | 'png' | 'pdf';
  quality?: number;
  backgroundColor?: string;
}

// 颜色方案类型
export type ColorScheme = 'business' | 'medical' | 'scientific' | 'custom';

export interface ColorSchemeConfig {
  primary: string[];
  secondary?: string[];
  accent?: string;
  background?: string;
  text?: string;
}

// 动画类型
export interface AnimationConfig {
  duration?: number;
  easing?: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
}

// 交互类型
export interface InteractionConfig {
  hover?: boolean;
  click?: boolean;
  tooltip?: boolean;
  selection?: boolean;
}

// 响应式配置
export interface ResponsiveConfig {
  breakpoints?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  adaptiveLayout?: boolean;
}

// ===============================
// 新增：D3.js 标准化配置类型定义
// ===============================

// 组合图数据集类型
export interface D3ComboDataset {
  name: string;
  type: 'bar' | 'line' | 'area';
  data: number[];
  options: {
    yAxis: 'left' | 'right';
  };
}

// 嵌套饼图数据集类型
export interface D3NestedPieDataset {
  name: string;
  value: number;
  children?: Array<{
    name: string;
    value: number;
  }>;
}

// 组合图配置
export interface D3ComboChartConfig {
  type: 'combo';
  title: string;
  categories: string[];
  datasets: D3ComboDataset[];
  options: {
    y_axes: {
      left: { label: string };
      right: { label: string };
    };
  };
}

// 嵌套饼图配置
export interface D3NestedPieChartConfig {
  type: 'nested-pie';
  title: string;
  datasets?: D3NestedPieDataset[]; // 旧格式（向后兼容）
  data?: {  // 新格式
    outer: Array<{ name: string; value: number }>;
    inner: Array<{ name: string; value: number; parent: string }>;
  };
}

// 统一的D3图表配置类型
// 简单饼图配置
export interface D3SimplePieChartConfig {
  type: 'simple-pie';
  title: string;
  datasets: Array<{
    name: string;
    value: number;
  }>;
}

export type D3ChartConfig = D3ComboChartConfig | D3NestedPieChartConfig | D3SimplePieChartConfig;

// 图表容器Props
export interface D3ChartContainerProps {
  chartConfig: D3ChartConfig;
  width?: number;
  height?: number;
  className?: string;
  onEdit?: () => void;
  onDelete?: () => void;
}

// API响应类型
export interface ChartApiResponse {
  success: boolean;
  charts: Array<{
    id: string;
    chartId: string;
    title: string;
    config: D3ChartConfig;
  }>;
  tables: any[];
  markdown_content: string;
  errors?: string[];
}