// 导入所有API模块
import { apiClient } from './client';
import { reportsApi } from './reports';
import { aiChatApi } from './ai-chat';
import { chartsApi } from './charts';
import { dataApi } from './data';
import { wordApi } from './word';

// 导出所有API模块
export * from './client';
export * from './reports';
export * from './ai-chat';
export * from './charts';
export * from './data';
export * from './word';

// 导出API实例
export { apiClient, reportsApi, aiChatApi, chartsApi, dataApi, wordApi };

// 默认导出所有API
export default {
  client: apiClient,
  reports: reportsApi,
  aiChat: aiChatApi,
  charts: chartsApi,
  data: dataApi,
  word: wordApi,
}; 