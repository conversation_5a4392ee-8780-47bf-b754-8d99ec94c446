"""
Excel图表XML解析器
从Excel图表的XML数据中提取结构化信息，为ChartJS配置生成做准备
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple
import re
import json


class ChartXMLParser:
    """Excel图表XML解析器"""
    
    def __init__(self):
        # Excel图表命名空间
        self.namespaces = {
            'c': 'http://schemas.openxmlformats.org/drawingml/2006/chart',
            'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
            'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships'
        }
    
    def parse_chart_xml(self, xml_content: str) -> Dict[str, Any]:
        """
        解析Excel图表XML，提取图表数据
        
        Args:
            xml_content: Excel图表的XML内容
            
        Returns:
            包含图表信息的字典
        """
        try:
            # 解析XML
            root = ET.fromstring(xml_content)
            
            # 提取基本信息
            chart_info = {
                "chart_type": self._detect_chart_type(root),
                "title": self._extract_title(root),
                "series": self._extract_series_data(root),
                "categories": self._extract_categories(root),
                "values": self._extract_values(root),
                "styling": self._extract_styling(root),
                "metadata": {
                    "xml_length": len(xml_content),
                    "has_title": self._has_title(root),
                    "series_count": self._count_series(root),
                    "data_points": self._count_data_points(root)
                }
            }
            
            return chart_info
            
        except Exception as e:
            return {
                "error": str(e),
                "chart_type": "unknown",
                "series": [],
                "categories": [],
                "values": []
            }
    
    def _detect_chart_type(self, root: ET.Element) -> str:
        """检测图表类型"""
        # 查找图表类型元素
        chart_types = {
            'pieChart': 'pie',
            'ofPieChart': 'pie',
            'barChart': 'bar', 
            'lineChart': 'line',
            'areaChart': 'area',
            'scatterChart': 'scatter',
            'bubbleChart': 'bubble'
        }
        
        for xml_type, chart_type in chart_types.items():
            if root.find(f'.//c:{xml_type}', self.namespaces) is not None:
                return chart_type
        
        return 'unknown'
    
    def _extract_title(self, root: ET.Element) -> str:
        """提取图表标题"""
        # 查找标题元素
        title_elem = root.find('.//c:title//a:t', self.namespaces)
        if title_elem is not None and title_elem.text:
            return title_elem.text.strip()
        
        # 如果没有找到标题，返回空字符串
        return ""
    
    def _has_title(self, root: ET.Element) -> bool:
        """检查是否有标题"""
        auto_deleted = root.find('.//c:autoTitleDeleted', self.namespaces)
        return auto_deleted is None or auto_deleted.get('val', '0') == '0'
    
    def _extract_series_data(self, root: ET.Element) -> List[Dict[str, Any]]:
        """提取数据系列"""
        series_list = []
        
        # 查找不同类型的图表及其系列
        chart_types = ['barChart', 'lineChart', 'pieChart', 'ofPieChart', 'areaChart', 'scatterChart']
        
        for chart_type in chart_types:
            chart_element = root.find(f'.//c:{chart_type}', self.namespaces)
            if chart_element is not None:
                # 查找该图表类型下的所有系列
                series_elements = chart_element.findall('.//c:ser', self.namespaces)
                
                for series in series_elements:
                    series_data = {
                        "index": self._get_series_index(series),
                        "name": self._get_series_name(series),
                        "categories": self._extract_series_categories(series),
                        "values": self._extract_series_values(series),
                        "formatting": self._extract_series_formatting(series),
                        "chart_type": chart_type.replace('Chart', '').lower().replace('ofpie', 'pie')  # 标记图表类型
                    }
                    series_list.append(series_data)
        
        return series_list
    
    def _get_series_index(self, series: ET.Element) -> int:
        """获取系列索引"""
        idx_elem = series.find('.//c:idx', self.namespaces)
        if idx_elem is not None:
            return int(idx_elem.get('val', 0))
        return 0
    
    def _get_series_name(self, series: ET.Element) -> str:
        """获取系列名称"""
        # 查找系列名称
        name_elem = series.find('.//c:tx//a:t', self.namespaces)
        if name_elem is not None and name_elem.text:
            return name_elem.text.strip()
        
        # 查找字符串引用
        str_ref = series.find('.//c:tx//c:strRef//c:strCache//c:pt//c:v', self.namespaces)
        if str_ref is not None and str_ref.text:
            return str_ref.text.strip()
        
        return f"系列{self._get_series_index(series) + 1}"
    
    def _extract_series_categories(self, series: ET.Element) -> List[str]:
        """提取系列分类标签"""
        categories = []
        
        # 查找分类数据
        cat_cache = series.find('.//c:cat//c:strCache', self.namespaces)
        if cat_cache is not None:
            points = cat_cache.findall('.//c:pt', self.namespaces)
            for point in points:
                value_elem = point.find('.//c:v', self.namespaces)
                if value_elem is not None and value_elem.text:
                    categories.append(value_elem.text.strip())
        
        return categories
    
    def _extract_series_values(self, series: ET.Element) -> List[float]:
        """提取系列数值"""
        values = []
        
        # 查找数值数据
        val_cache = series.find('.//c:val//c:numCache', self.namespaces)
        if val_cache is not None:
            points = val_cache.findall('.//c:pt', self.namespaces)
            for point in points:
                value_elem = point.find('.//c:v', self.namespaces)
                if value_elem is not None and value_elem.text:
                    try:
                        # 尝试转换为浮点数
                        value = float(value_elem.text)
                        values.append(value)
                    except ValueError:
                        values.append(0.0)
        
        return values
    
    def _extract_series_formatting(self, series: ET.Element) -> Dict[str, Any]:
        """提取系列格式信息"""
        formatting = {
            "colors": [],
            "patterns": [],
            "line_style": None
        }
        
        # 提取颜色信息
        color_elements = series.findall('.//a:solidFill//a:srgbClr', self.namespaces)
        for color_elem in color_elements:
            color_val = color_elem.get('val')
            if color_val:
                formatting["colors"].append(f"#{color_val}")
        
        # 提取主题颜色
        scheme_colors = series.findall('.//a:solidFill//a:schemeClr', self.namespaces)
        for scheme_elem in scheme_colors:
            scheme_val = scheme_elem.get('val')
            if scheme_val:
                formatting["patterns"].append(scheme_val)
        
        return formatting
    
    def _extract_categories(self, root: ET.Element) -> List[str]:
        """提取全局分类标签"""
        categories = []
        
        # 查找第一个系列的分类数据作为全局分类
        first_series = root.find('.//c:ser', self.namespaces)
        if first_series is not None:
            categories = self._extract_series_categories(first_series)
        
        return categories
    
    def _extract_values(self, root: ET.Element) -> List[List[float]]:
        """提取所有系列的数值"""
        all_values = []
        
        series_elements = root.findall('.//c:ser', self.namespaces)
        for series in series_elements:
            values = self._extract_series_values(series)
            if values:
                all_values.append(values)
        
        return all_values
    
    def _extract_styling(self, root: ET.Element) -> Dict[str, Any]:
        """提取图表样式信息"""
        styling = {
            "background_color": None,
            "plot_area": {},
            "legend": {},
            "axes": {}
        }
        
        # 提取背景颜色
        bg_fill = root.find('.//c:spPr//a:solidFill//a:srgbClr', self.namespaces)
        if bg_fill is not None:
            styling["background_color"] = f"#{bg_fill.get('val', 'FFFFFF')}"
        
        # 提取图例信息
        legend = root.find('.//c:legend', self.namespaces)
        if legend is not None:
            legend_pos = legend.find('.//c:legendPos', self.namespaces)
            if legend_pos is not None:
                styling["legend"]["position"] = legend_pos.get('val', 'r')
        
        return styling
    
    def _count_series(self, root: ET.Element) -> int:
        """统计数据系列数量"""
        series_elements = root.findall('.//c:ser', self.namespaces)
        return len(series_elements)
    
    def _count_data_points(self, root: ET.Element) -> int:
        """统计数据点总数"""
        total_points = 0
        
        series_elements = root.findall('.//c:ser', self.namespaces)
        for series in series_elements:
            val_cache = series.find('.//c:val//c:numCache', self.namespaces)
            if val_cache is not None:
                pt_count = val_cache.find('.//c:ptCount', self.namespaces)
                if pt_count is not None:
                    try:
                        total_points += int(pt_count.get('val', 0))
                    except ValueError:
                        pass
        
        return total_points
    
    def get_chart_summary(self, xml_content: str) -> Dict[str, Any]:
        """
        获取图表摘要信息
        
        Args:
            xml_content: Excel图表的XML内容
            
        Returns:
            图表摘要信息
        """
        chart_data = self.parse_chart_xml(xml_content)
        
        summary = {
            "chart_type": chart_data["chart_type"],
            "has_data": len(chart_data["values"]) > 0,
            "categories_count": len(chart_data["categories"]),
            "series_count": len(chart_data["series"]),
            "total_data_points": sum(len(series) for series in chart_data["values"]),
            "has_title": bool(chart_data["title"]),
            "xml_size": chart_data["metadata"]["xml_length"]
        }
        
        return summary


def test_parser():
    """测试解析器功能"""
    parser = ChartXMLParser()
    
    # 测试用例 - 这里可以放入实际的XML内容进行测试
    sample_xml = """<?xml version="1.0" encoding="UTF-8"?>
    <c:chartSpace xmlns:c="http://schemas.openxmlformats.org/drawingml/2006/chart">
        <c:chart>
            <c:plotArea>
                <c:pieChart>
                    <c:ser>
                        <c:cat>
                            <c:strCache>
                                <c:pt><c:v>类别1</c:v></c:pt>
                                <c:pt><c:v>类别2</c:v></c:pt>
                            </c:strCache>
                        </c:cat>
                        <c:val>
                            <c:numCache>
                                <c:pt><c:v>10</c:v></c:pt>
                                <c:pt><c:v>20</c:v></c:pt>
                            </c:numCache>
                        </c:val>
                    </c:ser>
                </c:pieChart>
            </c:plotArea>
        </c:chart>
    </c:chartSpace>"""
    
    result = parser.parse_chart_xml(sample_xml)
    print("解析结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    test_parser() 