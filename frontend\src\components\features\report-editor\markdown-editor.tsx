"use client";

import React, { useState, useRef } from "react";
import { DndContext, useDroppable, DragEndEvent } from "@dnd-kit/core";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, Edit, Code } from "lucide-react";
import ReactMarkdown, { Components } from "react-markdown";
import remarkGfm from "remark-gfm";

// Assuming these components are in the same folder or correctly imported
import SmartChartRenderer from "./smart-chart-renderer";
// You'll need a similar table component to the one in your display page
import { CustomTableComponent } from "@/components/ui/custom-table";

// --- Type Definitions (Aligned with new architecture) ---

interface TableData {
  id: string;
  title: string;
  headers: string[];
  rows: string[][];
}

interface ChartData {
  id: string; // The unique ID from the backend
  chartId: string; // The specific ID for the chart instance
  title: string;
  config: any; // The full Chart.js config object
}

interface MarkdownEditorProps {
  content: string;
  onChange: (newContent: string) => void;
  onComponentDrop: (item: { type: string; id: string }) => void;
  template: any; // Pass template for rendering
  onTextSelect?: (selectedText: string) => void; // 添加选中文本的回调函数
}

// --- Main Editor Component (Simplified) ---

function EditorArea({
  content,
  onChange,
  onComponentDrop,
  template,
  onTextSelect,
}: MarkdownEditorProps) {
  const [mode, setMode] = useState<"edit" | "preview" | "split">("split");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { isOver, setNodeRef } = useDroppable({
    id: "drop-area",
  });

  // --- Core Text Editing Logic (Simplified) ---

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  // 处理文本选择事件
  const handleTextSelection = () => {
    if (!textareaRef.current || !onTextSelect) return;
    
    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    
    // 如果有选中文本
    if (start !== end) {
      const selectedText = content.substring(start, end);
      if (selectedText.trim()) {
        onTextSelect(selectedText);
      }
    }
  };

  const insertMarkdown = (before: string, after: string = "") => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);

    const newContent =
      content.substring(0, start) +
      before +
      selectedText +
      after +
      content.substring(end);

    onChange(newContent);

    // Restore cursor position correctly after state update
    setTimeout(() => {
      textarea.focus();
      textarea.selectionStart = textarea.selectionEnd =
        start + before.length + selectedText.length;
    }, 0);
  };

  // --- Preview Logic using ReactMarkdown (The biggest simplification) ---

  const markdownComponents: Components = {
    // Override the <p> tag renderer to find and replace our placeholders
    p: ({ node, children }) => {
      const textContent = (node?.children[0] as any)?.value?.trim() || "";

      // Render Chart Component
      if (textContent.startsWith("{{CHART_") && textContent.endsWith("}}")) {
        const id = textContent.substring(8, textContent.length - 2);
        const chartData = template?.charts?.find((c: any) => c.id === id);

        return chartData ? (
          <div className="my-6">
            <SmartChartRenderer
              chartConfig={chartData.config}
            />
          </div>
        ) : (
          <div className="table-placeholder">❌ 无法加载图表: {id}</div>
        );
      }

      // Render Table Component
      if (textContent.startsWith("{{TABLE_") && textContent.endsWith("}}")) {
        const id = textContent.substring(8, textContent.length - 2);
        const tableData = template?.dataBindings.find((t: any) => t.id === id);

        return tableData ? (
          <CustomTableComponent data={tableData} />
        ) : (
          <div className="table-placeholder">❌ 无法加载表格: {id}</div>
        );
      }

      // Default paragraph rendering
      return <p>{children}</p>;
    },
  };

  // --- JSX Return ---

  return (
    <div className="h-full flex flex-col border rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="flex items-center justify-between p-2 border-b bg-gray-50">
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => insertMarkdown("**", "**")}
          >
            <strong>B</strong>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => insertMarkdown("*", "*")}
          >
            <em>I</em>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => insertMarkdown("\n- ", "")}
          >
            List
          </Button>
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant={mode === "edit" ? "secondary" : "ghost"}
            size="sm"
            onClick={() => setMode("edit")}
          >
            <Edit className="w-4 h-4 mr-1" /> 编辑
          </Button>
          <Button
            variant={mode === "preview" ? "secondary" : "ghost"}
            size="sm"
            onClick={() => setMode("preview")}
          >
            <Eye className="w-4 h-4 mr-1" /> 预览
          </Button>
          <Button
            variant={mode === "split" ? "secondary" : "ghost"}
            size="sm"
            onClick={() => setMode("split")}
          >
            <Code className="w-4 h-4 mr-1" /> 分割
          </Button>
        </div>
      </div>

      {/* Editor and Preview Panes */}
      <div className="flex-1 flex" ref={setNodeRef}>
        {(mode === "edit" || mode === "split") && (
          <textarea
            ref={textareaRef}
            value={content}
            onChange={handleContentChange}
            onMouseUp={handleTextSelection}
            onKeyUp={handleTextSelection}
            className={`p-4 font-mono text-sm outline-none resize-none h-full ${mode === "split" ? "w-1/2" : "w-full"}`}
            placeholder="开始编写报告内容..."
          />
        )}
        {(mode === "preview" || mode === "split") && (
          <div
            className={`p-4 overflow-auto prose prose-sm max-w-none h-full ${mode === "split" ? "w-1/2 border-l" : "w-full"}`}
          >
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={markdownComponents}
            >
              {content}
            </ReactMarkdown>
          </div>
        )}

        {/* Drag Overlay */}
        {isOver && (
          <div className="absolute inset-0 bg-blue-100 bg-opacity-50 flex items-center justify-center pointer-events-none">
            <div className="bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold">
              释放以插入组件
            </div>
          </div>
        )}
      </div>

      {/* Placeholder styles */}
      <style jsx global>{`
        .table-placeholder {
          background: #f3f4f6;
          border: 2px dashed #9ca3af;
          padding: 20px;
          text-align: center;
          margin: 10px 0;
          border-radius: 4px;
          color: #6b7280;
        }
      `}</style>
    </div>
  );
}

export default function MarkdownEditor(props: MarkdownEditorProps) {
  const handleDragEnd = (event: DragEndEvent) => {
    const { over, active } = event;
    if (over && over.id === "drop-area") {
      const item = active.data.current;
      if (item && item.type && item.id) {
        props.onComponentDrop({ type: item.type, id: item.id });
      }
    }
  };

  return (
    <DndContext onDragEnd={handleDragEnd}>
      <EditorArea {...props} />
    </DndContext>
  );
}
