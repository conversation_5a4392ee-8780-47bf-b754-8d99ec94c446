"use client";

import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { wordApi } from "@/api/word";
import {
  Upload,
  RefreshCw,
  FileText,
  ChevronRight,
  AlertTriangle,
  Loader2,
} from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import SmartChartRenderer from "../features/report-editor/smart-chart-renderer";
import { Components } from "react-markdown";
import { CustomTableComponent, TableData } from "../ui/custom-table";
import { D3ChartConfig } from "@/components/chart/types/chartTypes";

// --- 类型定义 ---
interface ProcessedDocument {
  id: number;
  filename: string;
  created_at: string;
  markdown_content: string;
  tables: any[];
  charts: any[];
}

// 更新ChartData接口以匹配D3ChartConfig
interface ChartData {
  chartId: string;
  title: string;
  description?: string;
  config: D3ChartConfig;
}

// --- 主组件 ---
export default function WordFormatPage() {
  const [documents, setDocuments] = useState<
    Omit<ProcessedDocument, "markdown_content" | "tables" | "charts">[]
  >([]);
  const [selectedDoc, setSelectedDoc] = useState<Omit<
    ProcessedDocument,
    "markdown_content" | "tables" | "charts"
  > | null>(null);
  const [markdownContent, setMarkdownContent] = useState<string>("");
  const [tables, setTables] = useState<TableData[]>([]);
  const [charts, setCharts] = useState<ChartData[]>([]);
  const [loadingDocs, setLoadingDocs] = useState(false);
  const [loadingContent, setLoadingContent] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const API_BASE_URL = "http://localhost:8000";

  const fetchDocuments = useCallback(async () => {
    setLoadingDocs(true);
    setError(null);
    try {
      const data = await wordApi.getProcessedDocuments();
      setDocuments(data);
    } catch (err) {
      const msg = err instanceof Error ? err.message : "获取文档列表失败";
      setError(msg);
    } finally {
      setLoadingDocs(false);
    }
  }, []);

  const fetchDocumentContent = async (docId: number) => {
    if (selectedDoc?.id === docId) return;

    setLoadingContent(true);
    setError(null);
    const docInfo = documents.find((d) => d.id === docId) || null;
    setSelectedDoc(docInfo);
    setMarkdownContent("");
    setTables([]);
    setCharts([]);

    try {
      const data: ProcessedDocument = await wordApi.getDocumentContent(docId);

      setMarkdownContent(data.markdown_content);
      setTables(data.tables);
      setCharts(data.charts);
    } catch (err) {
      const msg = err instanceof Error ? err.message : "获取文档内容失败";
      setError(msg);
      setSelectedDoc(null);
    } finally {
      setLoadingContent(false);
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile) return;
    setIsUploading(true);
    setError(null);
    try {
      await wordApi.uploadAndProcessDocument(selectedFile);
      await fetchDocuments();
      setSelectedFile(null);
    } catch (err) {
      const msg = err instanceof Error ? err.message : "上传处理失败";
      setError(msg);
    } finally {
      setIsUploading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  const markdownComponents: Components = {
    p: ({ node, children }) => {
      const textContent = (node?.children[0] as any)?.value?.trim() || "";

      // 检查图表占位符: [CHART:chart-abc]
      if (textContent.startsWith("[CHART:") && textContent.endsWith("]")) {
        const chartId = textContent.substring(7, textContent.length - 1);
        const chartData = charts.find((c) => c.chartId === chartId);

        if (chartData) {
          return (
            <div className="my-6">
              <SmartChartRenderer chartConfig={chartData.config} />
            </div>
          );
        }
        return (
          <div className="text-red-500 my-4 p-4 border border-red-200 bg-red-50">
            图表加载失败: {chartId}
          </div>
        );
      }

      // 检查表格占位符: [TABLE:table-123]
      if (textContent.startsWith("[TABLE:") && textContent.endsWith("]")) {
        const tableId = textContent.substring(7, textContent.length - 1);
        const tableData = tables.find((t) => t.id === tableId);
        if (tableData) {
          return <CustomTableComponent data={tableData} />;
        }
        return (
          <div className="text-red-500 my-4 p-4 border border-red-200 bg-red-50">
            表格加载失败: {tableId}
          </div>
        );
      }

      // 默认渲染为普通段落
      return <p>{children}</p>;
    },
  };

  // --- 渲染 ---
  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-8xl mx-auto">
        <header className="mb-6">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            Word 文档智能解析与格式化
          </h1>
          <p className="text-sm sm:text-base text-gray-600 mt-1">
            上传 .docx 文档，系统将自动提取内容、解析图表并进行格式化展示。
          </p>
        </header>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <AlertTriangle className="w-5 h-5 mr-3 text-red-600 inline-block" />
            <strong>操作失败:</strong> {error}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-4 xl:col-span-3 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <Upload className="w-5 h-5 mr-2" />
                  上传新文档
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <input
                    type="file"
                    accept=".docx"
                    onChange={(e) =>
                      setSelectedFile(e.target.files?.[0] || null)
                    }
                    className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    disabled={isUploading}
                  />
                  <Button
                    onClick={handleFileUpload}
                    disabled={!selectedFile || isUploading}
                    className="w-full"
                  >
                    {isUploading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        上传处理中...
                      </>
                    ) : (
                      "开始上传"
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between text-lg">
                  <span className="flex items-center">
                    <FileText className="w-5 h-5 mr-2" />
                    已处理文档
                  </span>
                  <Button
                    onClick={fetchDocuments}
                    disabled={loadingDocs}
                    variant="ghost"
                    size="sm"
                  >
                    <RefreshCw
                      className={`w-4 h-4 ${loadingDocs ? "animate-spin" : ""}`}
                    />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loadingDocs ? (
                  <div className="text-center py-4 text-gray-500">
                    <Loader2 className="w-6 h-6 mx-auto animate-spin" />
                  </div>
                ) : documents.length === 0 ? (
                  <p className="text-sm text-gray-500 text-center py-4">
                    暂无文档，请先上传。
                  </p>
                ) : (
                  <ul className="space-y-2">
                    {documents.map((doc) => (
                      <li key={doc.id}>
                        <button
                          onClick={() => fetchDocumentContent(doc.id)}
                          className={`w-full text-left p-3 rounded-lg transition-colors flex items-center justify-between ${selectedDoc?.id === doc.id ? "bg-blue-100 text-blue-800" : "hover:bg-gray-100"}`}
                        >
                          <div className="flex-1 overflow-hidden">
                            <p className="font-medium text-sm truncate">
                              {doc.filename}
                            </p>
                            <p className="text-xs text-gray-500">
                              {new Date(doc.created_at).toLocaleString()}
                            </p>
                          </div>
                          <ChevronRight className="w-5 h-5 text-gray-400 ml-2" />
                        </button>
                      </li>
                    ))}
                  </ul>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Column */}
          <div className="lg:col-span-8 xl:col-span-9">
            <Card className="min-h-[calc(100vh-12rem)]">
              <CardHeader>
                <CardTitle className="text-lg">
                  {selectedDoc ? `预览: ${selectedDoc.filename}` : "内容预览"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loadingContent ? (
                  <div className="flex items-center justify-center h-96 text-gray-500">
                    <Loader2 className="w-8 h-8 mr-3 animate-spin" />
                    <span>正在加载文档内容...</span>
                  </div>
                ) : markdownContent ? (
                  <div className="prose prose-sm sm:prose-base max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={markdownComponents}
                    >
                      {markdownContent}
                    </ReactMarkdown>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-96 text-gray-400">
                    <FileText size={48} className="mb-4" />
                    <p>请从左侧列表选择一个文档进行预览</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
