import shutil
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from pathlib import Path
import traceback
import os

from database.connection import get_db
from models import schemas
from models.database import ProcessedDocument
from services.chart_service import ChartService

router = APIRouter(
    prefix="/word-format",
    tags=["word-format"],
)

@router.post("/upload", response_model=schemas.ProcessedDocumentMetadata)
async def upload_and_process_word_document(
    file: UploadFile = File(...), 
    db: Session = Depends(get_db)
):
    """
    上传、解析Word文档，生成D3.js兼容的图表配置
    """
    if not file.filename or not file.filename.endswith('.docx'):
        raise HTTPException(status_code=400, detail="仅支持.docx格式文件")

    tmp_dir = Path("./tmp/word_uploads")
    tmp_dir.mkdir(parents=True, exist_ok=True)
    file_path = tmp_dir / file.filename
    
    try:
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        chart_service = ChartService()
        # 调用D3.js处理函数
        result = await chart_service.process_word_document_complete_d3(str(file_path))

        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"文档处理失败: {result['errors']}")

        # 使用新的数据结构创建数据库记录
        doc_to_create = schemas.ProcessedDocumentCreate(
            filename=file.filename,
            markdown_content=result["markdown_content"],
            tables=result["tables"],
            charts=result["charts"]
        )
        
        db_document = ProcessedDocument(**doc_to_create.model_dump())
        db.add(db_document)
        db.commit()
        db.refresh(db_document)

        return db_document

    except Exception as e:
        # 在开发过程中，打印完整的异常以帮助调试
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"处理文件时发生严重错误: {str(e)}")
    finally:
        # 清理临时文件
        if file_path.exists():
            file_path.unlink()


@router.post("/upload-d3")
async def upload_and_process_word_document_d3(
    file: UploadFile = File(...), 
    db: Session = Depends(get_db)
):
    """
    上传、解析Word文档，生成D3.js兼容的图表配置
    """
    if not file.filename or not file.filename.endswith('.docx'):
        raise HTTPException(status_code=400, detail="仅支持.docx格式文件")

    tmp_dir = Path("./tmp/word_uploads")
    tmp_dir.mkdir(parents=True, exist_ok=True)
    file_path = tmp_dir / file.filename
    
    try:
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        chart_service = ChartService()
        # 调用新的D3处理函数
        result = await chart_service.process_word_document_complete_d3(str(file_path))

        if not result["success"]:
            return {
                "success": False,
                "error": "文档处理失败",
                "errors": result["errors"],
                "charts": [],
                "tables": [],
                "markdown_content": ""
            }

        # 返回D3兼容的格式
        return {
            "success": True,
            "markdown_content": result["markdown_content"],
            "tables": result["tables"],
            "charts": result["charts"],
            "errors": result.get("errors", [])
        }

    except Exception as e:
        # 在开发过程中，打印完整的异常以帮助调试
        traceback.print_exc()
        return {
            "success": False,
            "error": f"处理文件时发生严重错误: {str(e)}",
            "errors": [str(e)],
            "charts": [],
            "tables": [],
            "markdown_content": ""
        }
    finally:
        # 清理临时文件
        if file_path.exists():
            file_path.unlink()


@router.get("/documents", response_model=List[schemas.ProcessedDocumentMetadata])
def get_processed_documents(
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db)
):
    """
    获取所有已处理文档的元数据列表
    """
    documents = db.query(ProcessedDocument).order_by(ProcessedDocument.created_at.desc()).offset(skip).limit(limit).all()
    return documents


@router.get("/documents/{document_id}", response_model=schemas.ProcessedDocumentDetail)
def get_processed_document_detail(
    document_id: int, 
    db: Session = Depends(get_db)
):
    """
    根据ID获取单个已处理文档的完整信息
    """
    document = db.query(ProcessedDocument).filter(ProcessedDocument.id == document_id).first()
    if document is None:
        raise HTTPException(status_code=404, detail="未找到指定的文档")
    return document