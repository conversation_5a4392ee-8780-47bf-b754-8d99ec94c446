import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { query, template_context } = body

    if (!query) {
      return NextResponse.json({ error: 'SQL查询不能为空' }, { status: 400 })
    }

    // 转发到后端API
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000'
    const response = await fetch(`${backendUrl}/api/sql-query`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, template_context }),
    })

    if (!response.ok) {
      throw new Error(`后端请求失败: ${response.status}`)
    }

    const result = await response.json()
    
    // 如果后端返回成功，直接返回数据部分
    if (result.success) {
      return NextResponse.json(result.data)
    } else {
      throw new Error(result.error || 'SQL查询执行失败')
    }

  } catch (error) {
    console.error('SQL查询执行失败:', error)
    return NextResponse.json({ 
      error: 'SQL查询执行失败，请检查语法',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
} 