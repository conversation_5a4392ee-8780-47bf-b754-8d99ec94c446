import { apiClient } from './client';
import { D3ChartConfig, ChartApiResponse } from '@/components/chart/types/chartTypes';

export interface WordTemplate {
  id: string;
  name: string;
  filename: string;
  created_at: string;
  size: number;
}

export interface WordParseResult {
  markdown: string;
  charts: any[];
  images: string[];
  metadata: any;
}

export interface WordParseResultD3 {
  success: boolean;
  markdown_content: string;
  charts: Array<{
    id: string;
    chartId: string;
    title: string;
    config: D3ChartConfig;
  }>;
  tables: any[];
  errors?: string[];
}

export interface WordExportRequest {
  reportData: any;
  templateId?: string;
}

// Word格式相关API
export const wordApi = {
  // 获取已处理的文档列表
  async getProcessedDocuments(): Promise<any[]> {
    const response = await apiClient.get<any[]>('/api/word-format/documents');
    return response.data;
  },

  // 获取单个文档内容
  async getDocumentContent(docId: number): Promise<any> {
    const response = await apiClient.get<any>(`/api/word-format/documents/${docId}`);
    return response.data;
  },

  // 上传并处理Word文档 (使用D3.js)
  async uploadAndProcessDocument(file: File): Promise<WordParseResultD3> {
    const response = await apiClient.upload<WordParseResultD3>('/api/word-format/upload', file);
    return response.data;
  },

  // 上传Word模板
  async uploadTemplate(file: File): Promise<WordTemplate> {
    const response = await apiClient.upload<WordTemplate>('/api/word/upload-template', file);
    return response.data;
  },

  // 获取模板列表
  async getTemplates(): Promise<WordTemplate[]> {
    const response = await apiClient.get<WordTemplate[]>('/api/word/templates');
    return response.data;
  },

  // 获取单个模板
  async getTemplate(id: string): Promise<WordTemplate> {
    const response = await apiClient.get<WordTemplate>(`/api/word/templates/${id}`);
    return response.data;
  },

  // 删除模板
  async deleteTemplate(id: string): Promise<{ success: boolean }> {
    await apiClient.delete(`/api/word/templates/${id}`);
    return { success: true };
  },

  // 解析Word文档
  async parseDocument(file: File): Promise<WordParseResult> {
    const response = await apiClient.upload<WordParseResult>('/api/word-parser/process', file);
    return response.data;
  },

  // 导出报告为Word
  async exportToWord(reportData: any, templateId?: string): Promise<void> {
    const filename = `report_${new Date().getTime()}.docx`;
    await apiClient.download('/api/word/export', filename, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reportData, templateId }),
    });
  },
}; 