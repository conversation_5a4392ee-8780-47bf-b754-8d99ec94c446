"use client";

import React, { useState } from "react";
import { useDraggable } from "@dnd-kit/core";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { FileText, BarChart3, Type } from "lucide-react";

interface ComponentItem {
  id: string;
  type: string;
  name: string;
  icon: React.ReactNode;
  template: string;
}

interface DraggableComponentProps {
  component: ComponentItem;
  onSelect: (component: ComponentItem) => void;
}

const DraggableComponent: React.FC<DraggableComponentProps> = ({
  component,
  onSelect,
}) => {
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: `component-${component.id}`,
      data: {
        type: "component",
        component,
      },
    });

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
        opacity: isDragging ? 0.5 : 1,
      }
    : {
        opacity: isDragging ? 0.5 : 1,
      };

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className="cursor-move hover:shadow-md"
      onClick={() => onSelect(component)}
      {...listeners}
      {...attributes}
    >
      <CardContent className="p-3 flex items-center space-x-2">
        {component.icon}
        <span className="text-sm font-medium">{component.name}</span>
      </CardContent>
    </Card>
  );
};

interface ComponentLibraryProps {
  onComponentSelect: (component: ComponentItem) => void;
}

export default function ComponentLibrary({
  onComponentSelect,
}: ComponentLibraryProps) {
  const [selectedTab, setSelectedTab] = useState("all");

  const components: ComponentItem[] = [
    {
      id: "text_heading",
      type: "text",
      name: "标题",
      icon: <Type className="w-4 h-4" />,
      template: "# 标题",
    },
    {
      id: "text_paragraph",
      type: "text",
      name: "段落",
      icon: <FileText className="w-4 h-4" />,
      template: "这是一个段落。",
    },
    {
      id: "chart_bar",
      type: "chart",
      name: "柱状图",
      icon: <BarChart3 className="w-4 h-4" />,
      template: '```chart\n{\n  "type": "bar",\n  "data": {}\n}\n```',
    },
  ];

  return (
    <div className="h-full flex flex-col bg-white border-r">
      <Tabs
        value={selectedTab}
        onValueChange={setSelectedTab}
        className="flex-1 flex flex-col min-h-0"
      >
        <TabsContent value="all" className="flex-1 overflow-y-auto p-3">
          <div className="grid grid-cols-1 gap-3">
            {components.map((component) => (
              <DraggableComponent
                key={component.id}
                component={component}
                onSelect={onComponentSelect}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
