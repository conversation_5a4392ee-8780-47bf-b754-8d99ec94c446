import { useEffect, useRef, useCallback, useState } from 'react';
import * as d3 from 'd3';
import { UseD3ChartOptions, UseD3ChartReturn } from '../types/chartTypes';
import { mergeChartConfig, calculateInnerDimensions } from '../utils/chartHelpers';

export function useD3Chart(
  renderChart: (
    container: d3.Selection<HTMLDivElement, unknown, null, undefined>,
    dimensions: { width: number; height: number; innerWidth: number; innerHeight: number }
  ) => void,
  dependencies: any[] = [],
  options: UseD3ChartOptions = {}
): UseD3ChartReturn {
  const containerRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const [dimensions, setDimensions] = useState({
    width: options.width || 800,
    height: options.height || 500,
    innerWidth: 0,
    innerHeight: 0
  });

  const config = mergeChartConfig(options);

  // 计算内部尺寸
  const updateDimensions = useCallback((width: number, height: number) => {
    const { innerWidth, innerHeight } = calculateInnerDimensions(width, height, config.margin);
    setDimensions({
      width,
      height,
      innerWidth,
      innerHeight
    });
  }, [config.margin]);

  // 响应式处理
  useEffect(() => {
    if (!containerRef.current || !config.responsive) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0) {
          updateDimensions(width, height);
        }
      }
    });

    resizeObserver.observe(containerRef.current);

    // 初始化尺寸
    const rect = containerRef.current.getBoundingClientRect();
    if (rect.width > 0 && rect.height > 0) {
      updateDimensions(rect.width, rect.height);
    } else {
      updateDimensions(options.width || 800, options.height || 500);
    }

    return () => resizeObserver.disconnect();
  }, [config.responsive, updateDimensions, options.width, options.height]);

  // 渲染图表
  useEffect(() => {
    if (!containerRef.current) return;

    const container = d3.select(containerRef.current);
    
    // 清理之前的内容
    container.selectAll('*').remove();
    
    // 调用渲染函数
    renderChart(container, dimensions);
    
    // 清理函数
    return () => {
      container.selectAll('*').remove();
      d3.select('#chart-tooltip').remove();
    };
  }, [renderChart, dimensions, ...dependencies]);

  return {
    svgRef,
    containerRef,
    dimensions
  };
}

export default useD3Chart; 