{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/api/*": ["src/api/*"], "@/components/*": ["src/components/*"], "@/lib/*": ["src/lib/*"], "@/hooks/*": ["src/hooks/*"], "@/types/*": ["src/types/*"]}, "forceConsistentCasingInFileNames": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "include": ["next-env.d.ts", "src/**/*.ts", "src/**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}