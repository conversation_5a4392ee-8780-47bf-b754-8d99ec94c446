import * as d3 from 'd3';
import { ChartConfig } from '../types/chartTypes';
import { DEFAULT_CHART_CONFIG } from './colorSchemes';

// 合并配置
export function mergeChartConfig(userConfig: Partial<ChartConfig> = {}): ChartConfig {
  return {
    ...DEFAULT_CHART_CONFIG,
    ...userConfig,
    margin: {
      ...DEFAULT_CHART_CONFIG.margin,
      ...userConfig.margin
    }
  };
}

// 计算图表内部尺寸
export function calculateInnerDimensions(
  width: number,
  height: number,
  margin: ChartConfig['margin']
) {
  const { top = 0, right = 0, bottom = 0, left = 0 } = margin || {};
  return {
    innerWidth: Math.max(0, width - left - right),
    innerHeight: Math.max(0, height - top - bottom)
  };
}

// 创建SVG元素
export function createSVG(
  container: d3.Selection<HTMLDivElement, unknown, null, undefined>,
  width: number,
  height: number,
  config: ChartConfig
) {
  container.selectAll('svg').remove();
  
  return container
    .append('svg')
    .attr('width', width)
    .attr('height', height)
    .attr('viewBox', `0 0 ${width} ${height}`)
    .style('background-color', config.backgroundColor || '#FFFFFF')
    .style('font-family', config.fontFamily || 'Arial')
    .style('font-size', `${config.fontSize || 12}px`)
    .style('color', config.textColor || '#000000');
}

// 创建主图表组
export function createChartGroup(
  svg: d3.Selection<SVGSVGElement, unknown, null, undefined>,
  margin: ChartConfig['margin']
) {
  const { top = 0, left = 0 } = margin || {};
  
  return svg
    .append('g')
    .attr('class', 'chart-group')
    .attr('transform', `translate(${left}, ${top})`);
}

// 创建标题
export function createTitle(
  svg: d3.Selection<SVGSVGElement, unknown, null, undefined>,
  title: string,
  width: number,
  margin: ChartConfig['margin']
) {
  if (!title) return;
  
  const { top = 0 } = margin || {};
  
  svg
    .append('text')
    .attr('class', 'chart-title')
    .attr('x', width / 2)
    .attr('y', top / 2)
    .attr('text-anchor', 'middle')
    .attr('dominant-baseline', 'middle')
    .style('font-size', '16px')
    .style('font-weight', 'bold')
    .text(title);
}

// 创建工具提示
export function createTooltip() {
  // 移除现有的tooltip
  d3.select('#chart-tooltip').remove();
  
  return d3.select('body')
    .append('div')
    .attr('id', 'chart-tooltip')
    .style('position', 'absolute')
    .style('visibility', 'hidden')
    .style('background', 'rgba(0, 0, 0, 0.8)')
    .style('color', 'white')
    .style('padding', '8px 12px')
    .style('border-radius', '4px')
    .style('font-size', '12px')
    .style('pointer-events', 'none')
    .style('z-index', '1000')
    .style('box-shadow', '0 2px 8px rgba(0, 0, 0, 0.2)');
}

// 显示工具提示
export function showTooltip(
  tooltip: d3.Selection<HTMLDivElement, unknown, null, undefined>,
  content: string,
  event: MouseEvent
) {
  tooltip
    .style('visibility', 'visible')
    .html(content)
    .style('left', `${event.pageX + 10}px`)
    .style('top', `${event.pageY - 10}px`);
}

// 隐藏工具提示
export function hideTooltip(
  tooltip: d3.Selection<HTMLDivElement, unknown, null, undefined>
) {
  tooltip.style('visibility', 'hidden');
}

// 数据验证
export function validateData(data: any[]): boolean {
  if (!Array.isArray(data)) {
    console.warn('Chart data must be an array');
    return false;
  }
  
  if (data.length === 0) {
    console.warn('Chart data array is empty');
    return false;
  }
  
  return true;
}

// 格式化数值
export function formatNumber(value: number, decimals: number = 0): string {
  return value.toLocaleString(undefined, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
}

// 格式化百分比
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${(value * 100).toFixed(decimals)}%`;
}

// 创建线性比例尺
export function createLinearScale(
  domain: [number, number],
  range: [number, number],
  nice: boolean = true
) {
  const scale = d3.scaleLinear()
    .domain(domain)
    .range(range);
  
  return nice ? scale.nice() : scale;
}

// 创建带状比例尺
export function createBandScale(
  domain: string[],
  range: [number, number],
  padding: number = 0.1
) {
  return d3.scaleBand()
    .domain(domain)
    .range(range)
    .padding(padding);
}

// 创建序数比例尺
export function createOrdinalScale(
  domain: string[],
  range: string[]
) {
  return d3.scaleOrdinal()
    .domain(domain)
    .range(range);
}

// 创建轴
export function createAxis(
  type: 'bottom' | 'left' | 'top' | 'right',
  scale: d3.ScaleLinear<number, number> | d3.ScaleBand<string>,
  tickCount?: number
) {
  let axis;
  
  switch (type) {
    case 'bottom':
      axis = d3.axisBottom(scale as any);
      break;
    case 'left':
      axis = d3.axisLeft(scale as any);
      break;
    case 'top':
      axis = d3.axisTop(scale as any);
      break;
    case 'right':
      axis = d3.axisRight(scale as any);
      break;
  }
  
  if (tickCount && 'ticks' in scale) {
    axis.ticks(tickCount);
  }
  
  return axis;
}

// 动画过渡
export function createTransition(duration: number = 750) {
  return d3.transition()
    .duration(duration)
    .ease(d3.easeQuadInOut);
}

// 清理图表
export function cleanupChart(container: d3.Selection<HTMLDivElement, unknown, null, undefined>) {
  container.selectAll('*').remove();
  d3.select('#chart-tooltip').remove();
}

// 响应式处理
export function handleResize(
  container: HTMLDivElement,
  callback: (width: number, height: number) => void
) {
  const resizeObserver = new ResizeObserver((entries) => {
    for (const entry of entries) {
      const { width, height } = entry.contentRect;
      callback(width, height);
    }
  });
  
  resizeObserver.observe(container);
  
  return () => resizeObserver.disconnect();
}

// 获取文本宽度
export function getTextWidth(text: string, fontSize: number = 12, fontFamily: string = 'Arial'): number {
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) return 0;
  
  context.font = `${fontSize}px ${fontFamily}`;
  return context.measureText(text).width;
}

// 截断文本
export function truncateText(text: string, maxWidth: number, fontSize: number = 12, fontFamily: string = 'Arial'): string {
  const textWidth = getTextWidth(text, fontSize, fontFamily);
  
  if (textWidth <= maxWidth) {
    return text;
  }
  
  const ellipsis = '...';
  const ellipsisWidth = getTextWidth(ellipsis, fontSize, fontFamily);
  
  let truncated = text;
  while (getTextWidth(truncated + ellipsis, fontSize, fontFamily) > maxWidth && truncated.length > 0) {
    truncated = truncated.slice(0, -1);
  }
  
  return truncated + ellipsis;
} 