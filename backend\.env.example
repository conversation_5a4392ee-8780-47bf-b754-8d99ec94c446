# 应用配置
APP_NAME="AI REPORT API"
VERSION="1.0.0"
DEBUG=false
API_PREFIX="/api/v1"
HOST="0.0.0.0"
PORT=8000

# 日志配置
LOG_LEVEL="INFO"
LOG_FORMAT="json"

# CORS配置
CORS_ORIGINS=["*"]
CORS_METHODS=["*"]
CORS_HEADERS=["*"]

# 数据库配置
DB_HOST="localhost"
DB_PORT=3306
DB_USER="root"
DB_PASSWORD="your_password"
DB_DATABASE="travel_data"
DB_CHARSET="utf8mb4"

# RAGFlow配置
RAGFLOW_API_KEY="your_ragflow_api_key"
RAGFLOW_BASE_URL="http://localhost:9380"

# RAGFlow数据集配置
RAGFLOW_DDL_DATASET="text2sql_ddl_kb"
RAGFLOW_Q2SQL_DATASET="text2sql_q2sql_kb"
RAGFLOW_DB_DESC_DATASET="text2sql_db_desc_kb"
RAGFLOW_THESAURUS_DATASET="text2sql_thesaurus_kb"

# RAGFlow Agent配置
RAGFLOW_AGENT_NAME="Text2SQL Agent"
RAGFLOW_AGENT_DESCRIPTION="智能Text2SQL转换Agent"

# RAGFlow检索配置
RAGFLOW_SIMILARITY_THRESHOLD=0.2
RAGFLOW_TOP_K=5
RAGFLOW_TEMPERATURE=0.1

# LLM配置
LLM_API_KEY="your_llm_api_key"
LLM_BASE_URL="https://api.openai.com/v1"
LLM_MODEL_NAME="gpt-3.5-turbo"
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=2000

# 质量评估配置
QUALITY_AUTO_RECORD=true
QUALITY_THRESHOLD=0.8
QUALITY_MAX_DAILY_RECORDS=100
QUALITY_ENABLE_FILTER=true

# 认证配置
AUTH_SECRET_KEY="your-super-secret-jwt-key-change-this-in-production"
AUTH_ACCESS_TOKEN_EXPIRE_MINUTES=30
AUTH_REFRESH_TOKEN_EXPIRE_DAYS=7
AUTH_ALGORITHM="HS256"
AUTH_MIN_PASSWORD_LENGTH=6
AUTH_REQUIRE_SPECIAL_CHARS=false
AUTH_REQUIRE_NUMBERS=false
AUTH_REQUIRE_UPPERCASE=false
