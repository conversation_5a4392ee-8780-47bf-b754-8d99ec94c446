import asyncio
import json
import re
from typing import Dict, List, Any, Optional, <PERSON><PERSON>
from dataclasses import dataclass
from openai import AsyncOpenAI
from sqlalchemy import create_engine, text
import pandas as pd
from config import get_settings

@dataclass
class SQLQueryResult:
    success: bool
    sql: str
    data: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None
    explanation: Optional[str] = None
    chart_suggestions: Optional[List[Dict[str, Any]]] = None

class EnhancedText2SQLService:
    """增强的Text2SQL服务 - 专为报告生成优化"""
    
    def __init__(self, openai_api_key: str, db_connection_string: str):
        settings = get_settings()
        self.client = AsyncOpenAI(
            api_key=settings.llm.api_key,
            base_url=settings.llm.base_url
        )
        self.model_name = settings.llm.model_name
        self.engine = create_engine(db_connection_string)
        self.schema_cache = {}
        self.query_templates = {}
        
    async def initialize(self):
        """初始化服务，加载数据库架构"""
        await self._load_database_schema()
        await self._load_query_templates()
    
    async def natural_language_to_sql(
        self, 
        query: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> SQLQueryResult:
        """
        将自然语言转换为SQL查询
        
        Args:
            query: 自然语言查询
            context: 上下文信息（报告类型、已有数据等）
            
        Returns:
            SQL查询结果
        """
        
        try:
            # 1. 查询意图分析
            intent = await self._analyze_query_intent(query, context)
            
            # 2. 查找相关表和字段
            relevant_schema = await self._find_relevant_schema(query, intent)
            
            # 3. 生成SQL查询
            sql_query = await self._generate_sql_query(query, intent, relevant_schema)
            
            # 4. 验证和优化SQL
            validated_sql = await self._validate_and_optimize_sql(sql_query)
            
            # 5. 执行查询
            data = await self._execute_query(validated_sql)
            
            # 6. 生成说明和图表建议
            explanation = await self._generate_explanation(query, validated_sql, data)
            chart_suggestions = await self._suggest_charts(data, intent)
            
            return SQLQueryResult(
                success=True,
                sql=validated_sql,
                data=data,
                explanation=explanation,
                chart_suggestions=chart_suggestions
            )
            
        except Exception as e:
            return SQLQueryResult(
                success=False,
                sql="",
                error=str(e)
            )
    
    async def _analyze_query_intent(
        self, 
        query: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """分析查询意图"""
        
        system_prompt = """
        你是一个SQL查询意图分析专家。分析用户的自然语言查询，识别其意图和需求。
        
        需要识别的信息包括：
        1. 查询类型：统计(statistics)、对比(comparison)、趋势(trend)、排名(ranking)、明细(detail)
        2. 时间范围：具体的时间条件
        3. 地理范围：涉及的地区或城市
        4. 指标类型：收入、游客数量、增长率等
        5. 聚合方式：求和、平均、计数、最大值、最小值
        6. 分组维度：按时间、地区、类型等分组
        
        请返回JSON格式的结果。
        """
        
        user_prompt = f"""
        用户查询：{query}
        上下文：{json.dumps(context or {}, ensure_ascii=False)}
        
        请分析查询意图。
        """
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content
            if content:
                return json.loads(content)
            return {}
            
        except Exception as e:
            # 降级到基础意图分析
            return self._basic_intent_analysis(query)
    
    def _basic_intent_analysis(self, query: str) -> Dict[str, Any]:
        """基础意图分析"""
        intent = {
            "query_type": "statistics",
            "time_range": None,
            "geo_scope": None,
            "metrics": [],
            "aggregation": "sum",
            "group_by": []
        }
        
        # 简单的关键词匹配
        if any(word in query for word in ["对比", "比较", "vs"]):
            intent["query_type"] = "comparison"
        elif any(word in query for word in ["趋势", "变化", "增长"]):
            intent["query_type"] = "trend"
        elif any(word in query for word in ["排名", "排序", "最高", "最低"]):
            intent["query_type"] = "ranking"
        
        # 提取时间信息
        time_patterns = [
            r"\d{4}年",
            r"\d{1,2}月",
            r"最近.*?[天月年]",
            r"[去今]年",
            r"上[个半].*?[月年]"
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, query)
            if match:
                intent["time_range"] = match.group()
                break
        
        return intent
    
    async def _find_relevant_schema(
        self, 
        query: str, 
        intent: Dict[str, Any]
    ) -> Dict[str, Any]:
        """查找相关的数据库架构"""
        
        # 从缓存的架构中查找相关表
        relevant_tables = []
        
        for table_name, table_info in self.schema_cache.items():
            # 计算表的相关性得分
            relevance_score = self._calculate_table_relevance(
                query, intent, table_name, table_info
            )
            
            if relevance_score > 0.3:  # 设定阈值
                relevant_tables.append({
                    "name": table_name,
                    "info": table_info,
                    "relevance": relevance_score
                })
        
        # 按相关性排序
        relevant_tables.sort(key=lambda x: x["relevance"], reverse=True)
        
        return {
            "tables": relevant_tables[:5],  # 最多返回5个最相关的表
            "total_tables": len(self.schema_cache)
        }
    
    def _calculate_table_relevance(
        self, 
        query: str, 
        intent: Dict[str, Any], 
        table_name: str, 
        table_info: Dict[str, Any]
    ) -> float:
        """计算表的相关性得分"""
        
        score = 0.0
        query_lower = query.lower()
        
        # 表名匹配
        if any(keyword in table_name.lower() for keyword in ["旅游", "tourism", "visit", "revenue"]):
            score += 0.3
        
        # 字段匹配
        for column in table_info.get("columns", []):
            column_name = column.get("name", "").lower()
            if any(keyword in column_name for keyword in ["收入", "revenue", "游客", "visitor", "数量", "amount"]):
                score += 0.2
        
        # 查询类型匹配
        query_type = intent.get("query_type", "")
        if query_type == "trend" and "时间" in str(table_info):
            score += 0.2
        elif query_type == "comparison" and "地区" in str(table_info):
            score += 0.2
        
        return min(score, 1.0)
    
    async def _generate_sql_query(
        self, 
        query: str, 
        intent: Dict[str, Any], 
        schema: Dict[str, Any]
    ) -> str:
        """生成SQL查询"""
        
        # 构建上下文信息
        tables_info = ""
        for table in schema.get("tables", []):
            table_info = table["info"]
            columns = ", ".join([col["name"] + "(" + col["type"] + ")" 
                               for col in table_info.get("columns", [])])
            tables_info += f"表 {table['name']}: {columns}\n"
        
        system_prompt = """
        你是一个专业的SQL查询生成专家。基于用户的自然语言查询、查询意图和数据库架构，生成准确的SQL查询。
        
        请遵循以下原则：
        1. 生成标准的SQL语法
        2. 使用适当的聚合函数和分组
        3. 添加必要的WHERE条件
        4. 优化查询性能
        5. 确保查询结果对报告生成有用
        6. 只返回SQL语句，不要其他解释
        """
        
        user_prompt = f"""
        自然语言查询：{query}
        查询意图：{json.dumps(intent, ensure_ascii=False)}
        
        可用的数据库表和字段：
        {tables_info}
        
        请生成对应的SQL查询。
        """
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
            )
            
            sql = response.choices[0].message.content
            if sql:
                sql = sql.strip()
                # 清理SQL语句
                sql = re.sub(r'^```sql\s*', '', sql)
                sql = re.sub(r'\s*```$', '', sql)
                return sql.strip()
            return ""
            
        except Exception as e:
            raise Exception(f"SQL生成失败: {str(e)}")
    
    async def _validate_and_optimize_sql(self, sql: str) -> str:
        """验证和优化SQL查询"""
        
        # 基本的SQL验证
        if not sql.upper().startswith(('SELECT', 'WITH')):
            raise Exception("只支持SELECT查询")
        
        # 检查危险操作
        dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE']
        sql_upper = sql.upper()
        
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                raise Exception(f"不允许的操作: {keyword}")
        
        # 添加LIMIT以防止大量数据返回
        if 'LIMIT' not in sql_upper:
            sql += ' LIMIT 1000'
        
        return sql
    
    async def _execute_query(self, sql: str) -> List[Dict[str, Any]]:
        """执行SQL查询"""
        
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text(sql))
                columns = result.keys()
                rows = result.fetchall()
                
                # 转换为字典列表
                data = []
                for row in rows:
                    row_dict = {}
                    for i, column in enumerate(columns):
                        value = row[i]
                        # 处理特殊数据类型
                        if hasattr(value, 'isoformat'):  # datetime
                            value = value.isoformat()
                        elif isinstance(value, bytes):
                            value = value.decode('utf-8')
                        row_dict[column] = value
                    data.append(row_dict)
                
                return data
                
        except Exception as e:
            raise Exception(f"查询执行失败: {str(e)}")
    
    async def _generate_explanation(
        self, 
        query: str, 
        sql: str, 
        data: List[Dict[str, Any]]
    ) -> str:
        """生成查询说明"""
        
        data_summary = f"查询返回了 {len(data)} 条记录"
        if data:
            data_summary += f"，包含字段：{', '.join(data[0].keys())}"
        
        system_prompt = """
        为用户解释SQL查询的结果。用简洁的中文说明：
        1. 查询做了什么
        2. 返回了什么数据
        3. 数据的主要特点
        """
        
        user_prompt = f"""
        用户查询：{query}
        执行的SQL：{sql}
        数据概要：{data_summary}
        
        请生成简洁的说明。
        """
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
            )
            
            return response.choices[0].message.content or ""
            
        except Exception as e:
            return f"查询完成：{data_summary}"
    
    async def _suggest_charts(
        self, 
        data: List[Dict[str, Any]], 
        intent: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """建议合适的图表类型"""
        
        if not data:
            return []
        
        suggestions = []
        
        # 分析数据结构
        columns = list(data[0].keys())
        numeric_columns = []
        date_columns = []
        categorical_columns = []
        
        for col in columns:
            sample_value = data[0][col]
            if isinstance(sample_value, (int, float)):
                numeric_columns.append(col)
            elif 'date' in col.lower() or 'time' in col.lower():
                date_columns.append(col)
            else:
                categorical_columns.append(col)
        
        # 根据数据特征推荐图表
        query_type = intent.get("query_type", "statistics")
        
        if query_type == "trend" and date_columns and numeric_columns:
            suggestions.append({
                "type": "line",
                "title": "趋势图",
                "x_axis": date_columns[0],
                "y_axis": numeric_columns[0],
                "description": "显示数据随时间的变化趋势"
            })
        
        if query_type == "comparison" and categorical_columns and numeric_columns:
            suggestions.append({
                "type": "bar",
                "title": "对比图",
                "x_axis": categorical_columns[0],
                "y_axis": numeric_columns[0],
                "description": "对比不同类别的数据"
            })
        
        if len(numeric_columns) >= 2:
            suggestions.append({
                "type": "scatter",
                "title": "散点图",
                "x_axis": numeric_columns[0],
                "y_axis": numeric_columns[1],
                "description": "显示两个数值变量的关系"
            })
        
        if categorical_columns and len(set(row[categorical_columns[0]] for row in data)) <= 10:
            suggestions.append({
                "type": "pie",
                "title": "饼图",
                "category": categorical_columns[0],
                "value": numeric_columns[0] if numeric_columns else None,
                "description": "显示各部分的占比关系"
            })
        
        return suggestions
    
    async def _load_database_schema(self):
        """加载数据库架构信息"""
        
        try:
            with self.engine.connect() as connection:
                # 获取所有表名
                tables_query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                """
                
                result = connection.execute(text(tables_query))
                table_names = [row[0] for row in result.fetchall()]
                
                # 获取每个表的字段信息
                for table_name in table_names:
                    columns_query = """
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns
                    WHERE table_name = :table_name
                    ORDER BY ordinal_position
                    """
                    
                    result = connection.execute(text(columns_query), {"table_name": table_name})
                    columns = []
                    
                    for row in result.fetchall():
                        columns.append({
                            "name": row[0],
                            "type": row[1],
                            "nullable": row[2] == 'YES'
                        })
                    
                    self.schema_cache[table_name] = {
                        "columns": columns,
                        "row_count": await self._get_table_row_count(table_name)
                    }
                    
        except Exception as e:
            print(f"加载数据库架构失败: {str(e)}")
    
    async def _get_table_row_count(self, table_name: str) -> int:
        """获取表的行数"""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                row = result.fetchone()
                return row[0] if row else 0
        except:
            return 0
    
    async def _load_query_templates(self):
        """加载常用查询模板"""
        
        self.query_templates = {
            "tourism_monthly_stats": {
                "description": "月度旅游统计",
                "template": """
                SELECT 
                    strftime('%Y-%m', event_time) as month,
                    COUNT(DISTINCT visitor_id) as total_visitors,
                    SUM(total_spent) as total_revenue
                FROM 
                    tourism_events
                WHERE 
                    event_time >= :start_date AND event_time < :end_date
                GROUP BY 
                    month
                ORDER BY 
                    month;
                """
            }
        }

    async def get_available_templates(self) -> List[Dict[str, str]]:
        """获取可用的查询模板列表"""
        return [
            {"name": name, "description": info["description"]}
            for name, info in self.query_templates.items()
        ]

    async def use_template(
        self, 
        template_name: str, 
        parameters: Dict[str, Any]
    ) -> SQLQueryResult:
        """使用查询模板"""
        
        if template_name not in self.query_templates:
            return SQLQueryResult(success=False, sql="", error="模板不存在")
        
        template = self.query_templates[template_name]["template"]
        
        try:
            # 简单的参数替换
            sql_query = template
            for key, value in parameters.items():
                sql_query = sql_query.replace(f":{key}", f"'{value}'")
            
            data = await self._execute_query(sql_query)
            
            return SQLQueryResult(
                success=True,
                sql=sql_query,
                data=data
            )
            
        except Exception as e:
            return SQLQueryResult(success=False, sql=template, error=str(e))