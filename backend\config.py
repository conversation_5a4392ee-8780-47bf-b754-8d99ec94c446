
"""
应用配置管理
"""

from pathlib import Path
from functools import lru_cache
from typing import Optional, List

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    """数据库配置"""

    model_config = SettingsConfigDict(
        env_prefix="DB_",
        env_file=str(Path(__file__).parent / ".env"),
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    host: str = Field(default="localhost", description="数据库主机")
    port: int = Field(default=3306, description="数据库端口")
    user: str = Field(default="root", description="数据库用户名")
    password: str = Field(default="", description="数据库密码")
    database: str = Field(default="travel_data", description="数据库名称")
    charset: str = Field(default="utf8mb4", description="字符集")
    
    @property
    def connection_string(self) -> str:
        """获取数据库连接字符串"""
        return f"mysql+pymysql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"


class RAGFlowSettings(BaseSettings):
    """RAGFlow配置 - 仅知识库功能"""

    model_config = SettingsConfigDict(
        env_prefix="RAGFLOW_",
        env_file=str(Path(__file__).parent / ".env"),
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    api_key: str = Field(default="",description="RAGFlow API密钥")
    base_url: str = Field(default="http://localhost:9380", description="RAGFlow服务地址")
    
    # 数据集配置
    ddl_dataset: str = Field(default="text2sql_ddl_kb", description="DDL知识库名称")
    q2sql_dataset: str = Field(default="text2sql_q2sql_kb", description="Q->SQL知识库名称")
    db_desc_dataset: str = Field(default="text2sql_db_desc_kb", description="数据库描述知识库名称")
    thesaurus_dataset: str = Field(default="text2sql_thesaurus_kb", description="同义词知识库名称")
    
    # 检索配置
    similarity_threshold: float = Field(default=0.2, description="相似度阈值")
    top_k: int = Field(default=5, description="检索数量")
    temperature: float = Field(default=0.1, description="生成温度")


class LLMSettings(BaseSettings):
    """LLM配置"""

    model_config = SettingsConfigDict(
        env_prefix="LLM_",
        env_file=str(Path(__file__).parent / ".env"),
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    api_key: str = Field(default="",description="LLM API密钥")
    base_url: str = Field(default="https://api.openai.com/v1", description="LLM API地址")
    model_name: str = Field(default="gpt-3.5-turbo", description="模型名称")
    temperature: float = Field(default=0.1, description="生成温度")
    max_tokens: int = Field(default=2000, description="最大token数")


class QualitySettings(BaseSettings):
    """质量评估配置"""

    model_config = SettingsConfigDict(
        env_prefix="QUALITY_",
        env_file=str(Path(__file__).parent / ".env"),
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )

    auto_record: bool = Field(default=True, description="是否自动记录")
    threshold: float = Field(default=0.8, description="质量阈值")
    max_daily_records: int = Field(default=100, description="每日最大记录数")
    enable_filter: bool = Field(default=True, description="是否启用质量过滤")


class AuthSettings(BaseSettings):
    """认证配置"""

    model_config = SettingsConfigDict(
        env_prefix="AUTH_",
        env_file=str(Path(__file__).parent / ".env"),
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )

    secret_key: str = Field(
        default="your-secret-key-change-this-in-production",
        description="JWT密钥"
    )
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间(分钟)")
    refresh_token_expire_days: int = Field(default=7, description="刷新令牌过期时间(天)")
    algorithm: str = Field(default="HS256", description="JWT算法")

    # 密码策略
    min_password_length: int = Field(default=6, description="最小密码长度")
    require_special_chars: bool = Field(default=False, description="是否要求特殊字符")
    require_numbers: bool = Field(default=False, description="是否要求数字")
    require_uppercase: bool = Field(default=False, description="是否要求大写字母")


class AppSettings(BaseSettings):
    """应用主配置"""
    
    model_config = SettingsConfigDict(
        env_file=str(Path(__file__).parent / ".env"),
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # 应用基础配置
    app_name: str = Field(default="RAGFlow Text2SQL API", description="应用名称")
    version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    
    # API配置
    api_prefix: str = Field(default="/api/v1", description="API前缀")
    host: str = Field(default="0.0.0.0", description="服务主机")
    port: int = Field(default=8000, description="服务端口")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式")
    
    # CORS配置
    cors_origins: List[str] = Field(default=["*"], description="CORS允许的源")
    cors_methods: List[str] = Field(default=["*"], description="CORS允许的方法")
    cors_headers: List[str] = Field(default=["*"], description="CORS允许的头部")
    
    # 子配置
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    ragflow: RAGFlowSettings = RAGFlowSettings()
    llm: LLMSettings = LLMSettings()
    quality: QualitySettings = Field(default_factory=QualitySettings)
    auth: AuthSettings = Field(default_factory=AuthSettings)

    # 认证相关的便捷属性
    @property
    def auth_secret_key(self) -> str:
        """获取认证密钥"""
        return self.auth.secret_key

    @property
    def auth_access_token_expire_minutes(self) -> int:
        """获取访问令牌过期时间"""
        return self.auth.access_token_expire_minutes


@lru_cache()
def get_settings() -> AppSettings:
    """获取应用配置（单例模式）"""
    return AppSettings()


# 全局配置实例
settings = get_settings()
