@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式重置 */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-cyber-light text-cyber-text;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
  
  * {
    @apply border-cyber-border;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply text-cyber-text font-semibold;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-cyber-muted;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-cyber-secondary rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-cyber-primary;
  }
}

/* 组件样式 */
@layer components {
  .neon-text {
    @apply text-cyber-primary font-medium;
    text-shadow: 0 0 10px rgba(37, 99, 235, 0.3);
  }
  
  .cyber-card {
    @apply bg-white/80 backdrop-blur-sm border border-cyber-border rounded-lg shadow-sm;
    transition: all 0.3s ease;
  }
  
  .cyber-card:hover {
    @apply shadow-md border-cyber-accent/50;
    transform: translateY(-2px);
  }
  
  .cyber-button {
    @apply bg-cyber-primary text-white px-4 py-2 rounded-md font-medium;
    @apply hover:bg-cyber-accent transition-colors duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-cyber-primary focus:ring-offset-2;
  }
  
  .cyber-button-secondary {
    @apply bg-white text-cyber-text border border-cyber-border px-4 py-2 rounded-md font-medium;
    @apply hover:bg-cyber-muted transition-colors duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-cyber-primary focus:ring-offset-2;
  }
  
  .cyber-input {
    @apply w-full px-3 py-2 border border-cyber-border rounded-md;
    @apply focus:outline-none focus:ring-2 focus:ring-cyber-primary focus:border-cyber-primary;
    @apply bg-white/80 backdrop-blur-sm;
  }
  
  .cyber-grid {
    background-image: 
      linear-gradient(rgba(37, 99, 235, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(37, 99, 235, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .light-rays {
    background: radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.15) 0%, transparent 50%);
  }
}

/* 工具类 */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .glass-effect {
    @apply bg-white/20 backdrop-blur-md border border-white/30;
  }
  
  .fade-in {
    animation: fade-in 0.5s ease-out;
  }
  
  .slide-up {
    animation: slide-up 0.3s ease-out;
  }
}

/* 动画关键帧 */
@keyframes gentle-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slide-up {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0px);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cyber-card {
    @apply mx-2;
  }
  
  .cyber-button {
    @apply text-sm px-3 py-1.5;
  }
}

/* 打印样式 */
@media print {
  .cyber-grid,
  .light-rays {
    display: none !important;
  }
  
  .cyber-card {
    @apply shadow-none border-gray-300;
  }
} 