"use client";

import React, { useState } from 'react';
import { 
  <PERSON>Bar<PERSON>hart, 
  SimplePieChart, 
  ComboChart, 
  NestedPieChart,
  barChartExampleData,
  pieChartExampleData,
  comboChartExampleData,
  CHART_LIBRARY_INFO 
} from '@/components/chart';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// 使用导入的组合图表数据

// 嵌套饼图示例数据
const nestedPieData = {
  outer: [
    { name: "华东", value: 45, color: "#4A90E2" },
    { name: "华北", value: 30, color: "#7ED321" },
    { name: "华南", value: 25, color: "#F5A623" }
  ],
  inner: [
    { name: "浙江", value: 25, color: "#5BA0F2", parent: "华东" },
    { name: "江苏", value: 20, color: "#6BB0F2", parent: "华东" },
    { name: "北京", value: 18, color: "#8EE331", parent: "华北" },
    { name: "天津", value: 12, color: "#9EE341", parent: "华北" },
    { name: "广东", value: 15, color: "#F7A633", parent: "华南" },
    { name: "广西", value: 10, color: "#F8B643", parent: "华南" }
  ]
};

export default function ChartShowcasePage() {
  const [activeTab, setActiveTab] = useState('overview');

  const exportSVG = (chartId: string) => {
    const svgElement = document.querySelector(`#${chartId} svg`) as SVGSVGElement;
    if (svgElement) {
      const serializer = new XMLSerializer();
      const svgString = serializer.serializeToString(svgElement);
      const blob = new Blob([svgString], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${chartId}.svg`;
      link.click();
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {CHART_LIBRARY_INFO.name}
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            {CHART_LIBRARY_INFO.description}
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            {CHART_LIBRARY_INFO.features.map((feature, index) => (
              <span 
                key={index}
                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>

        {/* 图表展示区域 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5 mb-8">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="bar">柱状图</TabsTrigger>
            <TabsTrigger value="pie">饼图</TabsTrigger>
            <TabsTrigger value="combo">组合图</TabsTrigger>
            <TabsTrigger value="nested">嵌套饼图</TabsTrigger>
          </TabsList>

          {/* 概览标签页 */}
          <TabsContent value="overview" className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex justify-between items-center">
                    单一柱状图
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => exportSVG('overview-bar-chart')}
                    >
                      导出SVG
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div id="overview-bar-chart">
                    <SimpleBarChart
                      data={barChartExampleData}
                      width={500}
                      height={300}
                      title="产品销售数据"
                      barColor="#4A90E2"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex justify-between items-center">
                    单一饼图
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => exportSVG('overview-pie-chart')}
                    >
                      导出SVG
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div id="overview-pie-chart">
                    <SimplePieChart
                      data={pieChartExampleData}
                      width={500}
                      height={300}
                      title="市场份额"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex justify-between items-center">
                    组合图表
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => exportSVG('overview-combo-chart')}
                    >
                      导出SVG
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div id="overview-combo-chart">
                    <ComboChart
                      data={comboChartExampleData}
                      width={500}
                      height={300}
                      title="城市旅游数据"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex justify-between items-center">
                    嵌套饼图
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => exportSVG('overview-nested-chart')}
                    >
                      导出SVG
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div id="overview-nested-chart">
                    <NestedPieChart
                      data={nestedPieData}
                      width={500}
                      height={300}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 柱状图标签页 */}
          <TabsContent value="bar" className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>垂直柱状图</CardTitle>
                </CardHeader>
                <CardContent>
                  <SimpleBarChart
                    data={barChartExampleData}
                    width={600}
                    height={400}
                    title="产品销售数据"
                    barColor="#4A90E2"
                    xAxisLabel="产品"
                    yAxisLabel="销售量"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>水平柱状图</CardTitle>
                </CardHeader>
                <CardContent>
                  <SimpleBarChart
                    data={barChartExampleData}
                    width={600}
                    height={400}
                    title="产品销售数据（水平）"
                    orientation="horizontal"
                    barColor="#7ED321"
                    xAxisLabel="销售量"
                    yAxisLabel="产品"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>多色柱状图</CardTitle>
                </CardHeader>
                <CardContent>
                  <SimpleBarChart
                    data={barChartExampleData}
                    width={600}
                    height={400}
                    title="产品销售数据（多色）"
                    colors={['#4A90E2', '#7ED321', '#F5A623', '#D0021B', '#9013FE', '#50E3C2']}
                    xAxisLabel="产品"
                    yAxisLabel="销售量"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>无动画柱状图</CardTitle>
                </CardHeader>
                <CardContent>
                  <SimpleBarChart
                    data={barChartExampleData}
                    width={600}
                    height={400}
                    title="产品销售数据（无动画）"
                    barColor="#F5A623"
                    animate={false}
                    xAxisLabel="产品"
                    yAxisLabel="销售量"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 饼图标签页 */}
          <TabsContent value="pie" className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>标准饼图</CardTitle>
                </CardHeader>
                <CardContent>
                  <SimplePieChart
                    data={pieChartExampleData}
                    width={600}
                    height={400}
                    title="产品市场份额"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>环形图</CardTitle>
                </CardHeader>
                <CardContent>
                  <SimplePieChart
                    data={pieChartExampleData}
                    width={600}
                    height={400}
                    title="产品市场份额（环形）"
                    innerRadius={80}
                    labelPosition="inside"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>无图例饼图</CardTitle>
                </CardHeader>
                <CardContent>
                  <SimplePieChart
                    data={pieChartExampleData}
                    width={600}
                    height={400}
                    title="产品市场份额（无图例）"
                    showLegend={false}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>无动画饼图</CardTitle>
                </CardHeader>
                <CardContent>
                  <SimplePieChart
                    data={pieChartExampleData}
                    width={600}
                    height={400}
                    title="产品市场份额（无动画）"
                    animate={false}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 组合图标签页 */}
          <TabsContent value="combo" className="space-y-8">
            <div className="grid grid-cols-1 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>组合图表（柱状图+折线图+双Y轴）</CardTitle>
                </CardHeader>
                <CardContent>
                                  <ComboChart
                  data={comboChartExampleData}
                  width={1000}
                  height={500}
                  title="浙江省各城市旅游数据分析"
                  barColor="#4A90E2"
                  lineColor="#7ED321"
                />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 嵌套饼图标签页 */}
          <TabsContent value="nested" className="space-y-8">
            <div className="grid grid-cols-1 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>嵌套饼图（子母饼图）</CardTitle>
                </CardHeader>
                <CardContent>
                  <NestedPieChart
                    data={nestedPieData}
                    width={800}
                    height={600}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* 技术说明 */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>技术特性</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <h3 className="font-semibold mb-2">SVG原生输出</h3>
                <p className="text-sm text-gray-600">
                  所有图表均基于SVG渲染，支持无损缩放和完美的矢量输出，非常适合用于文档和报告生成。
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">完全自定义</h3>
                <p className="text-sm text-gray-600">
                  基于D3.js的底层控制能力，可以实现任何复杂的图表样式和交互效果。
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">响应式设计</h3>
                <p className="text-sm text-gray-600">
                  支持响应式布局，自动适应不同屏幕尺寸，提供最佳的用户体验。
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">动画效果</h3>
                <p className="text-sm text-gray-600">
                  内置平滑的动画过渡效果，提升用户体验和视觉吸引力。
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">交互功能</h3>
                <p className="text-sm text-gray-600">
                  支持鼠标悬停、点击等交互操作，提供丰富的用户交互体验。
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">TypeScript支持</h3>
                <p className="text-sm text-gray-600">
                  完整的TypeScript类型定义，提供更好的开发体验和代码安全性。
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 