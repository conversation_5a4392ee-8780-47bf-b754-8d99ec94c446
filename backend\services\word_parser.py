import re
from typing import Dict, Any, List
from docx import Document
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.table import Table, _Cell
from docx.text.paragraph import Paragraph

class WordToMarkdownParser:
    """
    新版解析器：
    将 Word 文档解析为纯净的 Markdown 和结构化的数据（仅表格）。
    """

    def parse_document(self, file_path: str) -> Dict[str, Any]:
        """
        解析.docx文件，分离文本和表格数据。

        Args:
            file_path: Word 文件的路径。

        Returns:
            一个字典，包含:
            - "markdown_content": 包含表格占位符的Markdown文本。
            - "tables": 结构化表格数据的列表。
        """
        doc = Document(file_path)
        markdown_parts = []
        tables = []
        table_counter = 1

        # doc.element.body 包含文档的所有顶级块元素（段落和表格）
        for element in doc.element.body:
            if isinstance(element, CT_P):
                paragraph = Paragraph(element, doc)
                # 处理段落和标题
                style_name = ""
                if paragraph.style and paragraph.style.name:
                    style_name = paragraph.style.name

                if style_name.startswith('Heading'):
                    try:
                        match = re.search(r'\d+$', style_name)
                        if match:
                            level = int(match.group())
                            markdown_parts.append(f"{'#' * level} {paragraph.text}")
                        else:
                            markdown_parts.append(f"### {paragraph.text}") # 默认
                    except (ValueError, AttributeError):
                        markdown_parts.append(f"### {paragraph.text}")
                elif paragraph.text.strip():
                    markdown_parts.append(paragraph.text)

            elif isinstance(element, CT_Tbl):
                # 处理表格
                table_id = f"table-{table_counter}"
                table_obj = Table(element, doc)
                table_data = self._table_to_structured_data(table_obj)
                table_data["id"] = table_id
                
                tables.append(table_data)
                
                # 在Markdown中插入占位符
                markdown_parts.append(f"\n[TABLE:{table_id}]\n")
                table_counter += 1

        # 将所有部分连接成最终的Markdown字符串
        full_markdown = "\n\n".join(filter(None, markdown_parts))
        
        return {
            "markdown_content": full_markdown,
            "tables": tables,
        }

    def _table_to_structured_data(self, table: Table) -> Dict[str, Any]:
        """
        将 docx 表格对象转换为结构化的字典。
        """
        if not table.rows:
            return {"title": "", "headers": [], "rows": []}

        headers = [cell.text.strip() for cell in table.rows[0].cells]
        rows = []
        if len(table.rows) > 1:
            for row in table.rows[1:]:
                row_data = [cell.text.strip() for cell in row.cells]
                # 确保行数据长度与表头一致
                if len(row_data) == len(headers):
                    rows.append(row_data)

        # 尝试从表格前的段落中找到标题
        title = self._find_title_for_table(table)

        return {
            "title": title,
            "headers": headers,
            "rows": rows
        }

    def _find_title_for_table(self, table: Table) -> str:
        """
        尝试通过查找表格前的段落来确定表格标题。
        通常表格标题是紧邻表格上方的那个段落，以“表X”开头。
        """
        previous_element = table._element.getprevious()
        if previous_element is not None and isinstance(previous_element, CT_P):
            paragraph = Paragraph(previous_element, table._parent)
            text = paragraph.text.strip()
            # 检查文本是否符合标题模式 (例如, "表1 ...")
            if re.match(r'^表\s*\d+', text):
                return text
        return ""
