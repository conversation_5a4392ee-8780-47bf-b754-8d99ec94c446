import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, context } = body

    if (!message) {
      return NextResponse.json({ error: '消息内容不能为空' }, { status: 400 })
    }

    // 转发到后端API
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000'
    const response = await fetch(`${backendUrl}/api/ai-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message, context }),
    })

    if (!response.ok) {
      throw new Error(`后端请求失败: ${response.status}`)
    }

    const result = await response.json()
    return NextResponse.json(result)

  } catch (error) {
    console.error('AI聊天请求失败:', error)
    return NextResponse.json({ 
      error: 'AI服务暂时不可用，请稍后重试',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
} 