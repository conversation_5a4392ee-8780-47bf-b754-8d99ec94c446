"""
RAGFlow客户端管理 - 仅使用知识库功能
"""

import time
from typing import Any, Dict, List, Optional

from ragflow_sdk import RAGFlow

from config import settings
from core.exceptions import RAGFlowException
from core.logging import get_logger

logger = get_logger(__name__)


class RAGFlowClient:
    """RAGFlow客户端封装 - 仅使用知识库功能"""
    
    def __init__(self):
        self._client: Optional[RAGFlow] = None
        self._datasets: Dict[str, Any] = {}
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """初始化RAGFlow客户端"""
        try:
            self._client = RAGFlow(
                api_key=settings.ragflow.api_key,
                base_url=settings.ragflow.base_url
            )
            
            logger.info(
                "RAGFlow客户端初始化成功",
                base_url=settings.ragflow.base_url
            )
            
            # 初始化数据集
            self._initialize_datasets()
            
        except Exception as e:
            logger.warning("RAGFlow客户端初始化部分失败", error=str(e))
            # 不抛出异常，允许系统继续运行
    
    def _initialize_datasets(self) -> None:
        """初始化数据集"""
        dataset_configs = {
            'ddl': settings.ragflow.ddl_dataset,
            'q2sql': settings.ragflow.q2sql_dataset,
            'db_desc': settings.ragflow.db_desc_dataset,
            'thesaurus': settings.ragflow.thesaurus_dataset
        }
        
        for key, dataset_name in dataset_configs.items():
            try:
                # 尝试获取现有数据集
                existing_datasets = self._client.list_datasets(name=dataset_name)
                
                if existing_datasets:
                    self._datasets[key] = existing_datasets[0]
                    logger.info(f"找到现有数据集: {dataset_name}")
                else:
                    # 创建新数据集
                    dataset = self._client.create_dataset(
                        name=dataset_name,
                        description=f"Text2SQL {key.upper()} 知识库",
                        chunk_method="manual"
                    )
                    self._datasets[key] = dataset
                    logger.info(f"创建新数据集: {dataset_name}")
                    
            except Exception as e:
                logger.warning(f"初始化数据集失败 {dataset_name}: {e}")
                # 权限不足时不抛出异常，继续运行
                self._datasets[key] = None
    
    def retrieve_similar_examples(
        self, 
        question: str, 
        dataset_type: str, 
        top_k: int = 3
    ) -> List[str]:
        """使用RAGFlow检索相似示例"""
        try:
            if dataset_type not in self._datasets:
                logger.warning(f"数据集类型 {dataset_type} 不存在")
                return []

            dataset = self._datasets[dataset_type]
            if dataset is None:
                logger.warning(f"数据集 {dataset_type} 未初始化")
                return []
            
            # 使用RAGFlow的检索功能
            chunks = self._client.retrieve(
                question=question,
                dataset_ids=[dataset.id],
                similarity_threshold=settings.ragflow.similarity_threshold,
                top_k=top_k
            )
            
            examples = []
            for chunk in chunks:
                examples.append(chunk.content)
            
            logger.debug(
                "RAGFlow检索完成",
                dataset_type=dataset_type,
                question=question[:50],
                result_count=len(examples)
            )
            
            return examples
            
        except Exception as e:
            logger.error(f"RAGFlow检索失败: {e}")
            return []
    
    def add_q2sql_example(
        self, 
        question: str, 
        sql: str, 
        quality_score: float = 1.0
    ) -> None:
        """添加新的Q->SQL示例到RAGFlow数据集"""
        try:
            if quality_score < settings.quality.threshold:
                logger.info(f"质量分数过低，跳过记录: {quality_score}")
                return
            
            if 'q2sql' not in self._datasets:
                logger.warning("Q->SQL数据集不存在")
                return

            dataset = self._datasets['q2sql']
            if dataset is None:
                logger.warning("Q->SQL数据集未初始化")
                return
            
            # 构建新的Q->SQL示例内容
            from datetime import datetime
            example_content = f"""
### 问题：{question}
SQL：{sql}
质量分数：{quality_score:.2f}
记录时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # 获取现有文档
            docs = dataset.list_documents(keywords="q2sql_examples")
            if docs:
                # 如果存在文档，添加新的chunk
                doc = docs[0]
                doc.add_chunk(content=example_content)
                logger.info(f"添加新Q->SQL示例到现有文档: {question[:30]}...")
            else:
                # 如果不存在文档，创建新文档
                documents = [{
                    'display_name': f'q2sql_example_{int(time.time())}.txt',
                    'blob': example_content.encode('utf-8')
                }]
                dataset.upload_documents(documents)
                logger.info(f"创建新Q->SQL示例文档: {question[:30]}...")
                
        except Exception as e:
            logger.error(f"添加Q->SQL示例失败: {e}")
    
    def test_connection(self) -> bool:
        """测试RAGFlow连接"""
        try:
            datasets = self._client.list_datasets()
            logger.info("RAGFlow连接测试成功", dataset_count=len(datasets))
            return True
        except Exception as e:
            logger.error("RAGFlow连接测试失败", error=str(e))
            return False

    def get_available_datasets(self) -> Dict[str, Any]:
        """获取可用的数据集"""
        return {k: v for k, v in self._datasets.items() if v is not None}


# 全局RAGFlow客户端实例
ragflow_client = RAGFlowClient()
