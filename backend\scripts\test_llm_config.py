#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""测试LLM配置加载功能"""

import sys
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent))

from config import LLMSettings

def test_llm_config():
    """测试LLM配置加载"""
    print("🧪 测试LLM配置加载")
    print("=" * 50)
    
    try:
        # 加载配置
        settings = LLMSettings()
        
        print(f"📋 配置信息:")
        print(f"  API密钥: {'已配置' if settings.api_key else '未配置'}")
        print(f"  API地址: {settings.base_url}")
        print(f"  模型名称: {settings.model_name}")
        print(f"  温度: {settings.temperature}")
        print(f"  最大令牌: {settings.max_tokens}")
        
        # 检查配置状态
        if settings.api_key:
            print("✅ LLM配置完整 - AI功能可用")
        else:
            print("⚠️ 未配置API密钥 - 将使用预设优化策略")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_ai_optimizer_with_config():
    """测试AI优化器配置使用"""
    print("\n🤖 测试AI优化器配置")
    print("=" * 50)
    
    try:
        from services.ai_chart_optimizer import AIChartOptimizer
        
        # 创建优化器实例
        optimizer = AIChartOptimizer()
        
        print(f"🔧 优化器配置状态:")
        print(f"  有API密钥: {optimizer.has_api_key}")
        print(f"  客户端状态: {'已初始化' if optimizer.client else '未初始化'}")
        
        # 测试配置使用
        if optimizer.has_api_key:
            print(f"  模型名称: {optimizer.llm_settings.model_name}")
            print(f"  API地址: {optimizer.llm_settings.base_url}")
            print("✅ AI优化功能可用")
        else:
            print("🔄 将使用智能预设优化策略")
        
        return True
        
    except Exception as e:
        print(f"❌ AI优化器测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 LLM配置测试")
    print("=" * 60)
    
    # 测试配置加载
    config_test = test_llm_config()
    
    # 测试AI优化器
    optimizer_test = test_ai_optimizer_with_config()
    
    print("\n📋 测试结果")
    print("=" * 50)
    print(f"配置加载: {'✅ 通过' if config_test else '❌ 失败'}")
    print(f"AI优化器: {'✅ 通过' if optimizer_test else '❌ 失败'}")
    
    if config_test and optimizer_test:
        print("\n🎉 所有测试通过! 系统可以正常工作")
        print("\n💡 使用提示:")
        print("  - 如果有API密钥：将获得AI优化建议")
        print("  - 如果无API密钥：将使用智能预设策略")
        print("  - 可在 .env 文件中配置 LLM_API_KEY 等参数")
    else:
        print("\n❌ 测试失败，请检查配置") 