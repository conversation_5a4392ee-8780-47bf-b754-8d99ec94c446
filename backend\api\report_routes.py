from fastapi import APIRouter, HTTPException, UploadFile, File, Depends
from fastapi.responses import FileResponse, JSONResponse
from typing import List, Dict, Any, Optional
import json
import logging
import os
from pathlib import Path
import time
from datetime import datetime

from services.word_parser import WordToMarkdownParser
from services.ai_chat_service import AIChatService
from services.enhanced_text2sql import EnhancedText2SQLService, SQLQueryResult
from database.connection import get_db
from config import settings

router = APIRouter(prefix="/api", tags=["reports"])
logger = logging.getLogger(__name__)

# 初始化服务
word_parser = WordToMarkdownParser()
ai_chat_service = AIChatService()
text2sql_service = EnhancedText2SQLService(
    openai_api_key=settings.llm.api_key,
    db_connection_string=settings.database.connection_string
)

@router.post("/word-parser/upload")
async def upload_word_template(file: UploadFile = File(...)):
    """上传并解析Word模板"""
    try:
        # 验证文件类型
        if not file.filename or not file.filename.endswith(('.docx', '.doc')):
            raise HTTPException(status_code=400, detail="仅支持.docx和.doc格式文件")
        
        # 创建临时目录
        tmp_dir = Path("/tmp/")
        tmp_dir.mkdir(exist_ok=True)
        
        # 保存上传的文件
        file_path = tmp_dir / file.filename
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 解析Word文档为Markdown
        markdown_content = word_parser.convert_to_markdown(str(file_path))
        
        # 由于 WordToMarkdownParser 中没有 parse_word_template 和 generate_editable_template
        # 我们将简化此处的逻辑，直接返回 Markdown 内容
        # 模拟旧接口的返回结构
        parsed_data = {
            "markdown_content": markdown_content,
            "template_metadata": {"source": file.filename}
        }
        editable_template = {"type": "basic", "content": markdown_content}

        return {
            "success": True,
            "template": editable_template,
            "content": parsed_data["markdown_content"],
            "metadata": parsed_data["template_metadata"]
        }
        
    except Exception as e:
        logger.error(f"Word模板解析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"模板解析失败: {str(e)}")

@router.post("/ai-chat")
async def chat_with_ai(request: Dict[str, Any]):
    """AI对话接口"""
    try:
        message = request.get("message")
        context = request.get("context", {})
        
        if not message:
            raise HTTPException(status_code=400, detail="消息内容不能为空")
        
        # 处理AI对话
        response = await ai_chat_service.process_user_message(message, context)
        
        return {
            "success": True,
            "message": response.get("message", ""),
            "type": response.get("type", ""),
            "suggestion": response.get("suggestion"),
            "action": response.get("action")
        }
        
    except Exception as e:
        logger.error(f"AI对话处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"AI对话失败: {str(e)}")

@router.get("/data-sources")
async def get_data_sources():
    """获取可用数据源"""
    try:
        # 这里应该从数据库获取实际的表信息
        # 暂时返回模拟数据
        data_sources = [
            {
                "id": "tourism_data",
                "name": "旅游数据表",
                "type": "table",
                "description": "包含旅游相关的各项指标数据",
                "columns": [
                    {"name": "date", "type": "DATE", "description": "日期"},
                    {"name": "visitors", "type": "INTEGER", "description": "游客数量"},
                    {"name": "revenue", "type": "DECIMAL", "description": "旅游收入"},
                    {"name": "region", "type": "VARCHAR", "description": "地区"},
                    {"name": "type", "type": "VARCHAR", "description": "旅游类型"}
                ],
                "rowCount": 15420
            },
            {
                "id": "monthly_stats",
                "name": "月度统计",
                "type": "view",
                "description": "按月汇总的统计数据",
                "columns": [
                    {"name": "month", "type": "VARCHAR", "description": "月份"},
                    {"name": "total_visitors", "type": "INTEGER", "description": "总游客数"},
                    {"name": "total_revenue", "type": "DECIMAL", "description": "总收入"},
                    {"name": "growth_rate", "type": "DECIMAL", "description": "增长率"}
                ],
                "rowCount": 12
            },
            {
                "id": "regional_data",
                "name": "地区数据",
                "type": "table",
                "description": "各地区的基础信息和统计数据",
                "columns": [
                    {"name": "region", "type": "VARCHAR", "description": "地区名称"},
                    {"name": "population", "type": "INTEGER", "description": "人口数量"},
                    {"name": "area", "type": "DECIMAL", "description": "面积"},
                    {"name": "gdp", "type": "DECIMAL", "description": "GDP"}
                ],
                "rowCount": 89
            }
        ]
        
        return {"success": True, "data": data_sources}
        
    except Exception as e:
        logger.error(f"获取数据源失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据源失败: {str(e)}")

@router.post("/sql-query")
async def execute_sql_query(request: Dict[str, Any]):
    """执行SQL查询"""
    try:
        sql = request.get("query")
        template_context = request.get("template_context")
        
        if not sql:
            raise HTTPException(status_code=400, detail="SQL查询不能为空")
        
        # 这里应该连接实际数据库执行查询
        # 暂时返回模拟结果
        mock_result = {
            "columns": ["date", "visitors", "revenue", "region"],
            "rows": [
                ["2024-01-01", 1000, 50000.0, "杭州"],
                ["2024-01-02", 1200, 60000.0, "杭州"],
                ["2024-01-03", 800, 40000.0, "杭州"],
                ["2024-01-04", 1500, 75000.0, "杭州"],
                ["2024-01-05", 1100, 55000.0, "杭州"]
            ],
            "totalRows": 5,
            "executionTime": 125
        }
        
        return {"success": True, "data": mock_result}
        
    except Exception as e:
        logger.error(f"SQL查询执行失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SQL查询失败: {str(e)}")

@router.post("/smart-query")
async def generate_smart_query(request: Dict[str, Any]):
    """智能查询生成"""
    try:
        data_source = request.get("dataSource")
        template_type = request.get("template_type")
        prompt = request.get("prompt")
        
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")

        # 使用Text2SQL服务生成查询
        result: SQLQueryResult = await text2sql_service.natural_language_to_sql(
            query=prompt,
            context={
                "table_info": data_source,
                "domain": template_type
            }
        )
        
        if result.success:
            return {
                "success": True,
                "sql": result.sql,
                "explanation": result.explanation
            }
        else:
            raise HTTPException(status_code=400, detail=result.error or "查询生成失败")
            
    except Exception as e:
        logger.error(f"智能查询生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"智能查询生成失败: {str(e)}")

@router.post("/charts/preview")
async def preview_chart(chart_config: Dict[str, Any]):
    """图表预览"""
    try:
        # 这里应该根据图表配置生成实际的图表数据
        # 暂时返回模拟数据
        mock_chart_data = {
            "type": chart_config.get("type", "bar"),
            "title": chart_config.get("title", "图表"),
            "data": {
                "labels": ["一月", "二月", "三月", "四月", "五月"],
                "datasets": [{
                    "label": "数据系列1",
                    "data": [12, 19, 3, 5, 2],
                    "backgroundColor": chart_config.get("colors", ["#3B82F6"])[0]
                }]
            },
            "options": chart_config.get("options", {})
        }
        
        return {"success": True, "data": mock_chart_data}
        
    except Exception as e:
        logger.error(f"图表预览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"图表预览失败: {str(e)}")

@router.post("/reports/save")
async def save_report(report_data: Dict[str, Any]):
    """保存报告"""
    try:
        # 这里应该将报告数据保存到数据库
        # 包含报告内容、模板信息、图表配置等
        
        report_id = f"report_{int(time.time())}"
        
        # 模拟保存操作
        saved_report = {
            "id": report_id,
            "title": report_data.get("title", "未命名报告"),
            "content": report_data.get("content", ""),
            "template": report_data.get("template"),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        return {"success": True, "report": saved_report}
        
    except Exception as e:
        logger.error(f"报告保存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"报告保存失败: {str(e)}")

@router.post("/reports/export/{format}")
async def export_report(format: str, request: Dict[str, Any]):
    """导出报告"""
    try:
        content = request.get("content", "")
        template = request.get("template", {})
        
        if format not in ["word", "pdf"]:
            raise HTTPException(status_code=400, detail="不支持的导出格式")
        
        # 这里应该实现实际的导出逻辑
        # 将Markdown内容转换为Word或PDF
        
        # 暂时返回成功响应
        return {"success": True, "message": f"报告已导出为{format}格式"}
        
    except Exception as e:
        logger.error(f"报告导出失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"报告导出失败: {str(e)}")

@router.get("/reports/{report_id}")
async def get_report(report_id: str):
    """获取报告详情"""
    try:
        # 这里应该从数据库获取报告信息
        # 暂时返回模拟数据
        
        report = {
            "id": report_id,
            "title": "示例报告",
            "content": "# 示例报告\n\n这是一个示例报告内容...",
            "template": {
                "id": "template_001",
                "type": "tourism_report"
            },
            "created_at": "2024-01-15T10:30:00",
            "updated_at": "2024-01-15T15:45:00"
        }
        
        return {"success": True, "report": report}
        
    except Exception as e:
        logger.error(f"获取报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取报告失败: {str(e)}")

@router.get("/reports")
async def list_reports(page: int = 1, limit: int = 10):
    """获取报告列表"""
    try:
        # 这里应该从数据库分页获取报告列表
        # 暂时返回模拟数据
        
        reports = [
            {
                "id": "report_001",
                "title": "2024年第一季度旅游数据报告",
                "created_at": "2024-01-15T10:30:00",
                "updated_at": "2024-01-15T15:45:00",
                "status": "published"
            },
            {
                "id": "report_002", 
                "title": "端午假期旅游分析报告",
                "created_at": "2024-01-20T09:15:00",
                "updated_at": "2024-01-20T14:22:00",
                "status": "draft"
            }
        ]
        
        return {
            "success": True,
            "reports": reports,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": len(reports)
            }
        }
        
    except Exception as e:
        logger.error(f"获取报告列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取报告列表失败: {str(e)}")

@router.get("/charts/{chart_id}_config.json")
async def get_chart_config(chart_id: str):
    """获取图表配置文件"""
    try:
        # 构建配置文件路径
        config_dir = Path("./output/chartjs_configs")
        config_file = config_dir / f"{chart_id}_config.json"
        
        # 检查文件是否存在
        if not config_file.exists():
            raise HTTPException(status_code=404, detail=f"图表配置文件未找到: {chart_id}")
        
        # 读取并返回JSON配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return JSONResponse(content=config_data)
        
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail=f"图表配置文件未找到: {chart_id}")
    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail=f"图表配置文件格式错误: {chart_id}")
    except Exception as e:
        logger.error(f"获取图表配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取图表配置失败: {str(e)}")

@router.get("/charts/configs")
async def list_chart_configs():
    """获取所有可用的图表配置文件列表"""
    try:
        config_dir = Path("./output/chartjs_configs")
        
        if not config_dir.exists():
            return {"success": True, "charts": []}
        
        chart_configs = []
        for config_file in config_dir.glob("chart_*_config.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                chart_id = config_file.stem.replace('_config', '')
                chart_configs.append({
                    "id": chart_id,
                    "title": config_data.get("options", {}).get("plugins", {}).get("title", {}).get("text", chart_id),
                    "type": config_data.get("type", "unknown"),
                    "configFile": f"/api/charts/{config_file.name}",
                    "lastModified": os.path.getmtime(config_file)
                })
                
            except Exception as e:
                logger.warning(f"跳过无效配置文件 {config_file}: {str(e)}")
                continue
        
        return {"success": True, "charts": chart_configs}
        
    except Exception as e:
        logger.error(f"获取图表配置列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取图表配置列表失败: {str(e)}")

@router.post("/word-parser/process")
async def process_word_document(file: UploadFile = File(...)):
    """完整处理Word文档 - 解析、提取图表、生成配置"""
    try:
        # 验证文件类型
        if not file.filename or not file.filename.endswith(('.docx', '.doc')):
            raise HTTPException(status_code=400, detail="仅支持.docx和.doc格式文件")
        
        # 创建临时目录
        tmp_dir = Path("./tmp/")
        tmp_dir.mkdir(exist_ok=True)

        # 保存上传的文件到tmp目录
        file_path = tmp_dir / file.filename
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 调用完整的处理流程
        from services.chart_service import ChartService
        
        chart_service = ChartService()
        result = await chart_service.process_word_document_complete(str(file_path))
        
        return {
            "success": True,
            "document": {
                "filename": file.filename,
                "markdownFile": result.get("markdown_file"),
                "chartCount": len(result.get("chart_configs", []))
            },
            "charts": result.get("chart_configs", []),
            "markdownContent": result.get("markdown_content", ""),
            "processing_summary": result.get("summary", {})
        }
        
    except Exception as e:
        logger.error(f"Word文档处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文档处理失败: {str(e)}")