import type { Metada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import { SessionProvider } from "next-auth/react";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-jetbrains-mono",
  display: "swap",
});

export const metadata: Metadata = {
  title: "AI REPORT - 数据查询平台",
  description: "AI REPORT 智能ai报告生成",
  keywords: [
    "Text2SQL",
    "REPORT",
    "AI",
    "SQL",
    "数据查询",
    "人工智能",
    "极简设计",
  ],
  authors: [{ name: "muzi" }],
  icons: {
    icon: "/favicon.ico",
    apple: "/apple-touch-icon.png",
  },
  openGraph: {
    title: "AI REPORT",
    description: "AI REPORT - 数据查询平台",
    type: "website",
    locale: "zh_CN",
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: "#2563eb",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="zh-CN"
      className={`${inter.variable} ${jetbrainsMono.variable}`}
      suppressHydrationWarning
    >
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
      </head>
      <body
        className="min-h-screen bg-cyber-light text-cyber-text font-sans antialiased"
        suppressHydrationWarning={true}
      >
        {/* 背景效果 */}
        <div className="fixed inset-0 cyber-grid opacity-30 pointer-events-none" />
        <div className="fixed inset-0 light-rays opacity-40 pointer-events-none" />

        {/* 主要内容 */}
        <SessionProvider>
          <div className="relative z-10">{children}</div>
        </SessionProvider>

        {/* 全局脚本 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 防止FOUC (Flash of Unstyled Content)
              document.documentElement.classList.remove('dark');

              // 添加键盘快捷键支持
              document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + K 打开搜索
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                  e.preventDefault();
                  // 触发搜索功能
                  const searchEvent = new CustomEvent('openSearch');
                  document.dispatchEvent(searchEvent);
                }

                // ESC 关闭模态框
                if (e.key === 'Escape') {
                  const escapeEvent = new CustomEvent('escapePressed');
                  document.dispatchEvent(escapeEvent);
                }
              });

              // 添加白色效果
              function addFutureEffects() {
                // 优雅浮动效果
                const elements = document.querySelectorAll('.neon-text');
                elements.forEach(el => {
                  if (Math.random() < 0.05) {
                    el.style.animation = 'gentle-float 2s ease-in-out';
                    setTimeout(() => {
                      el.style.animation = '';
                    }, 2000);
                  }
                });
              }

              // 每8秒执行一次浮动效果
              setInterval(addFutureEffects, 8000);
            `,
          }}
        />
      </body>
    </html>
  );
}
