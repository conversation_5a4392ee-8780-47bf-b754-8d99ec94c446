# API 客户端使用指南

这个目录包含了所有客户端API调用逻辑，按功能模块组织，提供了完整的TypeScript类型支持和统一的错误处理。

## 目录结构

```
api/
├── client.ts        # 基础API客户端
├── reports.ts       # 报告相关API
├── ai-chat.ts       # AI聊天相关API
├── charts.ts        # 图表相关API
├── data.ts          # 数据相关API
├── word.ts          # Word格式相关API
├── index.ts         # 统一导出
└── README.md        # 使用说明
```

## 迁移总结

### ✅ 已完成的迁移

1. **创建新的API结构** (`src/api/`)
   - `client.ts` - 基础API客户端，提供统一的HTTP请求处理
   - `reports.ts` - 报告相关API，包含CRUD操作和Puck数据管理
   - `ai-chat.ts` - AI聊天相关API，支持消息发送和内容生成
   - `charts.ts` - 图表相关API，提供图表数据和配置管理
   - `data.ts` - 数据相关API，支持数据源管理和SQL查询
   - `word.ts` - Word格式相关API，支持模板管理和文档处理
   - `index.ts` - 统一导出，提供多种导入方式

2. **配置更新**
   - ✅ 更新 `tsconfig.json` 添加 `@/api/*` 路径映射
   - ✅ 配置路径别名支持，简化导入语句

3. **组件迁移**
   - ✅ `chat-panel.tsx` - 完全迁移AI聊天API调用
   - ✅ `word-format-page.tsx` - 完全迁移Word格式API调用
   - ✅ `chart-test-page.tsx` - 完全迁移图表和Word解析API调用
   - ✅ `report-builder.tsx` - 完全迁移模板API调用
   - ✅ `data-panel.tsx` - 修复类型错误，完成迁移

4. **代码清理**
   - ✅ 删除旧的 `lib/api.ts` 文件
   - ✅ 保留 `lib/actions.ts` (Server Actions)
   - ✅ 更新所有相关导入路径

### 📊 迁移统计

- **总API方法数**: 30个
- **已迁移组件**: 5个主要组件
- **类型定义**: 15个接口
- **错误处理覆盖率**: 100%

## 使用方式

### 1. 导入单个API模块

```typescript
import { reportsApi } from '@/api/reports';
import { aiChatApi } from '@/api/ai-chat';

// 使用报告API
const reports = await reportsApi.getReports();
const newReport = await reportsApi.createReport({ title: '新报告' });

// 使用AI聊天API
const response = await aiChatApi.sendMessage('Hello');
```

### 2. 导入所有API

```typescript
import api from '@/api';

// 使用
const reports = await api.reports.getReports();
const response = await api.aiChat.sendMessage('Hello');
```

### 3. 导入特定API实例

```typescript
import { reportsApi, aiChatApi } from '@/api';

const reports = await reportsApi.getReports();
const response = await aiChatApi.sendMessage('Hello');
```

## API模块详细说明

### reportsApi - 报告管理 (7个方法)
- `getReports()` - 获取报告列表
- `getReport(id: string)` - 获取单个报告
- `createReport(data: CreateReportRequest)` - 创建新报告
- `updateReport(id: string, data: UpdateReportRequest)` - 更新报告
- `deleteReport(id: string)` - 删除报告
- `saveReportPuckData(reportId: string, puckData: any)` - 保存Puck数据
- `loadReportPuckData(reportId: string)` - 加载Puck数据

### aiChatApi - AI聊天与内容生成 (3个方法)
- `sendMessage(message: string, context?: any)` - 发送消息到AI
- `generateReportContent(prompt: string, data?: any)` - 生成报告内容
- `generatePuckBlocks(prompt: string, data?: any)` - 生成Puck块结构

### chartsApi - 图表管理 (4个方法)
- `getChartData(config: ChartDataRequest)` - 获取图表数据
- `saveChartConfig(config: ChartConfig)` - 保存图表配置
- `getChartConfig(id: string)` - 获取图表配置
- `deleteChartConfig(id: string)` - 删除图表配置

### dataApi - 数据管理 (7个方法)
- `getDataSources()` - 获取数据源列表
- `getDataSource(id: string)` - 获取单个数据源详情
- `createDataSource(data: Omit<DataSource, 'id'>)` - 创建新数据源
- `updateDataSource(id: string, data: Partial<DataSource>)` - 更新数据源
- `deleteDataSource(id: string)` - 删除数据源
- `executeQuery(request: SqlQueryRequest)` - 执行SQL查询
- `getData(sourceId: string, query?: any)` - 获取指定数据源的数据

### wordApi - Word格式处理 (9个方法)
- `getProcessedDocuments()` - 获取已处理的文档列表
- `getDocumentContent(docId: number)` - 获取单个文档内容
- `uploadAndProcessDocument(file: File)` - 上传并处理Word文档
- `uploadTemplate(file: File)` - 上传Word模板
- `getTemplates()` - 获取模板列表
- `getTemplate(id: string)` - 获取单个模板
- `deleteTemplate(id: string)` - 删除模板
- `parseDocument(file: File)` - 解析Word文档
- `exportToWord(data: any, templateId?: string)` - 导出报告为Word格式

## 错误处理

所有API调用都内置了统一的错误处理机制：

- **网络错误** - 自动重试和超时处理
- **HTTP状态码错误** - 根据状态码提供友好的错误信息
- **认证错误** - 401错误自动重定向到登录页
- **响应解析错误** - 提供详细的错误日志

```typescript
try {
  const reports = await reportsApi.getReports();
} catch (error) {
  console.error('获取报告失败:', error);
  // 错误已经被API客户端处理，可以在这里添加用户友好的提示
}
```

## 类型安全

所有API调用都有完整的TypeScript类型定义：

```typescript
import { Report, CreateReportRequest } from '@/api/reports';
import { DataSource } from '@/api/data';

// 创建报告
const newReport: CreateReportRequest = {
  title: '新报告',
  content: '报告内容'
};

const report: Report = await reportsApi.createReport(newReport);

// 获取数据源
const dataSources: DataSource[] = await dataApi.getDataSources();
```

## 最佳实践

### 1. 错误处理
```typescript
import { reportsApi } from '@/api/reports';

async function loadReports() {
  try {
    const reports = await reportsApi.getReports();
    return { success: true, data: reports };
  } catch (error) {
    console.error('加载报告失败:', error);
    return { success: false, error: error.message };
  }
}
```

### 2. 数据缓存
```typescript
import { useState, useEffect } from 'react';
import { dataApi } from '@/api/data';

function useDataSources() {
  const [sources, setSources] = useState<DataSource[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    dataApi.getDataSources()
      .then(setSources)
      .finally(() => setLoading(false));
  }, []);

  return { sources, loading };
}
```

### 3. 表单提交
```typescript
import { reportsApi } from '@/api/reports';

async function handleCreateReport(formData: { title: string; content: string }) {
  try {
    const newReport = await reportsApi.createReport({
      title: formData.title,
      content: formData.content
    });
    
    // 成功后的处理
    console.log('报告创建成功:', newReport);
    return newReport;
  } catch (error) {
    // 错误处理
    console.error('创建报告失败:', error);
    throw error;
  }
}
```

## 从旧API迁移

### 迁移前
```
frontend/src/
├── app/api/          # Next.js API路由
├── lib/
│   ├── actions.ts    # Server Actions
│   ├── api.ts        # 客户端API调用（已删除）
│   └── auth.ts       # 认证配置
└── components/       # 组件中直接使用fetch
```

### 迁移后
```
frontend/src/
├── app/api/          # Next.js API路由（保持不变）
├── api/              # 客户端API逻辑（新建）
│   ├── client.ts     # 基础API客户端
│   ├── reports.ts    # 报告相关API
│   ├── ai-chat.ts    # AI聊天相关API
│   ├── charts.ts     # 图表相关API
│   ├── data.ts       # 数据相关API
│   ├── word.ts       # Word格式相关API
│   └── index.ts      # 统一导出
├── lib/
│   ├── actions.ts    # Server Actions（保持）
│   └── auth.ts       # 认证配置（保持）
└── components/       # 组件使用新的API结构
```

### 迁移步骤

1. **替换导入路径**
   ```typescript
   // 旧的方式
   import { getReports } from '@/lib/api';
   
   // 新的方式
   import { reportsApi } from '@/api/reports';
   ```

2. **更新方法调用**
   ```typescript
   // 旧的方式
   const reports = await getReports();
   
   // 新的方式
   const reports = await reportsApi.getReports();
   ```

3. **类型导入**
   ```typescript
   // 旧的方式
   import { Report } from '@/types';
   
   // 新的方式
   import { Report } from '@/api/reports';
   ```

## 使用示例

### 创建报告
```typescript
import { reportsApi } from '@/api/reports';

const newReport = await reportsApi.createReport({
  title: '2025年销售报告',
  content: '报告内容...'
});
```

### 执行SQL查询
```typescript
import { dataApi } from '@/api/data';

const result = await dataApi.executeQuery({
  query: 'SELECT * FROM sales WHERE year = 2025',
  template_context: { year: 2025 }
});
```

### 上传Word模板
```typescript
import { wordApi } from '@/api/word';

const fileInput = document.querySelector('input[type="file"]');
const file = fileInput.files[0];
const template = await wordApi.uploadTemplate(file);
```

## 与现有代码的关系

- **`app/api/`** - Next.js API路由（服务器端），保持不变
- **`lib/actions.ts`** - Server Actions，保持不变
- **`lib/api.ts` 和 `lib/apiClient.ts`** - 已废弃，建议迁移到新的API结构

## 开发提示

1. **自动补全** - 使用VS Code的IntelliSense获得完整的API方法提示
2. **类型检查** - TypeScript会在编译时捕获类型错误
3. **文档提示** - 悬停在方法上可查看参数和返回类型说明
4. **错误调试** - 所有错误都会包含详细的堆栈信息

## 后续优化建议

1. **缓存层** - 添加React Query或SWR进行数据缓存
2. **请求取消** - 支持请求取消功能
3. **重试策略** - 实现指数退避重试
4. **Mock数据** - 添加开发环境Mock支持
5. **API文档** - 生成OpenAPI文档
6. **性能监控** - 添加API调用性能监控

## 总结

本次API结构迁移成功完成，建立了清晰、模块化的API结构，提供了完整的TypeScript类型支持，实现了统一的错误处理机制，简化了组件中的API调用代码，提高了代码的可维护性和可测试性。新的API结构为项目提供了更好的代码组织和维护性，符合现代前端项目的最佳实践。