from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>RequestForm
from sqlalchemy.orm import Session

from database.connection import get_db
from services.auth_service import AuthService
from core.logging import get_logger

auth_router = APIRouter(
    prefix="/auth",
    tags=["Authentication"],
)

logger = get_logger(__name__)

@auth_router.post("/login")
async def login_for_next_auth(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()], 
    db: Session = Depends(get_db)
):
    user = AuthService.authenticate_user(
        db, email=form_data.username, password=form_data.password
    )
    
    if not user:
        logger.warning(f"用户认证失败: {form_data.username}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return {"id": str(user.id), "name": user.full_name, "email": user.email}


