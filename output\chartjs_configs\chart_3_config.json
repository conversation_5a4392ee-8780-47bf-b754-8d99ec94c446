{"type": "pie", "data": {"labels": ["观光／休闲", "探亲访友", "研学／体育／科技交流", "商务活动", "会议／培训／展览", "文化艺术欣赏／娱乐", "购物", "医疗康养", "宗教活动", "其他"], "datasets": [{"data": [0.545748987854251, 0.209986504723347, 0.0480431848852901, 0.0461538461538462, 0.0369770580296896, 0.0313090418353576, 0.0118758434547908, 0.0102564102564103, 0.00728744939271255, 0.052361673414305], "backgroundColor": ["#2E86AB", "#A23B72", "#F18F01", "#C73E1D", "#592E83", "#F79D84", "#4C956C", "#F2CC8F", "#0d7cab", "#884167"], "borderColor": "#ffffff", "borderWidth": 2, "hoverOffset": 4}]}, "options": {"responsive": true, "maintainAspectRatio": false, "plugins": {"title": {"display": true, "text": "图表 chart_3", "font": {"size": 16, "weight": "bold"}, "padding": {"top": 10, "bottom": 30}}, "legend": {"display": true, "position": "right", "labels": {"usePointStyle": true, "font": {"size": 12}, "padding": 15}}, "tooltip": {"callbacks": {"label": "function(context) { return context.label + ': ' + context.formattedValue + ' (' + Math.round(context.parsed * 100 / context.dataset.data.reduce((a, b) => a + b, 0)) + '%)'; }"}}}, "layout": {"padding": 20}}}