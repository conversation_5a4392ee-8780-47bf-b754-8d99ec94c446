# 前端技术方案概述

本文档概述了当前用于从 Markdown 内容直接渲染由 D3.js 驱动的动态 SVG 图表和自定义表格的架构。此方案取代了之前基于 `rehype` 插件的解决方案。

## 🎯 核心架构

### 1. 核心技术栈
- **图表渲染**: D3.js + SVG + React
- **Markdown 解析**: ReactMarkdown + remarkGfm
- **组件系统**: TypeScript + React 18
- **样式系统**: Tailwind CSS

### 2. 图表组件层 (D3.js 驱动)
系统构建在一个可复用的 D3.js 图表组件库之上。

- **📊 基础图表**
  - `SimpleBarChart`: 用于标准柱状图。
  - `SimplePieChart`: 用于标准饼图。
- **📈 复杂组合图表**
  - `ComboChart`: 用于带双 Y 轴的柱状图和折线图组合。
  - `NestedPieChart`: 用于嵌套（旭日图/环形）饼图。

### 3. Markdown 渲染流程
我们在 Markdown 源文件中使用基于占位符的系统。通过为 `ReactMarkdown` 配置自定义渲染器，来拦截这些占位符并渲染相应的 React 组件。

**占位符语法:**
```markdown
{{CHART_revenue_2024}}     // 渲染一个图表
{{TABLE_sales_summary}}    // 渲染一个表格
```

**自定义渲染器逻辑:**
```typescript
const components: Components = {
  p: ({ children }) => {
    const textContent = children.toString();
    // 检测并渲染图表占位符
    if (textContent.startsWith("{{CHART_")) {
      const chartId = textContent.replace(/{{CHART_|}}/g, '');
      return <SmartChartRenderer chartId={chartId} />;
    }
    // 检测并渲染表格占位符
    if (textContent.startsWith("{{TABLE_")) {
      const tableId = textContent.replace(/{{TABLE_|}}/g, '');
      return <CustomTableComponent tableId={tableId} />;
    }
    return <p>{children}</p>;
  }
};
```

### 4. 数据流程图
```mermaid
graph TD
    A[Markdown 文本] --> B[ReactMarkdown 解析器]
    B --> C{在 <p> 标签中检测占位符}
    C -->|{{CHART_ID}}| D[SmartChartRenderer]
    C -->|{{TABLE_ID}}| E[CustomTableComponent]
    C -->|其他文本| F[标准 HTML <p>]
    D --> G[D3.js 图表组件]
    G --> H[SVG 输出]
    E --> I[HTML 表格输出]
```

## ✨ 核心优势

### 1. 原生 SVG 导出
所有图表都渲染为 SVG，从而可以获得高质量、可缩放的图形，并能轻松导出用于其他应用程序（如 Typst）或生成 PDF。
```typescript
const exportSVG = () => {
  const svgElement = svgRef.current;
  const svgData = new XMLSerializer().serializeToString(svgElement);
  // ... 下载或使用 SVG 数据的逻辑
};
```

### 2. 高度可定制的复杂图表
基于组件的架构允许通过清晰、声明式的 props 来创建复杂的多层图表。
```typescript
<ComboChart 
  data={cityData}
  width={800}
  height={400}
  title="城市旅游数据分析"
  barColor="#4A90E2"
  lineColor="#7ED321"
/>
```

### 3. 类型安全的接口
所有图表和表格的数据结构都使用 TypeScript 进行了强类型定义，减少了运行时错误并改善了开发体验。
```typescript
interface ChartData {
  id: string;
  chartId: string; 
  title: string;
  config: D3ChartConfig; // 标准化的 D3 配置
}

interface TableData {
  id: string;
  title: string;
  headers: string[];
  rows: string[][];
}
```

## 🔧 实现状态

### ✅ 已完成
- 核心 D3.js 图表组件库（4个组件）。
- SVG 和 PNG 导出功能。
- Markdown 解析及占位符渲染系统。
- 自定义表格组件。
- 所有数据结构的 TypeScript 类型定义。

### 🚀 未来可优化的方向
- 实现统一的图表主题系统。
- 增加更多图表类型（例如，散点图、雷达图）。
- 为表格组件增加高级功能，如排序和筛选。
- 开发报告中所有资源的批量导出功能。
- 增强图表的交互性（如工具提示、缩放、刷选）。

## 📝 使用示例

```markdown
# 2024年第一季度销售报告

## 销售趋势分析
{{CHART_sales_trend_q1}}

## 区域销售数据
{{TABLE_regional_sales}}

## 产品类别对比
{{CHART_product_category_combo}}