// 图表组件导出
export { default as SimpleBarChart } from './SimpleBarChart';
export { default as SimplePieChart } from './SimplePieChart';
export { default as ComboChart } from './combo_chart_component';
export { default as NestedPieChart } from './nested-pie-chart';

// 类型导出
export * from './types/chartTypes';

// 工具函数导出
export * from './utils/chartHelpers';
export * from './utils/colorSchemes';

// Hooks导出
export { default as useD3Chart } from './hooks/useD3Chart';
export { default as useChartExport } from './hooks/useChartExport';

// 示例数据导出
export { exampleData as barChartExampleData } from './SimpleBarChart';
export { exampleData as pieChartExampleData } from './SimplePieChart';
export { exampleData as comboChartExampleData } from './combo_chart_component';

// 图表库信息
export const CHART_LIBRARY_INFO = {
  name: 'D3.js Chart Library',
  version: '1.0.0',
  description: '基于D3.js的SVG图表组件库',
  features: [
    'SVG原生输出',
    '完全自定义样式',
    '响应式设计',
    '动画效果',
    '交互功能',
    '多种导出格式',
    'TypeScript支持'
  ],
  supportedCharts: [
    'SimpleBarChart - 单一柱状图',
    'SimplePieChart - 单一饼图',
    'ComboChart - 组合图表',
    'NestedPieChart - 嵌套饼图'
  ]
}; 