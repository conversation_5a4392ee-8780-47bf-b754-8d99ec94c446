"use client";

import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface NestedPieChartProps {
  data: {
    outer: Array<{ name: string; value: number; color?: string }>;
    inner: Array<{ name: string; value: number; color?: string; parent: string }>;
  };
  width?: number;
  height?: number;
  className?: string;
  showPercentages?: boolean; // 新增：是否显示百分比
  title?: string;
}

// 优化的颜色方案 - 更鲜明的对比色
const ENHANCED_COLORS = {
  primary: ['#4A90E2', '#7ED321', '#F5A623', '#D0021B', '#9013FE', '#50E3C2'],
  secondary: ['#6BB6FF', '#95E84B', '#FFB84D', '#E6334A', '#B84FFF', '#70F5E6'],
  accent: '#FF6B6B'
};

// 数值格式化函数
const formatNumber = (value: number, decimals: number = 2): string => {
  return value.toFixed(decimals);
};

const formatPercentage = (value: number, total: number, decimals: number = 1): string => {
  return ((value / total) * 100).toFixed(decimals) + '%';
};

// 检查标签是否应该显示（避免重合）
const shouldShowLabel = (d: any, minPercentage: number = 5): boolean => {
  const percentage = (d.endAngle - d.startAngle) / (2 * Math.PI) * 100;
  return percentage >= minPercentage;
};

export default function NestedPieChart({ 
  data, 
  width = 700, 
  height = 500,
  className = "",
  showPercentages = true,
  title = "嵌套饼图"
}: NestedPieChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const legendRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!svgRef.current || !legendRef.current) return;

    // 清空之前的内容
    d3.select(svgRef.current).selectAll("*").remove();
    d3.select(legendRef.current).selectAll("*").remove();

    const svg = d3.select(svgRef.current);
    const margin = 50;

    // 创建组元素
    const g = svg.append('g')
      .attr('transform', `translate(${width/2}, ${height/2})`);

    // 优化半径设置 - 更合理的扇形大小和间距
    const outerRadius = Math.min(width, height) / 2 - margin;
    const innerRadius = outerRadius * 0.55; // 减小内环，增加外环可见性
    const childRadius = innerRadius * 0.75; // 减小内饼图半径

    // 计算总值
    const outerTotal = d3.sum(data.outer, d => d.value);
    const innerTotal = d3.sum(data.inner, d => d.value);

    // 创建饼图生成器 - 增加间距
    const outerPie = d3.pie<any>()
      .value(d => d.value)
      .sort(null)
      .padAngle(0.02); // 添加扇形间距

    const innerPie = d3.pie<any>()
      .value(d => d.value)
      .sort(null)
      .padAngle(0.01);

    // 创建弧生成器
    const outerArc = d3.arc<any>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius)
      .cornerRadius(3); // 圆角效果

    const innerArc = d3.arc<any>()
      .innerRadius(0)
      .outerRadius(childRadius)
      .cornerRadius(2);

    // 标签弧生成器
    const labelArc = d3.arc<any>()
      .innerRadius(outerRadius * 1.15)
      .outerRadius(outerRadius * 1.15);

    // 创建工具提示
    const tooltip = d3.select('body').append('div')
      .attr('class', 'chart-tooltip')
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background', 'rgba(0, 0, 0, 0.8)')
      .style('color', 'white')
      .style('padding', '8px 12px')
      .style('border-radius', '4px')
      .style('font-size', '12px')
      .style('pointer-events', 'none')
      .style('z-index', '1000');

    // 绘制外层饼图
    const outerSlices = g.selectAll('.outer-slice')
      .data(outerPie(data.outer))
      .enter()
      .append('g')
      .attr('class', 'outer-slice');

    outerSlices.append('path')
      .attr('d', outerArc)
      .attr('fill', (d, i) => data.outer[i]?.color || ENHANCED_COLORS.primary[i % ENHANCED_COLORS.primary.length] || '#4A90E2')
      .attr('stroke', 'white')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .style('filter', 'drop-shadow(2px 2px 4px rgba(0,0,0,0.1))')
      .on('mouseover', function(event, d) {
        d3.select(this)
          .transition()
          .duration(150)
          .style('opacity', 0.8)
          .attr('transform', 'scale(1.02)');
        
        // 显示工具提示
        tooltip
          .style('visibility', 'visible')
          .html(`
            <strong>${d.data.name}</strong><br/>
            数值: ${formatNumber(d.data.value)}<br/>
            占比: ${formatPercentage(d.data.value, outerTotal)}
          `)
          .style('left', `${event.pageX + 10}px`)
          .style('top', `${event.pageY - 10}px`);
      })
      .on('mouseout', function(event, d) {
        d3.select(this)
          .transition()
          .duration(150)
          .style('opacity', 1)
          .attr('transform', 'scale(1)');
        
        tooltip.style('visibility', 'hidden');
      });

    // 绘制内层饼图
    const innerSlices = g.selectAll('.inner-slice')
      .data(innerPie(data.inner))
      .enter()
      .append('g')
      .attr('class', 'inner-slice');

    innerSlices.append('path')
      .attr('d', innerArc)
      .attr('fill', (d, i) => data.inner[i]?.color || ENHANCED_COLORS.secondary[i % ENHANCED_COLORS.secondary.length] || '#6BB6FF')
      .attr('stroke', 'white')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .style('filter', 'drop-shadow(1px 1px 2px rgba(0,0,0,0.1))')
      .on('mouseover', function(event, d) {
        d3.select(this)
          .transition()
          .duration(150)
          .style('opacity', 0.8)
          .attr('transform', 'scale(1.05)');
        
        // 显示工具提示
        tooltip
          .style('visibility', 'visible')
          .html(`
            <strong>${d.data.name}</strong><br/>
            数值: ${formatNumber(d.data.value)}<br/>
            占比: ${formatPercentage(d.data.value, innerTotal)}<br/>
            所属: ${d.data.parent || '未知'}
          `)
          .style('left', `${event.pageX + 10}px`)
          .style('top', `${event.pageY - 10}px`);
      })
      .on('mouseout', function(event, d) {
        d3.select(this)
          .transition()
          .duration(150)
          .style('opacity', 1)
          .attr('transform', 'scale(1)');
        
        tooltip.style('visibility', 'hidden');
      });

    // 外层标签 - 只显示足够大的扇形标签，避免重合
    const outerLabels = outerSlices
      .filter(d => shouldShowLabel(d, 8)) // 只显示占比大于8%的标签
      .append('text')
      .attr('transform', d => `translate(${labelArc.centroid(d)})`)
      .attr('text-anchor', d => {
        const midAngle = d.startAngle + (d.endAngle - d.startAngle) / 2;
        return midAngle < Math.PI ? 'start' : 'end';
      })
      .style('font-size', '13px')
      .style('font-weight', 'bold')
      .style('fill', '#2c3e50');

    outerLabels.append('tspan')
      .attr('x', 0)
      .attr('dy', '0em')
      .text(d => d.data.name);

    if (showPercentages) {
      outerLabels.append('tspan')
        .attr('x', 0)
        .attr('dy', '1.2em')
        .style('font-weight', 'normal')
        .style('font-size', '12px')
        .style('fill', '#7f8c8d')
        .text(d => `${formatNumber(d.data.value)} (${formatPercentage(d.data.value, outerTotal)})`);
    }

    // 内层标签 - 只显示较大的扇形，避免重合
    const innerLabels = innerSlices
      .filter(d => shouldShowLabel(d, 10)) // 只显示占比大于10%的标签
      .append('text')
      .attr('transform', d => `translate(${innerArc.centroid(d)})`)
      .attr('text-anchor', 'middle')
      .style('font-size', '10px')
      .style('font-weight', 'bold')
      .style('fill', 'white')
      .style('text-shadow', '1px 1px 2px rgba(0,0,0,0.5)');

    innerLabels.append('tspan')
      .attr('x', 0)
      .attr('dy', showPercentages ? '-0.3em' : '0.35em')
      .text(d => d.data.name);

    if (showPercentages) {
      innerLabels.append('tspan')
        .attr('x', 0)
        .attr('dy', '1em')
        .style('font-size', '9px')
        .text(d => formatPercentage(d.data.value, innerTotal));
    }

    // 优化连接线
    g.selectAll('.connector-line')
      .data(innerPie(data.inner))
      .enter()
      .append('line')
      .attr('class', 'connector-line')
      .attr('x1', d => innerArc.centroid(d)[0])
      .attr('y1', d => innerArc.centroid(d)[1])
      .attr('x2', d => {
        const outerSlice = outerPie(data.outer).find(od => od.data.name === d.data.parent);
        return outerSlice ? outerArc.centroid(outerSlice)[0] * 0.7 : 0;
      })
      .attr('y2', d => {
        const outerSlice = outerPie(data.outer).find(od => od.data.name === d.data.parent);
        return outerSlice ? outerArc.centroid(outerSlice)[1] * 0.7 : 0;
      })
      .style('stroke', '#95a5a6')
      .style('stroke-width', 1.5)
      .style('stroke-dasharray', '4,2')
      .style('opacity', 0.6);

    // 添加中央标题
    g.append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '0.35em')
      .style('font-size', '14px')
      .style('font-weight', 'bold')
      .style('fill', '#2c3e50')
      .text('客源结构');

    // 创建简化的图例（移除分类标题）
    const legend = d3.select(legendRef.current);
    
    // 外层图例
    data.outer.forEach((item, i) => {
      const legendItem = legend.append('div')
        .attr('class', 'flex items-center mb-2 p-2 rounded hover:bg-gray-50 transition-colors');
      
      legendItem.append('div')
        .attr('class', 'w-5 h-5 rounded mr-3 shadow-sm')
        .style('background-color', item.color || ENHANCED_COLORS.primary[i % ENHANCED_COLORS.primary.length] || '#4A90E2');
      
      const textContainer = legendItem.append('div')
        .attr('class', 'flex-1');

      textContainer.append('div')
        .attr('class', 'text-sm font-medium text-gray-800')
        .text(item.name);

      if (showPercentages) {
        textContainer.append('div')
          .attr('class', 'text-xs text-gray-600')
          .text(`${formatNumber(item.value)} (${formatPercentage(item.value, outerTotal)})`);
      } else {
        textContainer.append('div')
          .attr('class', 'text-xs text-gray-600')
          .text(formatNumber(item.value));
      }
    });

    // 内层图例
    data.inner.forEach((item, i) => {
      const legendItem = legend.append('div')
        .attr('class', 'flex items-center mb-1 p-2 ml-4 rounded hover:bg-gray-50 transition-colors');
      
      legendItem.append('div')
        .attr('class', 'w-4 h-4 rounded mr-3 shadow-sm')
        .style('background-color', item.color || ENHANCED_COLORS.secondary[i % ENHANCED_COLORS.secondary.length] || '#6BB6FF');
      
      const textContainer = legendItem.append('div')
        .attr('class', 'flex-1');

      textContainer.append('div')
        .attr('class', 'text-sm text-gray-700')
        .text(item.name);

      if (showPercentages) {
        textContainer.append('div')
          .attr('class', 'text-xs text-gray-500')
          .text(`${formatNumber(item.value)} (${formatPercentage(item.value, innerTotal)})`);
      } else {
        textContainer.append('div')
          .attr('class', 'text-xs text-gray-500')
          .text(formatNumber(item.value));
      }
    });

    // 清理函数
    return () => {
      tooltip.remove();
    };

  }, [data, width, height, showPercentages]);

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-gray-800 mb-2">
          {title}
        </h3>
        <p className="text-sm text-gray-600">
          内环显示详细分类，外环显示主要分类
        </p>
      </div>
      <div className="flex flex-col lg:flex-row items-start justify-center gap-8">
        <div className="flex-shrink-0">
          <svg
            ref={svgRef}
            width={width}
            height={height}
            className="border border-gray-100 rounded-lg shadow-sm max-w-full h-auto"
            viewBox={`0 0 ${width} ${height}`}
          />
        </div>
        <div ref={legendRef} className="flex flex-col min-w-0 max-w-xs" />
      </div>
    </div>
  );
} 