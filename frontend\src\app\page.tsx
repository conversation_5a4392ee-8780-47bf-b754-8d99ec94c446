"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  FileText,
  Database,
  BarChart,
  Zap,
  ArrowRight,
  Upload,
  LogIn,
} from "lucide-react";
import Link from "next/link";
import { useSession } from "next-auth/react";

export default function HomePage() {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  const features = [
    {
      icon: FileText,
      title: "word文档解析",
      description: "将Word文档自动转换为markdown格式",
      href: "/word-format",
    },
    {
      icon: FileText,
      title: "智能报告生成",
      description: "将Word文档自动转换为交互式报告",
      href: "/report-editor",
    },
    {
      icon: BarChart,
      title: "数据可视化展示",
      description: "查看所有可用的图表类型",
      href: "/chart-showcase",
    },
    {
      icon: Zap,
      title: "实时分析",
      description: "实时数据分析和可视化",
      href: "/analytics",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-cyber-light to-white">
      {/* 导航栏 */}
      <nav className="border-b border-cyber-border bg-white/80 backdrop-blur-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-xl font-bold text-cyber-primary">
                  智能报告生成器
                </h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/test"
                className="text-cyber-secondary hover:text-cyber-primary transition-colors"
              >
                测试页面
              </Link>
              {session ? (
                <Link href="/report-editor" className="cyber-button">
                  进入工作台
                </Link>
              ) : (
                <Link href="/login" className="cyber-button">
                  <LogIn className="w-4 h-4 mr-2" />
                  登录
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* 英雄区域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-6xl font-bold text-cyber-text mb-6">
            智能报告生成器
          </h2>
          <p className="text-xl text-cyber-secondary mb-8 max-w-3xl mx-auto">
            基于AI的智能报告生成系统，支持Word文档解析、数据可视化和自然语言查询
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/report-editor"
              className="cyber-button text-lg px-8 py-3 inline-flex items-center gap-2"
            >
              <Upload className="w-5 h-5" />
              上传文档
              <ArrowRight className="w-5 h-5" />
            </Link>
            <Link
              href="/test"
              className="cyber-button-secondary text-lg px-8 py-3 inline-flex items-center gap-2"
            >
              <Zap className="w-5 h-5" />
              功能测试
            </Link>
          </div>
        </motion.div>

        {/* 功能特性 */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="cyber-card p-6 text-center hover:shadow-lg transition-all duration-300"
            >
              <div className="w-12 h-12 bg-cyber-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                <feature.icon className="w-6 h-6 text-cyber-primary" />
              </div>
              <h3 className="text-lg font-semibold text-cyber-text mb-2">
                {feature.title}
              </h3>
              <p className="text-cyber-secondary text-sm mb-4">
                {feature.description}
              </p>
              <Link
                href={feature.href}
                className="text-cyber-primary hover:text-cyber-accent transition-colors text-sm font-medium inline-flex items-center gap-1"
              >
                了解更多
                <ArrowRight className="w-4 h-4" />
              </Link>
            </motion.div>
          ))}
        </motion.div>

        {/* 快速开始 */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="cyber-card p-8 text-center"
        >
          <h3 className="text-2xl font-bold text-cyber-text mb-4">快速开始</h3>
          <p className="text-cyber-secondary mb-6 max-w-2xl mx-auto">
            只需几个简单步骤，就能将您的Word文档转换为交互式报告
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 bg-cyber-primary text-white rounded-full flex items-center justify-center mb-3 font-bold">
                1
              </div>
              <h4 className="font-semibold text-cyber-text mb-2">上传文档</h4>
              <p className="text-cyber-secondary text-sm">上传您的Word文档</p>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 bg-cyber-primary text-white rounded-full flex items-center justify-center mb-3 font-bold">
                2
              </div>
              <h4 className="font-semibold text-cyber-text mb-2">AI解析</h4>
              <p className="text-cyber-secondary text-sm">
                AI自动解析文档内容和图表
              </p>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 bg-cyber-primary text-white rounded-full flex items-center justify-center mb-3 font-bold">
                3
              </div>
              <h4 className="font-semibold text-cyber-text mb-2">生成报告</h4>
              <p className="text-cyber-secondary text-sm">
                获得交互式可视化报告
              </p>
            </div>
          </div>
        </motion.div>
      </main>

      {/* 页脚 */}
      <footer className="border-t border-cyber-border bg-white/50 backdrop-blur-sm mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-cyber-secondary">
            <p>&copy; 2024 智能报告生成器.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
