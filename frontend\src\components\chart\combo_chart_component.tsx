import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface ComboChartData {
  city: string;
  visitors: number;
  growth: number;
}

interface ComboChartProps {
  data: ComboChartData[];
  width?: number;
  height?: number;
  className?: string;
  title?: string;
  barColor?: string;
  lineColor?: string;
}

export default function ComboChart({ 
  data, 
  width = 800, 
  height = 500,
  className = "",
  title = "浙江各市游客量与增长率对比",
  barColor = "#4A90E2",
  lineColor = "#7ED321"
}: ComboChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data || data.length === 0) return;

    // 清空之前的内容
    d3.select(svgRef.current).selectAll("*").remove();

    const svg = d3.select(svgRef.current);
    const margin = { top: 60, right: 80, bottom: 60, left: 80 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    // 创建主容器
    const container = svg.append('g')
      .attr('transform', `translate(${margin.left}, ${margin.top})`);

    // 创建比例尺
    const xScale = d3.scaleBand()
      .domain(data.map(d => d.city))
      .range([0, innerWidth])
      .padding(0.2);

    const yScaleLeft = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.visitors) || 0])
      .range([innerHeight, 0]);

    const yScaleRight = d3.scaleLinear()
      .domain(d3.extent(data, d => d.growth) as [number, number])
      .range([innerHeight, 0]);

    // 创建坐标轴
    const xAxis = d3.axisBottom(xScale);
    const yAxisLeft = d3.axisLeft(yScaleLeft);
    const yAxisRight = d3.axisRight(yScaleRight);

    // 绘制网格线
    container.append('g')
      .attr('class', 'grid')
      .attr('transform', `translate(0, ${innerHeight})`)
      .call(d3.axisBottom(xScale)
        .tickSize(-innerHeight)
        .tickFormat(() => '')
      )
      .style('stroke-dasharray', '3,3')
      .style('opacity', 0.3);

    container.append('g')
      .attr('class', 'grid')
      .call(d3.axisLeft(yScaleLeft)
        .tickSize(-innerWidth)
        .tickFormat(() => '')
      )
      .style('stroke-dasharray', '3,3')
      .style('opacity', 0.3);

    // 绘制柱状图
    const bars = container.selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', d => xScale(d.city) || 0)
      .attr('y', d => yScaleLeft(d.visitors))
      .attr('width', xScale.bandwidth())
      .attr('height', d => innerHeight - yScaleLeft(d.visitors))
      .attr('fill', barColor)
      .style('cursor', 'pointer')
      .on('mouseover', function(event, d) {
        d3.select(this).style('opacity', 0.8);
        
        // 创建tooltip
        const tooltip = d3.select('body').append('div')
          .attr('id', 'tooltip')
          .style('position', 'absolute')
          .style('background', 'rgba(0, 0, 0, 0.8)')
          .style('color', 'white')
          .style('padding', '8px')
          .style('border-radius', '4px')
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.html(`
          <strong>${d.city}</strong><br/>
          游客量: ${d.visitors.toFixed(2)}万人次<br/>
          增长率: ${d.growth.toFixed(2)}%
        `)
          .style('left', (event.pageX + 10) + 'px')
          .style('top', (event.pageY - 10) + 'px')
          .transition()
          .duration(200)
          .style('opacity', 1);
      })
      .on('mouseout', function() {
        d3.select(this).style('opacity', 1);
        d3.select('#tooltip').remove();
      });

    // 添加柱状图数值标签（格式化）
    container.selectAll('.bar-label')
      .data(data)
      .enter()
      .append('text')
      .attr('class', 'bar-label')
      .attr('x', d => (xScale(d.city) || 0) + xScale.bandwidth() / 2)
      .attr('y', d => yScaleLeft(d.visitors) - 5)
      .attr('text-anchor', 'middle')
      .style('font-size', '11px')
      .style('fill', '#666')
      .text(d => d.visitors.toFixed(2));

    // 创建折线生成器
    const line = d3.line<ComboChartData>()
      .x(d => (xScale(d.city) || 0) + xScale.bandwidth() / 2)
      .y(d => yScaleRight(d.growth))
      .curve(d3.curveMonotoneX);

    // 绘制折线
    container.append('path')
      .datum(data)
      .attr('class', 'line')
      .attr('d', line)
      .style('fill', 'none')
      .style('stroke', lineColor)
      .style('stroke-width', 3)
      .style('stroke-linejoin', 'round')
      .style('stroke-linecap', 'round');

    // 绘制折线上的点
    container.selectAll('.dot')
      .data(data)
      .enter()
      .append('circle')
      .attr('class', 'dot')
      .attr('cx', d => (xScale(d.city) || 0) + xScale.bandwidth() / 2)
      .attr('cy', d => yScaleRight(d.growth))
      .attr('r', 5)
      .style('fill', lineColor)
      .style('stroke', 'white')
      .style('stroke-width', 2)
      .style('cursor', 'pointer')
      .on('mouseover', function(event, d) {
        d3.select(this).attr('r', 7);
      })
      .on('mouseout', function() {
        d3.select(this).attr('r', 5);
      });

    // 添加折线数值标签（格式化）
    container.selectAll('.line-label')
      .data(data)
      .enter()
      .append('text')
      .attr('class', 'line-label')
      .attr('x', d => (xScale(d.city) || 0) + xScale.bandwidth() / 2)
      .attr('y', d => yScaleRight(d.growth) - 10)
      .attr('text-anchor', 'middle')
      .style('font-size', '11px')
      .style('fill', lineColor)
      .style('font-weight', 'bold')
      .text(d => `${d.growth.toFixed(2)}%`);

    // 绘制X轴
    container.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0, ${innerHeight})`)
      .call(xAxis)
      .selectAll('text')
      .style('font-size', '12px')
      .style('fill', '#666');

    // 绘制左Y轴
    container.append('g')
      .attr('class', 'y-axis-left')
      .call(yAxisLeft)
      .selectAll('text')
      .style('font-size', '12px')
      .style('fill', '#666');

    // 绘制右Y轴
    container.append('g')
      .attr('class', 'y-axis-right')
      .attr('transform', `translate(${innerWidth}, 0)`)
      .call(yAxisRight)
      .selectAll('text')
      .style('font-size', '12px')
      .style('fill', '#666');

    // 添加Y轴标签
    container.append('text')
      .attr('class', 'y-label-left')
      .attr('transform', 'rotate(-90)')
      .attr('y', -60)
      .attr('x', -innerHeight / 2)
      .attr('text-anchor', 'middle')
      .style('font-size', '14px')
      .style('fill', '#666')
      .text('游客量（万人次）');

    container.append('text')
      .attr('class', 'y-label-right')
      .attr('transform', 'rotate(-90)')
      .attr('y', innerWidth + 60)
      .attr('x', -innerHeight / 2)
      .attr('text-anchor', 'middle')
      .style('font-size', '14px')
      .style('fill', '#666')
      .text('增长率（%）');

    // 添加标题
    svg.append('text')
      .attr('x', width / 2)
      .attr('y', 30)
      .attr('text-anchor', 'middle')
      .style('font-size', '16px')
      .style('font-weight', 'bold')
      .style('fill', '#333')
      .text(title);

    // 添加图例
    const legend = svg.append('g')
      .attr('class', 'legend')
      .attr('transform', `translate(${width - 200}, 50)`);

    // 柱状图图例
    const barLegend = legend.append('g')
      .attr('class', 'bar-legend');

    barLegend.append('rect')
      .attr('x', 0)
      .attr('y', 0)
      .attr('width', 12)
      .attr('height', 12)
      .attr('fill', barColor);

    barLegend.append('text')
      .attr('x', 18)
      .attr('y', 9)
      .style('font-size', '12px')
      .style('fill', '#666')
      .text('游客量（万人次）');

    // 折线图图例
    const lineLegend = legend.append('g')
      .attr('class', 'line-legend')
      .attr('transform', 'translate(0, 20)');

    lineLegend.append('line')
      .attr('x1', 0)
      .attr('y1', 6)
      .attr('x2', 12)
      .attr('y2', 6)
      .style('stroke', lineColor)
      .style('stroke-width', 3);

    lineLegend.append('circle')
      .attr('cx', 6)
      .attr('cy', 6)
      .attr('r', 3)
      .style('fill', lineColor);

    lineLegend.append('text')
      .attr('x', 18)
      .attr('y', 9)
      .style('font-size', '12px')
      .style('fill', '#666')
      .text('同比增长（%）');

  }, [data, width, height, title, barColor, lineColor]);

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="border border-gray-200 rounded max-w-full h-auto"
        viewBox={`0 0 ${width} ${height}`}
      />
    </div>
  );
}

// 导出示例数据
export const exampleData = [
  { city: "杭州市", visitors: 401.7, growth: -1.0 },
  { city: "宁波市", visitors: 225.3, growth: 1.9 },
  { city: "温州市", visitors: 215.6, growth: 22.7 },
  { city: "湖州市", visitors: 115.1, growth: -0.7 },
  { city: "嘉兴市", visitors: 138.7, growth: 4.2 },
  { city: "绍兴市", visitors: 134.3, growth: 8.5 },
  { city: "金华市", visitors: 209.9, growth: 11.3 },
  { city: "衢州市", visitors: 68.2, growth: -10.9 },
  { city: "舟山市", visitors: 39.6, growth: 13.0 },
  { city: "台州市", visitors: 139.4, growth: 13.3 },
  { city: "丽水市", visitors: 60.7, growth: 13.8 }
];

// 使用示例组件
export function ComboChartExample() {
  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-bold mb-8 text-center">复杂组合图表示例</h1>
      <ComboChart 
        data={exampleData}
        width={900}
        height={600}
        title="浙江各市游客量与增长率对比"
        barColor="#4A90E2"
        lineColor="#7ED321"
        className="max-w-6xl mx-auto"
      />
    </div>
  );
}