import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // 转发到后端API
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000'
    const response = await fetch(`${backendUrl}/api/data-sources`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`后端请求失败: ${response.status}`)
    }

    const result = await response.json()
    
    // 如果后端返回成功，直接返回数据部分
    if (result.success) {
      return NextResponse.json(result.data)
    } else {
      throw new Error(result.error || '获取数据源失败')
    }

  } catch (error) {
    console.error('获取数据源失败:', error)
    return NextResponse.json({ 
      error: '获取数据源失败，请稍后重试',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
} 