import asyncio
import json
from typing import Dict, List, Any, Optional
from openai import Async<PERSON>penA<PERSON>
from .enhanced_text2sql import EnhancedText2SQLService
from config import get_settings
import os

class AIChatService:
    """AI对话服务 - 处理报告编辑中的智能对话"""
    
    def __init__(self):
        settings = get_settings()
        self.client = AsyncOpenAI(
            api_key=settings.llm.api_key,
            base_url=settings.llm.base_url
        )
        self.model_name = settings.llm.model_name
        db_conn_string = settings.database.connection_string
        self.text2sql_service = EnhancedText2SQLService(
            openai_api_key=settings.llm.api_key, 
            db_connection_string=db_conn_string
        )
        self.conversation_history = []
        
    async def process_user_message(
        self, 
        message: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        处理用户消息并返回AI响应
        
        Args:
            message: 用户输入的消息
            context: 当前编辑器上下文（模板、内容等）
            
        Returns:
            包含AI响应和建议操作的字典
        """
        
        # 分析用户意图
        intent = await self._analyze_user_intent(message, context)
        
        # 根据意图类型处理
        response = await self._handle_intent(intent, message, context)
        
        # 记录对话历史
        self.conversation_history.append({
            "user_message": message,
            "ai_response": response,
            "timestamp": self._get_current_timestamp(),
            "context": context.get("template_id", "unknown")
        })
        
        return response
    
    async def _analyze_user_intent(
        self, 
        message: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析用户意图"""
        
        system_prompt = """
        你是一个报告编辑助手，需要分析用户的意图。
        请判断用户想要执行什么操作，并返回结构化的意图信息。
        
        可能的意图类型包括：
        1. content_edit - 编辑内容
        2. data_query - 查询数据
        3. chart_create - 创建图表
        4. structure_modify - 修改结构
        5. template_question - 模板相关问题
        6. general_help - 一般帮助
        
        请返回JSON格式：
        {
            "intent_type": "类型",
            "confidence": 0.95,
            "parameters": {
                "section": "相关章节",
                "data_type": "数据类型",
                "content": "相关内容"
            },
            "requires_data": true/false,
            "suggested_action": "建议的操作"
        }
        """
        
        user_prompt = f"""
        当前上下文：
        - 模板类型：{context.get('template_type', '未知')}
        - 当前章节：{context.get('current_section', '未知')}
        - 选中内容：{context.get('selected_content', '无')}
        
        用户消息：{message}
        """
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content
            if content:
                return json.loads(content)
            return {}
            
        except Exception as e:
            # 降级处理
            return {
                "intent_type": "general_help",
                "confidence": 0.5,
                "parameters": {},
                "requires_data": False,
                "suggested_action": "provide_general_help"
            }
    
    async def _handle_intent(
        self, 
        intent: Dict[str, Any], 
        message: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """根据意图处理用户请求"""
        
        intent_type = intent.get("intent_type")
        
        if intent_type == "content_edit":
            return await self._handle_content_edit(intent, message, context)
        elif intent_type == "data_query":
            return await self._handle_data_query(intent, message, context)
        elif intent_type == "chart_create":
            return await self._handle_chart_create(intent, message, context)
        elif intent_type == "structure_modify":
            return await self._handle_structure_modify(intent, message, context)
        elif intent_type == "template_question":
            return await self._handle_template_question(intent, message, context)
        else:
            return await self._handle_general_help(intent, message, context)
    
    async def _handle_content_edit(
        self, 
        intent: Dict[str, Any], 
        message: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理内容编辑请求"""
        
        system_prompt = """
        你是一个专业的报告写作助手。用户想要编辑报告内容，请提供具体的内容建议。
        请保持专业性和准确性，内容要符合报告的风格和结构。
        """
        
        # 获取当前内容上下文
        current_content = context.get("current_content", "")
        section = intent.get("parameters", {}).get("section", "")
        
        user_prompt = f"""
        用户请求：{message}
        当前章节：{section}
        当前内容：{current_content[:1000]}...
        
        请提供具体的内容建议或修改方案。
        """
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
            )
            
            suggestion = response.choices[0].message.content
            
            return {
                "type": "content_suggestion",
                "message": "我为您提供了内容建议",
                "suggestion": suggestion,
                "action": {
                    "type": "insert_content",
                    "position": intent.get("parameters", {}).get("position"),
                    "content": suggestion
                }
            }
            
        except Exception as e:
            return {
                "type": "error",
                "message": f"抱歉，处理内容编辑时出现错误：{str(e)}"
            }
    
    async def _handle_data_query(
        self, 
        intent: Dict[str, Any], 
        message: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理数据查询请求"""
        
        try:
            # 使用Text2SQL服务生成SQL查询
            sql_result = await self.text2sql_service.natural_language_to_sql(
                query=message,
                context={
                    "table_info": context.get("available_tables", []),
                    "domain": context.get("template_type", "general")
                }
            )
            
            if sql_result.success:
                # 执行SQL查询
                data_result = await self._execute_sql_query(sql_result.sql)
                
                return {
                    "type": "data_result",
                    "message": "我为您查询到了相关数据",
                    "sql": sql_result.sql,
                    "data": data_result,
                    "action": {
                        "type": "insert_data",
                        "data_format": "table",
                        "data": data_result
                    }
                }
            else:
                return {
                    "type": "error",
                    "message": f"数据查询失败：{sql_result.error or '未知错误'}"
                }
                
        except Exception as e:
            return {
                "type": "error",
                "message": f"数据查询处理出错：{str(e)}"
            }
    
    async def _handle_chart_create(
        self, 
        intent: Dict[str, Any], 
        message: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理图表创建请求 - 简化版本，只处理基础图表"""
        
        return {
            "type": "chart_suggestion",
            "message": "图表创建功能已简化，请直接使用数据查询功能获取表格数据",
            "action": {
                "type": "suggest_data_query",
                "suggestion": "建议使用数据查询功能来获取所需的表格数据"
            }
        }
    
    async def _handle_structure_modify(
        self, 
        intent: Dict[str, Any], 
        message: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理结构修改请求"""
        
        return {
            "type": "structure_suggestion",
            "message": "我将帮您调整报告结构",
            "suggestions": [
                "添加新的章节",
                "重新组织现有内容",
                "调整标题层级"
            ],
            "action": {
                "type": "modify_structure",
                "modifications": []
            }
        }
    
    async def _handle_template_question(
        self, 
        intent: Dict[str, Any], 
        message: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理模板相关问题"""
        
        template_info = context.get("template", {})
        
        system_prompt = """
        用户询问关于报告模板的问题。请基于模板信息提供准确的回答。
        回答要简洁明了，重点突出。
        """
        
        user_prompt = f"""
        模板信息：{json.dumps(template_info, ensure_ascii=False, indent=2)}
        用户问题：{message}
        """
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
            )
            
            return {
                "type": "template_info",
                "message": response.choices[0].message.content
            }
            
        except Exception as e:
            return {
                "type": "error",
                "message": f"模板问题处理失败：{str(e)}"
            }
    
    async def _handle_general_help(
        self, 
        intent: Dict[str, Any], 
        message: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理一般帮助请求"""
        
        help_info = """
        我是您的智能报告助手，可以帮助您：
        
        📝 **内容编辑**
        - "帮我写一段关于...的内容"
        - "优化这段文字"
        - "添加结论部分"
        
        📊 **数据查询**
        - "查询最近三个月的销售数据"
        - "显示各地区旅游收入对比"
        
        🔧 **结构调整**
        - "重新组织报告结构"
        - "添加新的章节"
        
        请告诉我您想要做什么，我会尽力帮助您！
        """
        
        return {
            "type": "help_info",
            "message": help_info
        }
    
    async def analyze_chart_xml(
        self, 
        chart_xml: str, 
        context: str = ""
    ) -> Dict[str, Any]:
        """分析Excel图表XML数据"""
        
        system_prompt = """
        你是一个Excel图表XML分析专家。用户提供了Word文档中的Excel图表XML数据，
        请分析XML内容并提取图表信息。
        
        请提取以下信息：
        1. 图表类型
        2. 图表标题
        3. 数据系列信息
        4. 数据类别
        
        请返回JSON格式的分析结果。
        """
        
        user_prompt = f"""
        图表上下文：{context}
        
        Excel图表XML数据：
        {chart_xml}
        
        请分析这个图表的基本信息并返回结构化数据。
        """
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content
            if content:
                result = json.loads(content)
            else:
                result = {}
            return result
            
        except Exception as e:
            print(f"AI图表XML分析失败: {str(e)}")
            return {
                "type": "unknown",
                "title": "未知图表",
                "description": "图表XML解析失败",
                "error": str(e)
            }

    async def analyze_chart_context(
        self, 
        context: str, 
        chart_line: str = "", 
        image_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        分析图表上下文并生成Chart.js配置
        
        Args:
            context: 图表的上下文文本
            chart_line: 图表相关的行内容
            image_path: 图片路径（如果有的话）
            
        Returns:
            Chart.js配置的字典
        """
        try:
            # 如果有图片路径，使用多模态分析
            if image_path and os.path.exists(image_path):
                return await self._analyze_with_image(context, chart_line, image_path)
            else:
                # 基于文本上下文生成图表配置
                return await self._analyze_text_only(context, chart_line)
                
        except Exception as e:
            print(f"AI图表分析失败: {e}")
            return {}

    async def _analyze_with_image(
        self, 
        context: str, 
        chart_line: str, 
        image_path: str
    ) -> Dict[str, Any]:
        """使用多模态模型分析图表图片"""
        
        # 将图片编码为base64
        import base64
        with open(image_path, "rb") as img_file:
            img_data = base64.b64encode(img_file.read()).decode('utf-8')
        
        system_prompt = """
        你是一个专业的图表分析专家。请分析提供的图表图片，并生成对应的Chart.js配置。
        
        请返回JSON格式的Chart.js配置，包括：
        1. 图表类型 (type: 'bar', 'line', 'pie', 'doughnut'等)
        2. 数据 (data)
        3. 配置选项 (options)
        
        确保配置是完整且可用的Chart.js格式。
        """
        
        user_prompt = f"""
        图表上下文：{context}
        图表说明：{chart_line}
        
        请分析图片中的图表，生成对应的Chart.js配置。
        """
        
        try:
            # 构建多模态消息
            messages = [
                {"role": "system", "content": system_prompt},
                {
                    "role": "user", 
                    "content": [
                        {"type": "text", "text": user_prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{img_data}"
                            }
                        }
                    ]
                }
            ]
            
            response = await self.client.chat.completions.create(
                model="gpt-4-vision-preview",  # 使用支持视觉的模型
                messages=messages,
                response_format={"type": "json_object"},
                max_tokens=2000
            )
            
            content = response.choices[0].message.content
            if content:
                return json.loads(content)
            return {}
            
        except Exception as e:
            print(f"多模态图表分析失败: {e}")
            # 降级到文本分析
            return await self._analyze_text_only(context, chart_line)

    async def _analyze_text_only(
        self, 
        context: str, 
        chart_line: str
    ) -> Dict[str, Any]:
        """基于文本上下文生成图表配置"""
        
        system_prompt = """
        你是一个数据可视化专家。基于提供的文本上下文，生成合适的Chart.js图表配置。
        
        请返回JSON格式的Chart.js配置，包括：
        1. 合适的图表类型
        2. 模拟的数据结构
        3. 完整的配置选项
        
        图表应该与上下文内容相匹配。
        """
        
        user_prompt = f"""
        上下文内容：{context}
        图表信息：{chart_line}
        
        请基于这些信息生成一个合适的Chart.js配置。
        """
        
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content
            if content:
                return json.loads(content)
            return {}
            
        except Exception as e:
            print(f"文本图表分析失败: {e}")
            return {}

    async def _execute_sql_query(self, sql: str) -> List[Dict[str, Any]]:
        """执行SQL查询 - 模拟实现"""
        print(f"Executing SQL: {sql}")
        return [{"column1": "data1", "column2": 123}, {"column1": "data2", "column2": 456}]

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        import datetime
        return datetime.datetime.now().isoformat()