'use client'

import React from 'react'
import { 
  <PERSON><PERSON>ar<PERSON>hart, 
  <PERSON>Pie<PERSON>hart, 
  Combo<PERSON>hart, 
  NestedPieChart 
} from '@/components/chart'

interface ChartComponentProps {
  config: {
    type: string
    title?: string
    data?: any
    datasets?: any[]
    categories?: string[]
    options?: any
  }
  className?: string
}

export default function ChartComponent({ config, className }: ChartComponentProps) {
  const { type, title, data, datasets, categories, options } = config

  // 渲染对应的D3组件
  const renderChart = () => {
    switch (type) {
      case 'bar':
        // 转换为D3格式
        const barData = categories?.map((label: string, index: number) => ({
          name: label,
          value: datasets?.[0]?.data?.[index] || 0
        })) || []
        
        return (
          <SimpleBarChart
            data={barData}
            width={600}
            height={300}
            title={options?.title || '柱状图'}
            barColor="#4A90E2"
            className="w-full"
          />
        )

      case 'simple-pie':
      case 'pie':
        // 直接使用数据集
        const pieData = datasets?.map((item: any) => ({
          name: item.name,
          value: item.value,
          color: item.color
        })) || []
        
        return (
          <SimplePieChart
            data={pieData}
            width={400}
            height={300}
            title={options?.title || '饼图'}
            className="w-full"
          />
        )

      case 'combo':
        // 使用组合图表
        const comboData = categories?.map((category: string, index: number) => {
          const barDataset = datasets?.find(d => d.type === 'bar');
          const lineDataset = datasets?.find(d => d.type === 'line');
          
          return {
            city: category,
            visitors: barDataset?.data?.[index] || 0,
            growth: lineDataset?.data?.[index] || 0
          };
        }) || []
        
        return (
          <ComboChart
            data={comboData}
            width={800}
            height={400}
            title={title || '组合图表'}
            barColor="#4A90E2"
            lineColor="#7ED321"
            className="w-full"
          />
        )

      case 'nested-pie':
        // 嵌套饼图
        if (data && data.outer && data.inner) {
          return (
            <NestedPieChart
              data={{
                outer: data.outer.map((item: any) => ({
                  name: item.name,
                  value: item.value,
                  color: item.color || "#4A90E2"
                })),
                inner: data.inner.map((item: any) => ({
                  name: item.name,
                  value: item.value,
                  color: item.color || "#7ED321",
                  parent: item.parent
                }))
              }}
              width={500}
              height={400}
              title={title || '嵌套饼图'}
              className="w-full"
            />
          )
        }
        
        // 默认显示简单饼图
        return (
          <SimplePieChart
            data={[
              { name: "数据1", value: 30, color: "#4A90E2" },
              { name: "数据2", value: 70, color: "#7ED321" }
            ]}
            width={400}
            height={300}
            title="默认饼图"
            className="w-full"
          />
        )

      default:
        // 默认显示柱状图
        return (
          <SimpleBarChart
            data={[
              { name: "数据1", value: 30 },
              { name: "数据2", value: 50 },
              { name: "数据3", value: 20 }
            ]}
            width={600}
            height={300}
            title="默认图表"
            barColor="#4A90E2"
            className="w-full"
          />
        )
    }
  }

  return (
    <div className={`w-full h-auto ${className || ''}`}>
      {renderChart()}
    </div>
  )
} 